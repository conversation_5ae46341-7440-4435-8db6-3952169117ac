spring.application.name=train-service

#spring.datasource.url=*************************************************************
#spring.datasource.url=********************************************************
#spring.datasource.url=*******************************************************
spring.datasource.url=********************************************************

#spring.datasource.username=cmudevuser
#spring.datasource.password=dreamtech%9
#spring.datasource.username=cmudevuser
#spring.datasource.password=DreamtechIT%9
spring.datasource.username=root
spring.datasource.password=dreamtech%9

app.secretKey.sm4=e83d7a1c9b046f25d2c5e789a0b4f67d

spring.datasource.tomcat.test-while-idle=true
spring.datasource.tomcat.test-on-borrow=true
spring.datasource.tomcat.time-between-eviction-runs-millis=5000
spring.datasource.tomcat.min-evictable-idle-time-millis=60000
spring.datasource.tomcat.driver-class-name=com.mysql.jdbc.Driver
spring.datasource.tomcat.validation-query=SELECT 1


spring.jooq.sql-dialect = mysql
logging.level.org.jooq=DEBUG

dubbo.application.name=train-service

#dubbo.registry.address=zookeeper://mw9.zhixueyun.com:10501
#dubbo.registry.address=zookeeper://mw9.zhixueyun.com:10501
#dubbo.registry.address=zookeeper://**************:30002
dubbo.registry.address=zookeeper://127.0.0.1:2181
#dubbo.registry.address=zookeeper://***************:2181
#dubbo.registry.address=zookeeper://mw9.zhixueyun.com:10501
dubbo.application.version=1
dubbo.registry.username=zk_user
dubbo.registry.password=dreamtech
dubbo.registry.client=curator


spring.rabbitmq.host=*************
spring.rabbitmq.port=30419
spring.rabbitmq.username=guest

spring.rabbitmq.password=guest
spring.rabbitmq.virtual-host=/
spring.rabbitmq.default-exchange=amq.direct

spring.data.mongodb.host = *************
spring.data.mongodb.port = 27017
spring.data.mongodb.dbname = cmu_homecfg
spring.data.mongodb.connectTimeout = 60000
spring.data.mongodb.socketTimeout = 120000
spring.data.mongodb.socketKeepAlive = true
spring.data.mongodb.maxConnectionIdleTime = 120000
spring.data.mongodb.maxConnectionLifeTime = 120000
spring.data.mongodb.username = admin
spring.data.mongodb.password = asd123

graphite.server=**************
graphite.port=30004

dubbo.protocol.port=20890

org.releated.tables=t_project,f_organization_id,false,60001|\
t_group_configuration_value,f_organization_id,false,60001|\
t_level,f_organization_id,false,60001


zyx.desensitize.accounts=admin
smart.campus.public.key = MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAipwtzs6sDtCn8VVVbr6t2HQVTeJS1Qi4mgq9VS8sk8e+QU+pOhgRrP/HQXiTcZFAb15u/kWxnMpis+M5MpQ/UWPpiW/2YW+HtxaZQNNx6c8toFUjRveSjN3yjh3kUXYI4+dLulKHRryZlV9bGwBKoxuHY7PXege4XWLK8pcG19MnKo3A5uvo6t6gRnvCNE2ej/bwbWL+zved8bDKX4+Jxh4JLPzksvejIfhn3ZfOj0AZcmiNkNp31WMXGT/UXIHGBSZLfsdwzbTmOW8oZwfdw2YUuAyKPoRGszQVnMsbrT3izLoj+Blc/AYs3wl0FYWl/dcl3J0yw7BzjrAiJODPrwIDAQAB

smart.campus.domain = http://*************:1667/

smart.campus.domain = http://***********:9740/

smart.campus.domain = http://***********:9740/

smart.campus.domain = http://***********:9740/

smart.campus.domain = http://***********:9740/

smart.campus.domain = http://***********:9740/