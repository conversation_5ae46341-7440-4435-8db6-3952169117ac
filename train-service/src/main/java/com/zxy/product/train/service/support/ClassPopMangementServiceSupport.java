package com.zxy.product.train.service.support;

import com.zxy.common.dao.support.CommonDao;
import com.zxy.product.train.api.ClassPopMangementService;
import com.zxy.product.train.entity.ClassPopMangement;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Optional;

import static com.zxy.product.train.jooq.Tables.CLASS_POP_MANGEMENT;

/**
 * @Auther: xxh
 * @Date: 2025/8/28 - 08 - 28 - 18:18
 * @Description: com.zxy.product.train.service.support
 * @version: 1.0
 */
@Service
public class ClassPopMangementServiceSupport implements ClassPopMangementService {


    private CommonDao<ClassPopMangement> classPopMangementCommonDao;

    @Autowired
    public void setClassPopMangementCommonDao(CommonDao<ClassPopMangement> classPopMangementCommonDao) {
        this.classPopMangementCommonDao = classPopMangementCommonDao;
    }


    @Override
    public ClassPopMangement add(ClassPopMangement mangement){
       return classPopMangementCommonDao.insert(mangement);
    }

    @Override
    public Optional<ClassPopMangement> getFlag(String classId, String memberId){
        return classPopMangementCommonDao.execute(e->e.select(CLASS_POP_MANGEMENT.CLASS_ID,CLASS_POP_MANGEMENT.ID,CLASS_POP_MANGEMENT.FLAG).from(CLASS_POP_MANGEMENT)
                .where(CLASS_POP_MANGEMENT.CLASS_ID.eq(classId).and(CLASS_POP_MANGEMENT.MEMBER_ID.eq(memberId)))
                .fetchOptionalInto(ClassPopMangement.class));
    }


}
