package com.zxy.product.train.service.support;

import com.zxy.common.base.helper.PagedResult;
import com.zxy.common.dao.Fields;
import com.zxy.common.dao.support.CommonDao;
import com.zxy.common.message.provider.MessageSender;
import com.zxy.product.train.api.ClassInfoService;
import com.zxy.product.train.api.ClassOfflineCourseService;
import com.zxy.product.train.api.QuestionnaireSurveyService;
import com.zxy.product.train.content.MessageHeaderContent;
import com.zxy.product.train.content.MessageTypeContent;
import com.zxy.product.train.dto.ResearchAnswerRecordMap;
import com.zxy.product.train.entity.*;
import com.zxy.product.train.util.DesensitizationUtil;
import com.zxy.product.train.util.EncryptUtil;
import org.jooq.*;
import org.jooq.impl.DSL;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.zxy.product.train.jooq.Tables.*;
import static com.zxy.product.train.jooq.tables.Member.MEMBER;

/**
 * <AUTHOR> 统计与评估调查问卷接口实现类
 */
@Service
public class QuestionnaireSurveyServiceSupport implements QuestionnaireSurveyService {

  private CommonDao<ClassEvaluate> classEvaluateDao;
  private CommonDao<ResearchQuestionary> researchQuestionaryDao;
  private CommonDao<ResearchAnswerRecord> researchAnswerRecordDao;
  private CommonDao<ResearchRecord> researchRecordDao;
  private CommonDao<ClassInfo> classInfoDao;
  private CommonDao<QuestionAttr> questionAttrDao;
  private ClassOfflineCourseService offlineCourseService;
  private CommonDao<Dimension> dimensionDao;
  private CommonDao<DimensionQuestion> dimensionQuestionDao;
  private CommonDao<Question> questionDao;
  private CommonDao<Member> memberDao;
  private CommonDao<Organization> orgDao;
  private MessageSender sender;
  private ClassInfoService classInfoService;
  private CommonDao<Trainee> traineeDao;
  private MessageSender messageSender;
  private CommonDao<Project> projectDao;
  private CommonDao<GroupConfigurationValue> groupConfigurationValueDao;
  private CommonDao<ClassStatistics> classStatisticsDao;

  private static Logger logger = LoggerFactory.getLogger( QuestionnaireSurveyServiceSupport.class);

  @Autowired
  public void setClassStatisticsDao(CommonDao<ClassStatistics> classStatisticsDao) {
    this.classStatisticsDao = classStatisticsDao;
  }

  @Autowired
  public void setGroupConfigurationValueDao(
      CommonDao<GroupConfigurationValue> groupConfigurationValueDao) {
    this.groupConfigurationValueDao = groupConfigurationValueDao;
  }

  @Autowired
  public void setOrgDao(CommonDao<Organization> orgDao) {
    this.orgDao = orgDao;
  }

  @Autowired
  public void setMessageSender(MessageSender messageSender) {
    this.messageSender = messageSender;
  }

  @Autowired
  public void setTraineeDao(CommonDao<Trainee> traineeDao) {
    this.traineeDao = traineeDao;
  }

  @Autowired
  public void setClassInfoService(ClassInfoService classInfoService) {
    this.classInfoService = classInfoService;
  }

  @Autowired
  public void setSender(MessageSender sender) {
    this.sender = sender;
  }

  @Autowired
  public void setMemberDao(CommonDao<Member> memberDao) {
    this.memberDao = memberDao;
  }

  @Autowired
  public void setDimensionDao(CommonDao<Dimension> dimensionDao) {
    this.dimensionDao = dimensionDao;
  }

  @Autowired
  public void setDimensionQuestionDao(CommonDao<DimensionQuestion> dimensionQuestionDao) {
    this.dimensionQuestionDao = dimensionQuestionDao;
  }

  @Autowired
  public void setQuestionDao(CommonDao<Question> questionDao) {
    this.questionDao = questionDao;
  }

  @Autowired
  public void setOfflineCourseService(ClassOfflineCourseService offlineCourseService) {
    this.offlineCourseService = offlineCourseService;
  }

  @Autowired
  public void setQuestionAttrDao(CommonDao<QuestionAttr> questionAttrDao) {
    this.questionAttrDao = questionAttrDao;
  }

  @Autowired
  public void setClassInfoDao(CommonDao<ClassInfo> classInfoDao) {
    this.classInfoDao = classInfoDao;
  }

  @Autowired
  public void setClassEvaluateDao(CommonDao<ClassEvaluate> classEvaluateDao) {
    this.classEvaluateDao = classEvaluateDao;
  }

  @Autowired
  public void setResearchQuestionaryDao(CommonDao<ResearchQuestionary> researchQuestionaryDao) {
    this.researchQuestionaryDao = researchQuestionaryDao;
  }

  @Autowired
  public void setResearchAnswerRecordDao(CommonDao<ResearchAnswerRecord> researchAnswerRecordDao) {
    this.researchAnswerRecordDao = researchAnswerRecordDao;
  }

  @Autowired
  public void setResearchRecordDao(CommonDao<ResearchRecord> researchRecordDao) {
    this.researchRecordDao = researchRecordDao;
  }

  @Autowired
  public void setProjectDao(CommonDao<Project> projectDao) {
    this.projectDao = projectDao;
  }

  @Override
  public List<ClassEvaluate> findEvaluateRecorde(String classId, String memberId) {
    return classEvaluateDao
        .execute(x -> x
            .selectDistinct(
                Fields.start().add(CLASS_EVALUATE).add(CLASS_BUSINESS_PROGRESS.FINISH_STATUS)
                    .add(CLASS_BUSINESS_PROGRESS.SCORE).end())
            .from(CLASS_EVALUATE)
            .leftJoin(CLASS_BUSINESS_PROGRESS)
            .on(CLASS_BUSINESS_PROGRESS.CLASS_BUSINESS_ID.eq(CLASS_EVALUATE.ID)
                .and(CLASS_BUSINESS_PROGRESS.CLASS_ID.eq(classId))
                .and(CLASS_BUSINESS_PROGRESS.MEMBER_ID.eq(memberId)))
            .where(CLASS_EVALUATE.DELETE_FLAG.eq(0).and(CLASS_EVALUATE.CLASS_ID.eq(classId)))
            .and(CLASS_EVALUATE.TYPE.ne(4))
            .and(CLASS_EVALUATE.TYPE.ne(5))
            .and(CLASS_EVALUATE.TYPE.ne(6))
            .and(CLASS_EVALUATE.TYPE.ne(7)))
        .fetch(r -> {
          ClassEvaluate c = r.into(ClassEvaluate.class);
          c.setEvaluateStatus(r.getValue(CLASS_BUSINESS_PROGRESS.FINISH_STATUS));
          c.setScore(r.getValue(CLASS_BUSINESS_PROGRESS.SCORE));
          return c;
        });
  }

  @Override
  public PagedResult<ResearchRecord> findResearchRecord(int page, int pageSize,
      Optional<String> classId,
      Optional<String> resourceId, Optional<String> name, Optional<String> fullName,
      Optional<Long> startTime) {
    // TODO Auto-generated method stub
    Field<String> mId = MEMBER.ID.as("mId");
    Field<String> zhanghao = MEMBER.NAME.as("zhanghao");
    Field<String> oName = ORGANIZATION.NAME.as("oName");
    Field<String> tmId = TRAINEE.MEMBER_ID.as("tmId");
    Field<String> tcId = TRAINEE.CLASS_ID.as("tcId");
    Field<Long> rStartTime = RESEARCH_QUESTIONARY.START_TIME.as("rStartTime");
    // 构建查询条件
    SelectOnConditionStep<Record> step = researchRecordDao.execute(x -> x
        .selectDistinct(Fields.start().add(RESEARCH_RECORD).add(mId).add(tmId).add(tcId)
            .add(rStartTime).add(zhanghao).add(oName).add(MEMBER.FULL_NAME).end())
        .from(RESEARCH_RECORD).leftJoin(MEMBER).on(MEMBER.ID.eq(RESEARCH_RECORD.MEMBER_ID))
        .leftJoin(TRAINEE)
        .on(TRAINEE.MEMBER_ID.eq(MEMBER.ID))
        .leftJoin(RESEARCH_QUESTIONARY)
        .on(RESEARCH_QUESTIONARY.ID.eq(RESEARCH_RECORD.RESEARCH_QUESTIONARY_ID)
            .and(TRAINEE.CLASS_ID.eq(RESEARCH_QUESTIONARY.CLASS_ID)))
        .leftJoin(ORGANIZATION).on(ORGANIZATION.ID.eq(MEMBER.ORGANIZATION_ID)));
    // 组合条件
    Stream<Optional<Condition>> conditions = Stream.of(name.map(MEMBER.NAME::eq),
        resourceId.map(RESEARCH_QUESTIONARY.ID::eq), fullName.map(MEMBER.FULL_NAME::contains),
        startTime.map(RESEARCH_QUESTIONARY.START_TIME::gt),
        classId.map(RESEARCH_QUESTIONARY.CLASS_ID::eq));
    // 过滤条件
    Condition condition = conditions.filter(Optional::isPresent).map(Optional::get)
        .reduce((acc, item) -> acc.and(item)).orElse(DSL.trueCondition())
        .and(TRAINEE.DELETE_FLAG.eq(0))
        .and(TRAINEE.AUDIT_STATUS.eq(1)).and(TRAINEE.TYPE.eq(0));
    // 查询分页总条数
    int count = researchRecordDao.execute(x -> x.fetchCount(step.where(condition)));
    // 构建分页limit条件查询
    int firstResult = (page - 1) * pageSize;
    List<ResearchRecord> list = step.limit(firstResult, pageSize).fetch(r -> {
      ResearchRecord t = r.into(RESEARCH_RECORD).into(ResearchRecord.class);
      Member m = new Member();
      m.setId(r.getValue(mId));
      m.setName(r.getValue(zhanghao));
      m.setFullName(r.getValue(MEMBER.FULL_NAME));
      Organization o = new Organization();
      o.setName(r.getValue(oName));
      t.setsTime(r.getValue(rStartTime));
      t.setMember(m);
      t.setOrganization(o);
      return t;
    });
    return PagedResult.create(count, list);
  }

  @Override
  public PagedResult<ResearchAnswerRecord> findResearchAnswerRecord(int page, int pageSize,
      Optional<String> classId,
      Optional<String> resourceId, Optional<String> name, Optional<String> fullName,
      Optional<Long> startTime) {
    // TODO Auto-generated method stub
    Field<String> mId = MEMBER.ID.as("mId");
    Field<String> rId = RESEARCH_RECORD.ID.as("rId");
    Field<String> zhanghao = MEMBER.NAME.as("zhanghao");
    Field<String> oName = ORGANIZATION.NAME.as("oName");
    Field<String> tmId = TRAINEE.MEMBER_ID.as("tmId");
    Field<String> tcId = TRAINEE.CLASS_ID.as("tcId");
    // 构建查询条件
    SelectOnConditionStep<Record> step = researchAnswerRecordDao.execute(x -> x
        .selectDistinct(Fields.start()
            .add(tmId, tcId, rId, RESEARCH_RECORD.RESEARCH_QUESTIONARY_ID, mId,
                RESEARCH_QUESTIONARY.START_TIME, RESEARCH_RECORD.SUBMIT_TIME, zhanghao, oName,
                MEMBER.FULL_NAME, RESEARCH_ANSWER_RECORD.SCORE.sum())
            .end())
        .from(RESEARCH_RECORD)
        .leftJoin(MEMBER).on(MEMBER.ID.eq(RESEARCH_RECORD.MEMBER_ID)).leftJoin(TRAINEE)
        .on(TRAINEE.MEMBER_ID.eq(MEMBER.ID))
        .leftJoin(RESEARCH_ANSWER_RECORD)
        .on(RESEARCH_RECORD.ID.eq(RESEARCH_ANSWER_RECORD.RESEARCH_RECORD_ID))
        .leftJoin(RESEARCH_QUESTIONARY)
        .on(RESEARCH_QUESTIONARY.ID.eq(RESEARCH_RECORD.RESEARCH_QUESTIONARY_ID)
            .and(TRAINEE.CLASS_ID.eq(RESEARCH_QUESTIONARY.CLASS_ID)))
        .leftJoin(ORGANIZATION).on(ORGANIZATION.ID.eq(MEMBER.ORGANIZATION_ID)));
    // 组合条件
    Stream<Optional<Condition>> conditions = Stream.of(name.map(MEMBER.NAME::eq),
        resourceId.map(RESEARCH_QUESTIONARY.ID::eq), fullName.map(MEMBER.FULL_NAME::contains),
        startTime.map(RESEARCH_QUESTIONARY.START_TIME::gt),
        classId.map(RESEARCH_QUESTIONARY.CLASS_ID::eq));
    // 过滤条件
    Condition condition = conditions.filter(Optional::isPresent).map(Optional::get)
        .reduce((acc, item) -> acc.and(item)).orElse(DSL.trueCondition())
        .and(TRAINEE.DELETE_FLAG.eq(0))
        .and(TRAINEE.AUDIT_STATUS.eq(1)).and(TRAINEE.TYPE.eq(0)).and(RESEARCH_RECORD.STATUS.eq(1));
    // 查询分页总条数
    int count = researchAnswerRecordDao
        .execute(x -> x.fetchCount(step.where(condition).groupBy(tmId, tcId, rId,
            RESEARCH_RECORD.RESEARCH_QUESTIONARY_ID, mId, RESEARCH_QUESTIONARY.START_TIME,
            RESEARCH_RECORD.SUBMIT_TIME, zhanghao, oName, MEMBER.FULL_NAME)));
    // 构建分页limit条件查询
    int firstResult = (page - 1) * pageSize;
    List<ResearchAnswerRecord> list = step.limit(firstResult, pageSize).fetch(r -> {
      ResearchAnswerRecord t = new ResearchAnswerRecord();
      BigDecimal value = r.getValue(RESEARCH_ANSWER_RECORD.SCORE.sum());
      if (value != null) {
        t.setSumScore(Integer.valueOf(value.toString()));
      }
      ResearchRecord researchRecord = new ResearchRecord();
      researchRecord.setResearchQuestionaryId(r.getValue(RESEARCH_RECORD.RESEARCH_QUESTIONARY_ID));
      Member m = new Member();
      m.setId(r.getValue(mId));
      m.setName(DesensitizationUtil.desensitizeEmployeeId(r.getValue(zhanghao)));
      m.setFullName(EncryptUtil.aesEncrypt(r.getValue(MEMBER.FULL_NAME), null));
      Organization o = new Organization();
      o.setName(r.getValue(oName));
      t.setsTime(r.getValue(RESEARCH_QUESTIONARY.START_TIME));
      t.setSuTime(r.getValue(RESEARCH_RECORD.SUBMIT_TIME));
      t.setrId(r.getValue(rId));
      t.setMember(m);
      t.setOrganization(o);
      return t;
    });
    return PagedResult.create(count, list);
  }

  @Override
  public PagedResult<ResearchAnswerRecord> findLeaderQuestion(int page, int pageSize,
      String resourceId, Optional<String> name, Optional<String> fullName, Optional<String> classId,
      Optional<Integer> type) {

    Field<String> mId = MEMBER.ID.as("mId");
    Field<String> rId = RESEARCH_RECORD.ID.as("rId");
    Field<String> zhanghao = MEMBER.NAME.as("zhanghao");
    Field<Long> rStartTime = RESEARCH_QUESTIONARY.START_TIME.as("rStartTime");
    SelectOnConditionStep<Record> step = researchQuestionaryDao
        .execute(x -> x.select(Fields.start().
            add(RESEARCH_RECORD.RESEARCH_QUESTIONARY_ID, mId, rId, TRAINEE.NEW_COMPANY,
                rStartTime, RESEARCH_RECORD.SUBMIT_TIME, zhanghao,
                MEMBER.FULL_NAME).end()).from(RESEARCH_QUESTIONARY)
            .leftJoin(RESEARCH_RECORD)
            .on(RESEARCH_QUESTIONARY.ID.eq(RESEARCH_RECORD.RESEARCH_QUESTIONARY_ID))
            .leftJoin(MEMBER).on(MEMBER.ID.eq(RESEARCH_QUESTIONARY.MEMBER_ID))
            .innerJoin(TRAINEE).on(MEMBER.ID.eq(TRAINEE.AUDIT_LEADERSHIP)
                .and(TRAINEE.COMMIT_SUPERIOR_LEADERSHIP.eq(1))
                .and(TRAINEE.CLASS_ID.eq(classId.get())))
        );
    Stream<Optional<Condition>> conditions = Stream.of(name.map(MEMBER.NAME::contains),
        fullName.map(MEMBER.FULL_NAME::contains),
        classId.map(RESEARCH_QUESTIONARY.CLASS_ID::eq));
    Condition condition = conditions.filter(Optional::isPresent).map(Optional::get)
        .reduce((acc, item) -> acc.and(item)).orElse(DSL.trueCondition())
        .and(RESEARCH_RECORD.STATUS.eq(1))
        .and(RESEARCH_QUESTIONARY.TYPE.eq(7));
    int count = researchAnswerRecordDao
        .execute(x -> x.fetchCount(step.where(condition).groupBy(rId)));
    int firstResult = (page - 1) * pageSize;
    List<ResearchAnswerRecord> list = step.limit(firstResult, pageSize).fetch(r -> {
      ResearchAnswerRecord t = new ResearchAnswerRecord();
      Member m = new Member();
      m.setId(r.getValue(mId));
      m.setName(DesensitizationUtil.desensitizeEmployeeId(r.getValue(zhanghao)));
      m.setFullName(EncryptUtil.aesEncrypt(r.getValue(MEMBER.FULL_NAME), null));
      Organization o = new Organization();
      o.setName(r.getValue(TRAINEE.NEW_COMPANY));
      t.setsTime(r.getValue(rStartTime));
      t.setSuTime(r.getValue(RESEARCH_RECORD.SUBMIT_TIME));
      t.setrId(r.getValue(rId));
      t.setMember(m);
      t.setOrganization(o);
      return t;
    });
//		Map<String, Organization> orgMap = orgDao.execute(x -> x.select(ORGANIZATION.NAME).from(ORGANIZATION).where(ORGANIZATION.ID.in(orgIds)))
//				.fetch(r -> r.into(Organization.class)).stream().collect(Collectors.toMap(Organization::getId, r -> r));
//		list.forEach(r -> {
//			Organization mb = r.getOrganization();
//			mb.setName(orgMap.get(mb.getCompanyId()).getName());
//			String path = mb.getPath();
////			String cName = mb.getName();
//			if (glist != null && glist.size() > 0) {
//				for (int i = 0; i < glist.size(); i++) {
//					if (path!=null&&path.contains(glist.get(i).getPath())) {
//						mb.setName(glist.get(i).getShortName());
//						break;
//					} else {
//						mb.setName(cName);
//					}
//				}
//			} else {
//				mb.setName(cName);
//			}
//		});
    return PagedResult.create(count, list);
  }

  @Override
  public PagedResult<ResearchAnswerRecord> findResearch(int page, int pageSize, String resourceId,
      Optional<String> name, Optional<String> fullName, Optional<String> classId,
      Optional<Integer> type, Optional<Integer> status) {
    // TODO Auto-generated method stub

//		List<GroupConfigurationValue> glist = groupConfigurationValueDao.execute(e -> {
//			List<GroupConfigurationValue> all = e.selectFrom(GROUP_CONFIGURATION_VALUE)
//					.fetch()
//					.into(GroupConfigurationValue.class);
//			return all;
//		});

    Field<String> mId = MEMBER.ID.as("mId");
    Field<String> rId = RESEARCH_RECORD.ID.as("rId");
    Field<String> zhanghao = MEMBER.NAME.as("zhanghao");
    Field<String> tmId =
        (type.isPresent() && type.get().equals(7)) ? TRAINEE.AUDIT_LEADERSHIP.as("tmId")
            : TRAINEE.MEMBER_ID.as("tmId");
    Field<String> tcId = TRAINEE.CLASS_ID.as("tcId");
    Field<String> teId = TRAINEE.ID.as("teId");
    Field<Long> rStartTime = RESEARCH_QUESTIONARY.START_TIME.as("rStartTime");
    Field<Integer> tCommitQues = TRAINEE.COMMIT_QUESTIONARY.as("tCommitQues");
    Field<Integer> rType = RESEARCH_QUESTIONARY.TYPE.as("rType");
    Condition statusField =
        status.isPresent() ? TRAINEE.COMMIT_QUESTIONARY.eq(status.get()) : DSL.trueCondition();
//		com.zxy.product.train.jooq.tables.Organization org2 = ORGANIZATION.as("org2");
//		Field<String> org2Name = org2.NAME.as("org2Name");
    SelectOnConditionStep<Record> step;
    // 构建查询条件
    String onSql;
    onSql = "`train`.`t_member`.f_id = `train`.`t_trainee`.`f_member_id`";
    step = researchRecordDao.execute(x -> x
        .select(Fields.start()
            .add(teId, tmId, tcId, rId, RESEARCH_RECORD.RESEARCH_QUESTIONARY_ID, mId,
                rStartTime, RESEARCH_RECORD.SUBMIT_TIME, zhanghao, tCommitQues, rType,
                TRAINEE.NEW_COMPANY, MEMBER.FULL_NAME, RESEARCH_ANSWER_RECORD.SCORE.sum())
            .end())
        .from(TRAINEE)
        .leftJoin(MEMBER).on(onSql)
        .leftJoin(RESEARCH_RECORD).on(RESEARCH_RECORD.MEMBER_ID.eq(MEMBER.ID)
            .and(RESEARCH_RECORD.RESEARCH_QUESTIONARY_ID.eq(resourceId)))
        .leftJoin(RESEARCH_ANSWER_RECORD)
        .on(RESEARCH_RECORD.ID.eq(RESEARCH_ANSWER_RECORD.RESEARCH_RECORD_ID))
        .leftJoin(RESEARCH_QUESTIONARY)
        .on(RESEARCH_QUESTIONARY.ID.eq(RESEARCH_RECORD.RESEARCH_QUESTIONARY_ID)
            .and(TRAINEE.CLASS_ID.eq(RESEARCH_QUESTIONARY.CLASS_ID)))
    );
    // 组合条件
    Stream<Optional<Condition>> conditions = Stream.of(name.map(MEMBER.NAME::contains),
        fullName.map(MEMBER.FULL_NAME::contains),
        classId.map(TRAINEE.CLASS_ID::eq),
           Optional.ofNullable(RESEARCH_QUESTIONARY.IS_ENSEMBLE.eq(1)));
    // 过滤条件
    Condition condition = conditions.filter(Optional::isPresent).map(Optional::get)
        .reduce((acc, item) -> acc.and(item)).orElse(DSL.trueCondition())
        .and(TRAINEE.DELETE_FLAG.eq(0))
        .and(TRAINEE.AUDIT_STATUS.eq(1)).and(TRAINEE.TYPE.eq(0)).and(statusField);
    // 查询分页总条数
    int count = researchAnswerRecordDao
        .execute(x -> x.fetchCount(step.where(condition).groupBy(zhanghao,
            MEMBER.FULL_NAME)));
    // 构建分页limit条件查询
    int firstResult = (page - 1) * pageSize;
    List<ResearchAnswerRecord> list = step.limit(firstResult, pageSize).fetch(r -> {
      ResearchAnswerRecord t = new ResearchAnswerRecord();
      BigDecimal value = r.getValue(RESEARCH_ANSWER_RECORD.SCORE.sum());
      Integer rtype = r.getValue(rType);
      if (value != null) {
        t.setSumScore(Integer.valueOf(value.toString()));
      } else if (ResearchQuestionary.TYPE_NEW_SATISFACTION_QUESTIONARY == rtype
          && Trainee.YES_COMMIT_QUESTIONARY == r.getValue(tCommitQues)) {
        t.setSumScore(1);
      }
      ResearchRecord researchRecord = new ResearchRecord();
      researchRecord.setResearchQuestionaryId(r.getValue(RESEARCH_RECORD.RESEARCH_QUESTIONARY_ID));
      Member m = new Member();
      m.setId(r.getValue(mId));
      m.setName(DesensitizationUtil.desensitizeEmployeeId(r.getValue(zhanghao)));
      m.setFullName(EncryptUtil.aesEncrypt(r.getValue(MEMBER.FULL_NAME), null));
      Organization o = new Organization();
      o.setName(r.getValue(TRAINEE.NEW_COMPANY));
      t.setsTime(r.getValue(rStartTime));
      t.setSuTime(r.getValue(RESEARCH_RECORD.SUBMIT_TIME));
      t.setrId(r.getValue(rId));
      t.setMember(m);
      t.setStatus(r.getValue(tCommitQues));
      t.setOrganization(o);
      return t;
    });

//		list.forEach(r -> {
//			Organization mb = r.getOrganization();
//			String path = mb.getPath();
//			String cName = mb.getName();
//			if (glist != null && glist.size() > 0) {
//				for (int i = 0; i < glist.size(); i++) {
//					if (path!=null&&path.contains(glist.get(i).getPath())) {
//						mb.setName(glist.get(i).getShortName());
//						break;
//					} else {
//						mb.setName(cName);
//					}
//				}
//			} else {
//				mb.setName(cName);
//			}
//		});
    return PagedResult.create(count, list);
  }

  @Override
  public PagedResult<ResearchAnswerRecord> findResearchT(int page, int pageSize, String resourceId,
      Optional<String> name, Optional<String> fullName, Optional<String> classId) {
//		List<String> orgIds = new ArrayList<>();
    boolean flag=existPartyCadreClass(classId.get());
    Field<String> rId = RESEARCH_RECORD.ID.as("rId");
    Field<String> zhanghao = MEMBER.NAME.as("zhanghao");
    Field<Integer> questionType = RESEARCH_QUESTIONARY.TYPE.as("questionType");
    Field<Integer> tCommitQues = TRAINEE.COMMIT_QUESTIONARY.as("tCommitQues");

    com.zxy.product.train.jooq.tables.Organization org2 = ORGANIZATION.as("org2");

    // 构建查询条件
    SelectOnConditionStep<Record> step = researchRecordDao.execute(x -> x
        .select(Fields.start()
            .add(rId, zhanghao, TRAINEE.ORGANIZATION_ID, TRAINEE.NEW_COMPANY, tCommitQues,
                questionType,
                MEMBER.FULL_NAME, RESEARCH_ANSWER_RECORD.SCORE.sum(),RESEARCH_RECORD.STATUS)
            .end())
        .from(TRAINEE)
        .leftJoin(MEMBER).on(MEMBER.ID.eq(TRAINEE.MEMBER_ID))
        .leftJoin(RESEARCH_RECORD).on(RESEARCH_RECORD.MEMBER_ID.eq(MEMBER.ID)
            .and(RESEARCH_RECORD.RESEARCH_QUESTIONARY_ID.eq(resourceId)))
        .leftJoin(RESEARCH_QUESTIONARY)
        .on(RESEARCH_QUESTIONARY.ID.eq(RESEARCH_RECORD.RESEARCH_QUESTIONARY_ID))
        .leftJoin(RESEARCH_ANSWER_RECORD)
        .on(RESEARCH_RECORD.ID.eq(RESEARCH_ANSWER_RECORD.RESEARCH_RECORD_ID))
        .leftJoin(ORGANIZATION).on(MEMBER.ORGANIZATION_ID.eq(ORGANIZATION.ID))
        .leftJoin(org2).on(ORGANIZATION.COMPANY_ID.eq(org2.ID))
    );
    // 组合条件
    Stream<Optional<Condition>> conditions = Stream.of(name.map(MEMBER.NAME::contains),
        fullName.map(MEMBER.FULL_NAME::contains),
        classId.map(TRAINEE.CLASS_ID::eq));
    // 过滤条件
    Condition condition = conditions.filter(Optional::isPresent).map(Optional::get)
        .reduce((acc, item) -> acc.and(item)).orElse(DSL.trueCondition())
        .and(TRAINEE.DELETE_FLAG.eq(0))
        .and(TRAINEE.AUDIT_STATUS.eq(1)).and(TRAINEE.TYPE.eq(0));


    step.orderBy(RESEARCH_RECORD.STATUS.asc(),
                 getOrgOrderCondition(org2),
                 getOrgCreateTimeCondition(org2),
                 getMemberNameCondition());


    // 查询分页总条数
    int count = researchAnswerRecordDao
        .execute(x -> x.fetchCount(step.where(condition).groupBy(zhanghao,
            MEMBER.FULL_NAME)));
    // 构建分页limit条件查询
    int firstResult = (page - 1) * pageSize;
    List<ResearchAnswerRecord> list = step.limit(firstResult, pageSize).fetch(r -> {
      ResearchAnswerRecord t = new ResearchAnswerRecord();
      BigDecimal value = r.getValue(RESEARCH_ANSWER_RECORD.SCORE.sum());
      if (value != null) {
        t.setSumScore(Integer.valueOf(value.toString()));
      } else if (ResearchQuestionary.TYPE_NEW_SATISFACTION_QUESTIONARY == r.getValue(questionType)) {
        //如果是党校培训班 是否提交问卷状态 取问卷记录表的status，否则直接取学员的提交状态
        if(flag){
          if(ResearchRecord.STATUS_FINISHED==r.getValue(RESEARCH_RECORD.STATUS)){
            t.setSumScore(1);
          }
        }else{
          if(Trainee.YES_COMMIT_QUESTIONARY == r.getValue(tCommitQues)){
            t.setSumScore(1);
          }
        }
      }
      Member m = new Member();
      m.setOrganizationId(r.getValue(TRAINEE.ORGANIZATION_ID));
      m.setName(DesensitizationUtil.desensitizeEmployeeId(r.getValue(zhanghao)));
      Organization o = new Organization();
      o.setName(r.getValue(TRAINEE.NEW_COMPANY));
      m.setFullName(EncryptUtil.aesEncrypt(r.getValue(MEMBER.FULL_NAME), null));
      t.setrId(r.getValue(rId));
      t.setOrganizationId(r.getValue(TRAINEE.ORGANIZATION_ID));
      t.setMember(m);
      t.setStatus(r.getValue(tCommitQues));
      t.setOrganization(o);
      return t;
    });
    return PagedResult.create(count, list);
  }

  private SortField<Integer> getMemberNameCondition() {
    return DSL.when(MEMBER.NAME.like("E%"), 0)
              .otherwise(1)
              .asc();
  }

  private SortField<Long> getOrgCreateTimeCondition(com.zxy.product.train.jooq.tables.Organization org) {
    return org.CREATE_TIME.asc();
  }

  private SortField<Integer> getOrgOrderCondition(com.zxy.product.train.jooq.tables.Organization org) {
    return DSL.when(org.ORDER.isNotNull(), org.ORDER)
              .otherwise(Integer.MAX_VALUE) // NULL 排在最后
              .asc();
  }

  @Override
  public ResearchQuestionary getResearchQuestionary(String resourceId) {
    // TODO Auto-generated method stub
    Integer number = researchRecordDao
        .execute(x -> x.selectDistinct(Fields.start().add(RESEARCH_RECORD.ID.count()).end())
            .from(RESEARCH_RECORD).where(RESEARCH_RECORD.RESEARCH_QUESTIONARY_ID.eq(resourceId)))
        .fetchOne(RESEARCH_RECORD.ID.count());
    Optional<ResearchQuestionary> r = researchQuestionaryDao.getOptional(resourceId);
    if (r.isPresent()) {
      ResearchQuestionary researchQuestionary = r.get();
      researchQuestionary.setNum(number);
      return researchQuestionary;
    } else {
      return null;
    }

  }

  @Override
  public List<ClassEvaluate> findOne(String classId) {
    Float f = null;
    Float fn = null;
    Float fs = null;
    ClassInfo classInfo = classInfoDao.get(classId);
    Optional<Project> project=projectDao.fetchOne(PROJECT.ID.eq(classInfo.getProjectId()));
    Integer isPartyCadre=project.get().getIsPartyCadre();
    boolean flag=(isPartyCadre==1);
    Integer tNum = classInfo.getTraineeNum();
    Integer number = classInfo.getSubmitNum();
    Integer numbern = classInfo.getAbilitySubmitNum();
    Integer numbers = classInfo.getFourDegreesSubmitNum();
    Integer view = classInfo.getView();
    List<ClassEvaluate> list2 = new ArrayList<ClassEvaluate>();
    List<ClassEvaluate> list;
    //如果是党干部培训班就走新逻辑
    if(flag){
      list=classEvaluateDao.execute(x -> x.selectDistinct(Fields.start().add(CLASS_EVALUATE)
              .add(RESEARCH_QUESTIONARY.IS_ENSEMBLE).add(RESEARCH_QUESTIONARY.NAME).end())
              .from(CLASS_EVALUATE)
              .innerJoin(RESEARCH_QUESTIONARY).on(RESEARCH_QUESTIONARY.ID.eq(CLASS_EVALUATE.RESOURCE_ID))
              .leftJoin(CLASS_THEME).on(CLASS_THEME.ID.eq(RESEARCH_QUESTIONARY.CLASS_THEME_ID))
              .where(CLASS_EVALUATE.CLASS_ID.eq(classId)
                      .and(CLASS_EVALUATE.DELETE_FLAG.eq(0))
                      .and(CLASS_EVALUATE.RELEASE.eq(1))
                      .and(CLASS_EVALUATE.TYPE.eq(ClassEvaluate.TYPE_EVA_STU_NEW)))
              .orderBy(RESEARCH_QUESTIONARY.IS_ENSEMBLE.asc(),CLASS_THEME.START_TIME.asc())
              .fetch(r -> {
                ClassEvaluate classEvaluate=r.into(ClassEvaluate.class);
                classEvaluate.setIsEnsemble(r.getValue(RESEARCH_QUESTIONARY.IS_ENSEMBLE));
                classEvaluate.setResourceName(r.getValue(RESEARCH_QUESTIONARY.NAME));
                return classEvaluate;
              }));
    }else{
      list = classEvaluateDao.execute(x -> x.selectDistinct(Fields.start().add(CLASS_EVALUATE).end())
                              .from(CLASS_EVALUATE).leftJoin(CLASS_INFO)
                              .on(CLASS_EVALUATE.CLASS_ID.eq(CLASS_INFO.ID)
                                      .and(CLASS_INFO.DELETE_FLAG.eq(0)))
                              .where(
                                      CLASS_EVALUATE.CLASS_ID.eq(classId)
                                              .and(CLASS_EVALUATE.DELETE_FLAG.eq(0))
                                              .and(CLASS_EVALUATE.RELEASE.eq(1))
                                              // 新旧满意度问卷
                                              .and(CLASS_EVALUATE.TYPE.eq(DSL
                                                      .when(CLASS_INFO.ARRIVE_DATE.lt(ClassInfo.SATISFACTION_TIME),
                                                              ClassEvaluate.TYPE_EVA_STU)
                                                      .otherwise(ClassEvaluate.TYPE_EVA_STU_NEW))))
                              //.or(CLASS_EVALUATE.TYPE.eq(5))
                              // .or(CLASS_EVALUATE.TYPE.eq(6)
                              //  .and(CLASS_INFO.QUESTIONNAIRE_STATUS.eq(1))))
                              .orderBy(CLASS_EVALUATE.TYPE.asc())
                              .fetch(r -> r.into(ClassEvaluate.class)));
    }

    if (tNum != null && number != null) {
      f = ((float) number / (float) tNum) * 100;
    }
    if (tNum != null && numbern != null) {
      fn = ((float) numbern / (float) tNum) * 100;
    }
    if (tNum != null && numbers != null) {
      fs = ((float) numbers / (float) tNum) * 100;
    }
    if (list.size() > 0) {
      for (int i = 0; i < list.size(); i++) {
        ClassEvaluate classEvaluate = list.get(i);
        classEvaluate.setIsPartyCadre(isPartyCadre);//判断是否是党校培训班
        classEvaluate.setView(view);
        // 满意度问卷 4:旧版 8：新版
        if (ResearchQuestionary.TYPE_SATISFACTION_QUESTIONARY.equals(classEvaluate.getType()) ||
            ResearchQuestionary.TYPE_NEW_SATISFACTION_QUESTIONARY.equals(classEvaluate.getType())) {
          //如果是党干部培训班，因为会生成多个满意度问卷，所以，统计数据实时统计
          if(flag){
            Integer submitCount=researchRecordDao.count(
                    RESEARCH_RECORD.RESEARCH_QUESTIONARY_ID.eq(classEvaluate.getResourceId())
                            .and(RESEARCH_RECORD.STATUS.eq(ResearchRecord.STATUS_FINISHED)));
            if (tNum != null && submitCount != null) {
              f = ((float) submitCount / (float) tNum) * 100;
            }
          }
          if (f != null) {
            classEvaluate.setResponse((float) (Math.round(f * 100)) / 100);
          } else {
            classEvaluate.setResponse((float) 0);
          }

        }
        // 四度问卷
        if (ClassEvaluate.TYPE_EVA_FDQ.equals(classEvaluate.getType())) {
          if (fs != null) {
            classEvaluate.setResponse((float) (Math.round(fs * 100)) / 100);
          } else {
            classEvaluate.setResponse((float) 0);
          }
        }
        // 能力习得问卷
        if (ClassEvaluate.TYPE_EVA_AAQ.equals(classEvaluate.getType())) {
          if (fn != null) {
            classEvaluate.setResponse((float) (Math.round(fn * 100)) / 100);
          } else {
            classEvaluate.setResponse((float) 0);
          }
        }
        // 新版满意度问卷
        if (ResearchQuestionary.TYPE_NEW_SATISFACTION_QUESTIONARY.equals(classEvaluate.getType())) {
          list2.add(classEvaluate);
        } else {
          list2.add(classEvaluate);
        }
      }
    }
    return list2;
  }

  @Override
  public List<QuestionAttr> findManYiA(String classId) {
    DecimalFormat df = new DecimalFormat("0.00");
    List<QuestionAttr> list = researchQuestionaryDao
        .execute(x -> x.select(Fields.start().add(QUESTION.CONTENT).add(DIMENSION_QUESTION.ORDER)
            .add(RESEARCH_ANSWER_RECORD.ANSWER).end()))
        .from(RESEARCH_QUESTIONARY)
        .leftJoin(DIMENSION).on(RESEARCH_QUESTIONARY.ID.eq(DIMENSION.RESEARCH_QUESTIONARY_ID))
        .leftJoin(DIMENSION_QUESTION).on(DIMENSION_QUESTION.DIMENSION_ID.eq(DIMENSION.ID))
        .leftJoin(QUESTION).on(DIMENSION_QUESTION.QUESTION_ID.eq(QUESTION.ID))
        .leftJoin(QUESTION_ATTR).on(QUESTION_ATTR.QUESTION_ID.eq(QUESTION.ID))
        .leftJoin(RESEARCH_RECORD)
        .on(RESEARCH_RECORD.RESEARCH_QUESTIONARY_ID.eq(RESEARCH_QUESTIONARY.ID))
        .leftJoin(RESEARCH_ANSWER_RECORD.forceIndex("ids_t_research_answer_record_index_f_question_id"))
            .on(RESEARCH_ANSWER_RECORD.ANSWER.eq(QUESTION_ATTR.NAME)
            .and(RESEARCH_ANSWER_RECORD.QUESTION_ID.eq(QUESTION.ID))
            .and(RESEARCH_ANSWER_RECORD.RESEARCH_RECORD_ID.eq(RESEARCH_RECORD.ID)))
        .where(RESEARCH_QUESTIONARY.TYPE.eq(4).and(RESEARCH_QUESTIONARY.CLASS_ID.eq(classId))
                .and(DIMENSION.ORDER.eq(1))
//						.and(RESEARCH_ANSWER_RECORD.ANSWER.isNotNull())
        )
        .orderBy(DIMENSION_QUESTION.ORDER).fetch(r -> {
          QuestionAttr questionAttr = new QuestionAttr();
          questionAttr.setQuestionContent(r.getValue(QUESTION.CONTENT));
          questionAttr.setAnswer(r.getValue(RESEARCH_ANSWER_RECORD.ANSWER));
//					questionAttr.setManZ(r.getValue(RESEARCH_ANSWER_RECORD.ANSWER.count()));
          return questionAttr;
        });
    // 目标对象集合
    List<QuestionAttr> targetList = new ArrayList<>();
    List<QuestionAttr> list2 = new ArrayList<>();

    list.forEach(source -> {
      String content = source.getQuestionContent();
      String answer = source.getAnswer();

      // 通过问题查找目标集合里是否存在该问题, 存在就更新
      boolean exists = false;
      for (QuestionAttr target : targetList) {
        if (content.equals(target.getQuestionContent()) && !"".equals(answer)) {
          updateTarget(target, answer);
          exists = true;
          break;
        }
      }

      // 不存在就添加
      if (!exists) {
        QuestionAttr target = initTarget(content);
        updateTarget(target, answer);
        targetList.add(target);
      }
    });
    targetList.forEach(r -> {
      QuestionAttr questionAttr = new QuestionAttr();
      questionAttr.setQuestionContent(r.getQuestionContent());
      questionAttr.setManA(r.getManA());
      questionAttr.setManB(r.getManB());
      questionAttr.setManC(r.getManC());
      questionAttr.setManD(r.getManD());
      questionAttr.setManE(r.getManE());
//				Integer count6 = r.getManZ() + r.getManB() + r.getManC() + r.getManD() + r.getManE();
      Integer count6 = r.getManZ();
      questionAttr.setManZ(r.getManZ());
      Float a = 0.00f;
      Float b = 0.00f;
      Float c = 0.00f;
      Float d = 0.00f;
      Float e = 0.00f;
      Float z = 0.00f;
      if (r.getManA() != 0 && count6 != 0) {
        a = ((float) r.getManA() / (float) count6) * 100;
        a = (float) (Math.round(a * 100)) / 100;
      }
      questionAttr.setManAF(df.format(a));
      if (r.getManB() != 0 && count6 != 0) {
        b = ((float) r.getManB() / (float) count6) * 100;
        b = (float) (Math.round(b * 100)) / 100;
      }
      questionAttr.setManBF(df.format(b));
      if (r.getManC() != 0 && count6 != 0) {
        c = ((float) r.getManC() / (float) count6) * 100;
        c = (float) (Math.round(c * 100)) / 100;
      }
      questionAttr.setManCF(df.format(c));
      if (r.getManD() != 0 && count6 != 0) {
        d = ((float) r.getManD() / (float) count6) * 100;
        d = (float) (Math.round(d * 100)) / 100;
      }
      questionAttr.setManDF(df.format(d));
      if (r.getManE() != 0 && count6 != 0) {
        e = ((float) r.getManE() / (float) count6) * 100;
        e = (float) (Math.round(e * 100)) / 100;
      }
      questionAttr.setManEF(df.format(e));
      if (count6 != 0) {
        if (r.getManA() != 0 && r.getManB() != 0) {
          z = (((float) (r.getManA() + r.getManB())) / (float) count6) * 100;
          z = (float) (Math.round(z * 100)) / 100;
          questionAttr.setManZF(df.format(z));
        } else if (r.getManA() != 0 && r.getManB() == 0) {
          z = ((float) r.getManA() / (float) count6) * 100;
          z = (float) (Math.round(z * 100)) / 100;
          questionAttr.setManZF(df.format(z));
        } else if (r.getManA() == 0 && r.getManB() != 0) {
          z = (float) (Math.round(z * 100)) / 100;
          z = ((float) r.getManB() / (float) count6) * 100;
          questionAttr.setManZF(df.format(z));
        } else {
          questionAttr.setManZF(df.format(z));
        }
      } else {
        questionAttr.setManZF(df.format(z));
      }
      list2.add(questionAttr);
    });
    System.out.println("集合长度-----------------" + list2.size());
    return list2;

  }

  @Override
  public List<QuestionAttr> findManYiB(String classId) {
    DecimalFormat df = new DecimalFormat("0.00");
    List<QuestionAttr> list = researchQuestionaryDao
        .execute(x -> x.select(
            Fields.start().add(QUESTION.ID).add(QUESTION.CONTENT).add(RESEARCH_ANSWER_RECORD.ANSWER)
                .end()))
        .from(RESEARCH_QUESTIONARY).leftJoin(DIMENSION)
        .on(RESEARCH_QUESTIONARY.ID.eq(DIMENSION.RESEARCH_QUESTIONARY_ID))
        .leftJoin(DIMENSION_QUESTION).on(DIMENSION_QUESTION.DIMENSION_ID.eq(DIMENSION.ID))
        .leftJoin(QUESTION)
        .on(DIMENSION_QUESTION.QUESTION_ID.eq(QUESTION.ID)).leftJoin(QUESTION_ATTR)
        .on(QUESTION_ATTR.QUESTION_ID.eq(QUESTION.ID))
        .leftJoin(RESEARCH_RECORD)
        .on(RESEARCH_RECORD.RESEARCH_QUESTIONARY_ID.eq(RESEARCH_QUESTIONARY.ID))
        .leftJoin(RESEARCH_ANSWER_RECORD.forceIndex("ids_t_research_answer_record_index_f_question_id"))
        .on(RESEARCH_ANSWER_RECORD.ANSWER.eq(QUESTION_ATTR.NAME)
            .and(RESEARCH_ANSWER_RECORD.QUESTION_ID.eq(QUESTION.ID))
            .and(RESEARCH_ANSWER_RECORD.RESEARCH_RECORD_ID.eq(RESEARCH_RECORD.ID)))
        .where(RESEARCH_QUESTIONARY.TYPE.eq(4).and(RESEARCH_QUESTIONARY.CLASS_ID.eq(classId))
                .and(DIMENSION.ORDER.eq(2))
//						.and((RESEARCH_ANSWER_RECORD.ANSWER.isNotNull())
//								.or(QUESTION.CONTENT.like("%是否愿意推荐"))
//								.or(QUESTION.CONTENT.like("%课程内容安排"))
//								.or(QUESTION.CONTENT.like("%讲师授课水平"))
//								)
        )
        .orderBy(QUESTION.ORDER).fetch(r -> {
          QuestionAttr questionAttr = new QuestionAttr();
          questionAttr.setQuestionId(r.getValue(QUESTION.ID));
          questionAttr.setQuestionContent(r.getValue(QUESTION.CONTENT));
          questionAttr.setAnswer(r.getValue(RESEARCH_ANSWER_RECORD.ANSWER));
//					questionAttr.setManZ(r.getValue(RESEARCH_ANSWER_RECORD.ANSWER.count()));
          return questionAttr;
        });

    // 目标对象集合
    List<QuestionAttr> targetList = new ArrayList<>();
    List<QuestionAttr> attr = new ArrayList<>();

    list.forEach(source -> {
      String content = source.getQuestionContent();
      String answer = source.getAnswer();
      String id = source.getQuestionId();

      // 通过问题查找目标集合里是否存在该问题, 存在就更新
      boolean exists = false;
      for (QuestionAttr target : targetList) {
        if (id.equals(target.getQuestionId()) && !"".equals(answer)) {
          updateTarget(target, answer);
          exists = true;
          break;
        }
      }

      // 不存在就添加
      if (!exists) {
        QuestionAttr target = initTargetT(content, id);
        updateTarget(target, answer);
        targetList.add(target);
      }
    });

    targetList.forEach(r -> {
      QuestionAttr questionAttr = new QuestionAttr();
      questionAttr.setQuestionContent(r.getQuestionContent());
      questionAttr.setManA(r.getManA());
      questionAttr.setManB(r.getManB());
      questionAttr.setManC(r.getManC());
      questionAttr.setManD(r.getManD());
      questionAttr.setManE(r.getManE());
      questionAttr.setManF(r.getManF());
      Integer count6 = r.getManZ();
      questionAttr.setManZ(r.getManZ());
      Float a = 0.00f;
      Float b = 0.00f;
      Float c = 0.00f;
      Float d = 0.00f;
      Float e = 0.00f;
//			Float f = 0.00f;
      Float z = 0.00f;
      if (r.getManA() != 0 && count6 != 0) {
        a = ((float) r.getManA() / (float) count6) * 100;
        a = (float) (Math.round(a * 100)) / 100;
      }
      questionAttr.setManAF(df.format(a));
      if (r.getManB() != 0 && count6 != 0) {
        b = ((float) r.getManB() / (float) count6) * 100;
        b = (float) (Math.round(b * 100)) / 100;
      }
      questionAttr.setManBF(df.format(b));
      if (r.getManC() != 0 && count6 != 0) {
        c = ((float) r.getManC() / (float) count6) * 100;
        c = (float) (Math.round(c * 100)) / 100;
      }
      questionAttr.setManCF(df.format(c));
      if (r.getManD() != 0 && count6 != 0) {
        d = ((float) r.getManD() / (float) count6) * 100;
        d = (float) (Math.round(d * 100)) / 100;
      }
      questionAttr.setManDF(df.format(d));
      if (r.getManE() != 0 && count6 != 0) {
        e = ((float) r.getManE() / (float) count6) * 100;
        e = (float) (Math.round(e * 100)) / 100;
      }
      questionAttr.setManEF(df.format(e));
      if (count6 != 0) {
        if (r.getManA() != 0 && r.getManB() != 0) {
          z = (((float) (r.getManA() + r.getManB())) / (float) count6) * 100;
          z = (float) (Math.round(z * 100)) / 100;
          questionAttr.setManZF(df.format(z));
        } else if (r.getManA() != 0 && r.getManB() == 0) {
          z = ((float) r.getManA() / (float) count6) * 100;
          z = (float) (Math.round(z * 100)) / 100;
          questionAttr.setManZF(df.format(z));
        } else if (r.getManA() == 0 && r.getManB() != 0) {
          z = ((float) r.getManB() / (float) count6) * 100;
          z = (float) (Math.round(z * 100)) / 100;
          questionAttr.setManZF(df.format(z));
        } else {
          questionAttr.setManZF(df.format(z));
        }
      } else {
        questionAttr.setManZF(df.format(z));
      }
      attr.add(questionAttr);
    });
    List<QuestionAttr> attrNew = new ArrayList<QuestionAttr>();
    Float p = 0.00f;
    if (attr.size() > 0) {
      for (int i = 0; i < attr.size(); i++) {
        Float f = 0.00f;
        if (!attr.get(i).getQuestionContent().contains("是否愿意推荐")) {
          p += Float.parseFloat(attr.get(i).getManAF()) + Float.parseFloat(attr.get(i).getManBF());
        } else {
          attr.get(i - 1).setManCMF(df.format(p / 2));
          attr.get(i - 2).setManCMF(df.format(p / 2));
          attr.get(i - 1).setManF(attr.get(i).getManF());
          attr.get(i - 2).setManF(attr.get(i).getManF());
          if (attr.get(i).getManF() != 0 && attr.get(i - 1).getManZ() != 0) {
            f = ((float) attr.get(i).getManF() / (float) attr.get(i - 1).getManZ()) * 100;
            f = (float) (Math.round(f * 100)) / 100;
          }
          attr.get(i - 1).setManFF(df.format(f));
          attr.get(i - 2).setManFF(df.format(f));
          attrNew.add(attr.get(i - 2));
          attrNew.add(attr.get(i - 1));
          p = 0.00f;
        }
      }
    }
    System.out.println("第二部分集合长度-----------------" + attrNew.size());
    sender.send(MessageTypeContent.TRAIN_QUESTIONNAIRE_UPDATE,
        MessageHeaderContent.QUESTIONNAIRECLASSID, classId);
    return attrNew;
  }

  @Override
  public List<QuestionAttr> findManYiBDW(String classId) {
    DecimalFormat df = new DecimalFormat("0.00");
    java.text.DecimalFormat df2 = new java.text.DecimalFormat("#.0");
    Integer countZ = 0;
    List<QuestionAttr> list = researchQuestionaryDao.execute(x -> x.select(
            Fields.start().add(QUESTION.ID).add(QUESTION.CONTENT).add(RESEARCH_ANSWER_RECORD.ANSWER)
                .end()))
        .from(RESEARCH_QUESTIONARY).leftJoin(DIMENSION)
        .on(RESEARCH_QUESTIONARY.ID.eq(DIMENSION.RESEARCH_QUESTIONARY_ID))
        .leftJoin(DIMENSION_QUESTION).on(DIMENSION_QUESTION.DIMENSION_ID.eq(DIMENSION.ID))
        .leftJoin(QUESTION)
        .on(DIMENSION_QUESTION.QUESTION_ID.eq(QUESTION.ID)).leftJoin(QUESTION_ATTR)
        .on(QUESTION_ATTR.QUESTION_ID.eq(QUESTION.ID))
        .leftJoin(RESEARCH_RECORD)
        .on(RESEARCH_RECORD.RESEARCH_QUESTIONARY_ID.eq(RESEARCH_QUESTIONARY.ID))
        .leftJoin(RESEARCH_ANSWER_RECORD.forceIndex("ids_t_research_answer_record_index_f_question_id"))
        .on(RESEARCH_ANSWER_RECORD.ANSWER.eq(QUESTION_ATTR.NAME)
            .and(RESEARCH_ANSWER_RECORD.QUESTION_ID.eq(QUESTION.ID))
            .and(RESEARCH_ANSWER_RECORD.RESEARCH_RECORD_ID.eq(RESEARCH_RECORD.ID)))
        .where(RESEARCH_QUESTIONARY.TYPE.eq(4).and(RESEARCH_QUESTIONARY.CLASS_ID.eq(classId))
                .and(DIMENSION.ORDER.eq(2))
//						.and((RESEARCH_ANSWER_RECORD.ANSWER.isNotNull())
//								.or(QUESTION.CONTENT.like("%是否愿意推荐"))
//								)
            )
            .orderBy(QUESTION.ORDER).fetch(r -> {
              QuestionAttr questionAttr = new QuestionAttr();
              questionAttr.setQuestionId(r.getValue(QUESTION.ID));
              questionAttr.setQuestionContent(r.getValue(QUESTION.CONTENT));
              questionAttr.setAnswer(r.getValue(RESEARCH_ANSWER_RECORD.ANSWER));
//					questionAttr.setManZ(r.getValue(RESEARCH_ANSWER_RECORD.ANSWER.count()));
              return questionAttr;
            });

    // 目标对象集合
    List<QuestionAttr> targetList = new ArrayList<>();
    List<QuestionAttr> attr = new ArrayList<>();

    list.forEach(source -> {
      String content = source.getQuestionContent();
      String answer = source.getAnswer();
      String id = source.getQuestionId();

      // 通过问题查找目标集合里是否存在该问题, 存在就更新
      boolean exists = false;
      for (QuestionAttr target : targetList) {
        if (id.equals(target.getQuestionId()) && !"".equals(answer)) {
          updateTarget(target, answer);
          exists = true;
          break;
        }
      }

      // 不存在就添加
      if (!exists) {
        QuestionAttr target = initTargetT(content, id);
        updateTarget(target, answer);
        targetList.add(target);
      }
    });
    targetList.forEach(r -> {
      QuestionAttr questionAttr = new QuestionAttr();
      questionAttr.setQuestionContent(r.getQuestionContent());
//			questionAttr.setQuestionId(r.getQuestionId());
      questionAttr.setManA(r.getManA());
      questionAttr.setManB(r.getManB());
      questionAttr.setManC(r.getManC());
      questionAttr.setManD(r.getManD());
      questionAttr.setManE(r.getManE());
      questionAttr.setManF(r.getManF());
//			Integer count6 = r.getValue(count1) + r.getValue(count2) + r.getValue(count3) + r.getValue(count4) + r.getValue(count5);
//			Integer count9 = r.getManF();
      Integer count6 = r.getManZ();
      questionAttr.setManZ(r.getManZ());
      Float a = 0.00f;
      Float b = 0.00f;
      Float c = 0.00f;
      Float d = 0.00f;
      Float e = 0.00f;
//			Float f = 0.00f;
      Float z = 0.00f;
      if (r.getManA() != 0 && count6 != 0) {
        a = ((float) r.getManA() / (float) count6) * 100;
        a = (float) (Math.round(a * 100)) / 100;
      }
      questionAttr.setManAF(df.format(a));
      if (r.getManB() != 0 && count6 != 0) {
        b = ((float) r.getManB() / (float) count6) * 100;
        b = (float) (Math.round(b * 100)) / 100;
      }
      questionAttr.setManBF(df.format(b));
      if (r.getManC() != 0 && count6 != 0) {
        c = ((float) r.getManC() / (float) count6) * 100;
        c = (float) (Math.round(c * 100)) / 100;
      }
      questionAttr.setManCF(df.format(c));
      if (r.getManD() != 0 && count6 != 0) {
        d = ((float) r.getManD() / (float) count6) * 100;
        d = (float) (Math.round(d * 100)) / 100;
      }
      questionAttr.setManDF(df.format(d));
      if (r.getManE() != 0 && count6 != 0) {
        e = ((float) r.getManE() / (float) count6) * 100;
        e = (float) (Math.round(e * 100)) / 100;
      }
      questionAttr.setManEF(df.format(e));
//			if (count9 != 0 && count6 != 0) {
//				f = ((float) count9 / (float) count6) * 100;
//				f = (float)(Math.round(f*100))/100;
//			}
//			questionAttr.setManFF(df.format(f));
      if (count6 != 0) {
        if (r.getManA() != 0 && r.getManB() != 0) {
          z = (((float) (r.getManA() + r.getManB())) / (float) count6) * 100;
          z = (float) (Math.round(z * 100)) / 100;
          questionAttr.setManZF(df.format(z));
        } else if (r.getManA() != 0 && r.getManB() == 0) {
          z = ((float) r.getManA() / (float) count6) * 100;
          z = (float) (Math.round(z * 100)) / 100;
          questionAttr.setManZF(df.format(z));
        } else if (r.getManA() == 0 && r.getManB() != 0) {
          z = ((float) r.getManB() / (float) count6) * 100;
          z = (float) (Math.round(z * 100)) / 100;
          questionAttr.setManZF(df.format(z));
        } else {
          questionAttr.setManZF(df.format(z));
        }
      } else {
        questionAttr.setManZF(df.format(z));
      }
      attr.add(questionAttr);
    });
    List<QuestionAttr> attrNew = new ArrayList<QuestionAttr>();
    Float p = 0.00f;
    if (attr.size() > 0) {
      for (int i = 0; i < attr.size(); i++) {
        Float f = 0.00f;
        if (!attr.get(i).getQuestionContent().contains("是否愿意推荐")) {
          p += Float.parseFloat(attr.get(i).getManAF()) + Float.parseFloat(attr.get(i).getManBF());
        } else {
          attr.get(i - 1).setManCMF(df.format(p / 2));
          attr.get(i - 2).setManCMF(df.format(p / 2));
          attr.get(i - 1).setManF(attr.get(i).getManF());
          attr.get(i - 2).setManF(attr.get(i).getManF());
          if (attr.get(i).getManF() != 0 && attr.get(i - 1).getManZ() != 0) {
            f = ((float) attr.get(i).getManF() / (float) attr.get(i - 1).getManZ()) * 100;
            f = (float) (Math.round(f * 100)) / 100;
          }
          attr.get(i - 1).setManFF(df.format(f));
          attr.get(i - 2).setManFF(df.format(f));
          attrNew.add(attr.get(i - 2));
          attrNew.add(attr.get(i - 1));
          p = 0.00f;
        }
      }
    }
    List<QuestionAttr> attrNew2 = new ArrayList<QuestionAttr>();
    if (attrNew.size() > 0) {
      for (int i = 0; i < attrNew.size(); i++) {
        String a = attrNew.get(i).getQuestionContent();
        int laststr = a.lastIndexOf('(');
        String cname = a.substring(0, laststr);
        String lname = a.substring(a.lastIndexOf('(') + 1, a.lastIndexOf(')'));
        String nname = a.substring(a.lastIndexOf(")") + 1);
        QuestionAttr qa = attrNew.get(i);
        qa.setCourseContent(nname);
        qa.setCourseLecturer("【" + lname + "】");
        qa.setCourseName("【" + cname + "】");
        attrNew2.add(qa);
      }
    }
    sender.send(MessageTypeContent.TRAIN_QUESTIONNAIRE_UPDATE,
            MessageHeaderContent.QUESTIONNAIRECLASSID, classId);
    return attrNew2;
  }

  @Override
  public List<QuestionAttr> findManYiKua(String classId, List<String> oIds) {
    DecimalFormat df = new DecimalFormat("0.00");
    // TODO Auto-generated method stub

	/*	ClassInfo classInfo = classInfoDao.get(classId);
		if (classInfo != null) {
			Field<Integer> count1 = DSL.choose(RESEARCH_ANSWER_RECORD.ANSWER).when("0", RESEARCH_ANSWER_RECORD.ID).count()
					.as("count1");

			Field<Integer> count2 = DSL.choose(RESEARCH_ANSWER_RECORD.ANSWER).when("1", RESEARCH_ANSWER_RECORD.ID).count()
					.as("count2");

			Field<Integer> count3 = DSL.choose(RESEARCH_ANSWER_RECORD.ANSWER).when("2", RESEARCH_ANSWER_RECORD.ID).count()
					.as("count3");

			Field<Integer> count4 = DSL.choose(RESEARCH_ANSWER_RECORD.ANSWER).when("3", RESEARCH_ANSWER_RECORD.ID).count()
					.as("count4");
			Field<Integer> count5 = DSL.choose(RESEARCH_ANSWER_RECORD.ANSWER).when("4", RESEARCH_ANSWER_RECORD.ID).count()
					.as("count5");

			List<String> rIds = new ArrayList<String>();
			String[] qId = {"1","2","3","4","5","6","7","8","9","10"};
			List<String> qIds =java.util.Arrays.asList(qId);
			List<String> rcIds = new ArrayList<String>();

			List<ResearchQuestionary> list = researchQuestionaryDao.execute(d -> {
	            Table<Record1<String>> basic = (d.select(RESEARCH_QUESTIONARY.ID)
	                    .from(RESEARCH_QUESTIONARY)
	                    .leftJoin(CLASS_INFO).on(CLASS_INFO.ID.eq(RESEARCH_QUESTIONARY.CLASS_ID))
	                    .leftJoin(PROJECT).on(PROJECT.ID.eq(CLASS_INFO.PROJECT_ID))
	                    .where(CLASS_INFO.DELETE_FLAG.eq(ClassInfo.DELETE_FLASE))
	                    .and(CLASS_INFO.IMPLEMENTATION_YEAR.eq(classInfo.getImplementationYear()))
						.and(CLASS_INFO.IMPLEMENTATION_MONTH.eq(classInfo.getImplementationMonth()))
						.and(PROJECT.DELETE_FLAG.eq(ClassInfo.DELETE_FLASE))
						.and(RESEARCH_QUESTIONARY.TYPE.eq(4))
						.and(PROJECT.ORGANIZATION_ID.in(oIds))
	            ).asTable("b");

	            return d.select(Fields.start()
	                    .add(RESEARCH_QUESTIONARY.ID)
	                    .end())
	                    .from(RESEARCH_QUESTIONARY)
	                    .innerJoin(basic).on(basic.field(RESEARCH_QUESTIONARY.ID).eq(RESEARCH_QUESTIONARY.ID))
	                    .fetch( r -> {
	                        ResearchQuestionary cf = r.into(ResearchQuestionary.class);
	                        rIds.add(r.getValue(RESEARCH_QUESTIONARY.ID));
	                        return cf;
	                    });
	        });

			Map<String, ResearchRecord> researchRecordMap = researchRecordDao.execute(x -> x.select(RESEARCH_RECORD.ID).from(RESEARCH_RECORD).where(RESEARCH_RECORD.RESEARCH_QUESTIONARY_ID.in(rIds)))
	                .fetch(r -> {
	                	ResearchRecord cf = r.into(ResearchRecord.class);
	                	rcIds.add(r.getValue(RESEARCH_RECORD.ID));
                        return cf;
                    }).stream()
	                .collect(Collectors.toMap(ResearchRecord::getId, m -> m));


			Map<String, QuestionAttr> questionAttrMap = questionAttrDao.execute(x ->
			x.select(QUESTION.ORDER,QUESTION.CONTENT,count1,count2,count3,count4,count5,RESEARCH_ANSWER_RECORD.ANSWER.count())
			.from(QUESTION_ATTR)
			.leftJoin(QUESTION)
			.on(QUESTION.ID.eq(QUESTION_ATTR.QUESTION_ID))
			.leftJoin(RESEARCH_ANSWER_RECORD)
			.on(RESEARCH_ANSWER_RECORD.ANSWER.eq(QUESTION_ATTR.NAME)
					.and(RESEARCH_ANSWER_RECORD.QUESTION_ID.eq(QUESTION.ID)))
//			.where(RESEARCH_ANSWER_RECORD.RESEARCH_RECORD_ID.in(rIds))
			.where(RESEARCH_ANSWER_RECORD.QUESTION_ID.in(qIds)
			.and(RESEARCH_ANSWER_RECORD.RESEARCH_RECORD_ID.in(rcIds)))
			.groupBy(QUESTION.CONTENT).orderBy(QUESTION.ORDER.asc())
    	       .fetch(r ->{
    	    	   QuestionAttr questionAttr = new QuestionAttr();
    	    	   questionAttr.setQuestionContent(r.getValue(QUESTION.CONTENT));
    	    	   questionAttr.setQuestionOrder(r.getValue(QUESTION.ORDER));
					questionAttr.setManA(r.getValue(count1));
					questionAttr.setManB(r.getValue(count2));
					questionAttr.setManC(r.getValue(count3));
					questionAttr.setManD(r.getValue(count4));
					questionAttr.setManE(r.getValue(count5));
					Integer count6 = r.getValue(count1) + r.getValue(count2) + r.getValue(count3) + r.getValue(count4) + r.getValue(count5);
					questionAttr.setManZ(count6);
					Float a = 0.00f;
					Float b = 0.00f;
					Float c = 0.00f;
					Float d = 0.00f;
					Float e = 0.00f;
					Float z = 0.00f;
					if (r.getValue(count1) != 0 && count6 != 0) {
						a = ((float) r.getValue(count1) / (float) count6) * 100;
						a = (float)(Math.round(a*100))/100;
					}
					questionAttr.setManAF(df.format(a));
					if (r.getValue(count2) != 0 && count6 != 0) {
						b = ((float) r.getValue(count2) / (float) count6) * 100;
						b = (float)(Math.round(b*100))/100;
					}
					questionAttr.setManBF(df.format(b));
					if (r.getValue(count3) != 0 && count6 != 0) {
						c = ((float) r.getValue(count3) / (float) count6) * 100;
						c = (float)(Math.round(c*100))/100;
					}
					questionAttr.setManCF(df.format(c));
					if (r.getValue(count4) != 0 && count6 != 0) {
						d = ((float) r.getValue(count4) / (float) count6) * 100;
						d = (float)(Math.round(d*100))/100;
					}
					questionAttr.setManDF(df.format(d));
					if (r.getValue(count5) != 0 && count6 != 0) {
						e = ((float) r.getValue(count5) / (float) count6) * 100;
						e = (float)(Math.round(e*100))/100;
					}
					questionAttr.setManEF(df.format(e));
					if (count6 != 0) {
						if (r.getValue(count1) != 0 && r.getValue(count2) != 0) {
							z = (((float) (r.getValue(count1) + r.getValue(count2))) / (float) count6) * 100;
							z = (float)(Math.round(z*100))/100;
							questionAttr.setManZF(df.format(z));
						} else if (r.getValue(count1) != 0 && r.getValue(count2) == 0) {
							z = ((float) r.getValue(count1) / (float) count6) * 100;
							z = (float)(Math.round(z*100))/100;
							questionAttr.setManZF(df.format(z));
						} else if (r.getValue(count1) == 0 && r.getValue(count2) != 0) {
							z = ((float) r.getValue(count2) / (float) count6) * 100;
							z = (float)(Math.round(z*100))/100;
							questionAttr.setManZF(df.format(z));
						} else {
							questionAttr.setManZF(df.format(z));
						}
					} else {
						questionAttr.setManZF(df.format(z));
					}
					return questionAttr;
    	       }).stream()
    	       .collect(Collectors.toMap(QuestionAttr::getQuestionContent, m -> m)));*/
    ClassInfo classInfo = classInfoDao.get(classId);
    if (classInfo != null) {
      Field<Integer> count1 = DSL.choose(RESEARCH_ANSWER_RECORD.ANSWER)
          .when("0", RESEARCH_ANSWER_RECORD.ID).count()
          .as("count1");

      Field<Integer> count2 = DSL.choose(RESEARCH_ANSWER_RECORD.ANSWER)
          .when("1", RESEARCH_ANSWER_RECORD.ID).count()
          .as("count2");

      Field<Integer> count3 = DSL.choose(RESEARCH_ANSWER_RECORD.ANSWER)
          .when("2", RESEARCH_ANSWER_RECORD.ID).count()
          .as("count3");

      Field<Integer> count4 = DSL.choose(RESEARCH_ANSWER_RECORD.ANSWER)
          .when("3", RESEARCH_ANSWER_RECORD.ID).count()
          .as("count4");
      Field<Integer> count5 = DSL.choose(RESEARCH_ANSWER_RECORD.ANSWER)
          .when("4", RESEARCH_ANSWER_RECORD.ID).count()
          .as("count5");

      List<String> rIds = new ArrayList<String>();
      String[] qId = {"1", "2", "3", "4", "5", "6", "7", "8", "9", "10"};
      List<String> qIds = java.util.Arrays.asList(qId);
      List<String> rcIds = new ArrayList<String>();
      List<ResearchQuestionary> list = researchQuestionaryDao.execute(d -> {
        Table<Record1<String>> basic = (d.select(RESEARCH_QUESTIONARY.ID)
            .from(RESEARCH_QUESTIONARY)
            .leftJoin(CLASS_INFO).on(CLASS_INFO.ID.eq(RESEARCH_QUESTIONARY.CLASS_ID))
            .leftJoin(PROJECT).on(PROJECT.ID.eq(CLASS_INFO.PROJECT_ID))
            .where(CLASS_INFO.DELETE_FLAG.eq(ClassInfo.DELETE_FLASE))
            .and(CLASS_INFO.IMPLEMENTATION_YEAR.eq(classInfo.getImplementationYear()))
            .and(CLASS_INFO.IMPLEMENTATION_MONTH.eq(classInfo.getImplementationMonth()))
            .and(PROJECT.DELETE_FLAG.eq(ClassInfo.DELETE_FLASE))
            .and(RESEARCH_QUESTIONARY.TYPE.eq(4))
            .and(PROJECT.ORGANIZATION_ID.in(oIds))
        ).asTable("b");

        return d.select(Fields.start()
            .add(RESEARCH_QUESTIONARY.ID)
            .end())
            .from(RESEARCH_QUESTIONARY)
            .innerJoin(basic).on(basic.field(RESEARCH_QUESTIONARY.ID).eq(RESEARCH_QUESTIONARY.ID))
            .fetch(r -> {
              ResearchQuestionary cf = r.into(ResearchQuestionary.class);
              rIds.add(r.getValue(RESEARCH_QUESTIONARY.ID));
              return cf;
            });
      });

      Map<String, ResearchRecord> researchRecordMap = researchRecordDao.execute(
          x -> x.select(RESEARCH_RECORD.ID).from(RESEARCH_RECORD)
              .where(RESEARCH_RECORD.RESEARCH_QUESTIONARY_ID.in(rIds)))
          .fetch(r -> {
            ResearchRecord cf = r.into(ResearchRecord.class);
            rcIds.add(r.getValue(RESEARCH_RECORD.ID));
            return cf;
          }).stream()
          .collect(Collectors.toMap(ResearchRecord::getId, m -> m));

      Map<String, QuestionAttr> questionAttrMap = questionAttrDao.execute(x ->
          x.select(QUESTION.ORDER, QUESTION.CONTENT, count1, count2, count3, count4, count5,
              RESEARCH_ANSWER_RECORD.ANSWER.count())
              .from(QUESTION_ATTR)
              .leftJoin(QUESTION)
              .on(QUESTION.ID.eq(QUESTION_ATTR.QUESTION_ID))
              .leftJoin(RESEARCH_ANSWER_RECORD.forceIndex("ids_t_research_answer_record_index_f_question_id"))
              .on(RESEARCH_ANSWER_RECORD.ANSWER.eq(QUESTION_ATTR.NAME)
                  .and(RESEARCH_ANSWER_RECORD.QUESTION_ID.eq(QUESTION.ID)))
//			.where(RESEARCH_ANSWER_RECORD.RESEARCH_RECORD_ID.in(rIds))
              .where(RESEARCH_ANSWER_RECORD.QUESTION_ID.in(qIds)
                  .and(RESEARCH_ANSWER_RECORD.RESEARCH_RECORD_ID.in(rcIds)))
              .groupBy(QUESTION.CONTENT).orderBy(QUESTION.ORDER.asc())
              .fetch(r -> {
                QuestionAttr questionAttr = new QuestionAttr();
                questionAttr.setQuestionContent(r.getValue(QUESTION.CONTENT));
                questionAttr.setQuestionOrder(r.getValue(QUESTION.ORDER));
                questionAttr.setManA(r.getValue(count1));
                questionAttr.setManB(r.getValue(count2));
                questionAttr.setManC(r.getValue(count3));
                questionAttr.setManD(r.getValue(count4));
                questionAttr.setManE(r.getValue(count5));
                Integer count6 = r.getValue(count1) + r.getValue(count2) + r.getValue(count3) + r
                    .getValue(count4) + r.getValue(count5);
                questionAttr.setManZ(count6);
                return questionAttr;
              })
              .stream()
              .collect(Collectors.toMap(QuestionAttr::getQuestionContent, m -> m)));

      List<QuestionAttr> mapValuesList = new ArrayList<QuestionAttr>(questionAttrMap.values());
      mapValuesList
          .sort((lec1, lec2) -> lec1.getQuestionOrder().compareTo(lec2.getQuestionOrder()));
      List<QuestionAttr> list2 = new ArrayList<>();
      // 目标对象集合
//	        List<QuestionAttr> targetList = new ArrayList<>();
//	        List<QuestionAttr> list2 = new ArrayList<>();
//
//	        mapValuesList.forEach(source->{
//	        	String content = source.getQuestionContent();
//	        	String answer = source.getAnswer();
//
//	        	// 通过问题查找目标集合里是否存在该问题, 存在就更新
//	        	boolean exists = false;
//	        	for (QuestionAttr target : targetList) {
//					if (content.equals(target.getQuestionContent()) && !"".equals(answer)) {
//						updateTarget(target, answer);
//						exists = true;
//						break;
//					}
//				}
//
//	        	// 不存在就添加
//	        	if (!exists) {
//	        		QuestionAttr target = initTarget(content);
//	        		updateTarget(target, answer);
//	        		targetList.add(target);
//	        	}
//	        });
      mapValuesList.forEach(r -> {
        QuestionAttr questionAttr = new QuestionAttr();
        questionAttr.setQuestionContent(r.getQuestionContent());
        questionAttr.setManA(r.getManA());
        questionAttr.setManB(r.getManB());
        questionAttr.setManC(r.getManC());
        questionAttr.setManD(r.getManD());
        questionAttr.setManE(r.getManE());
//					Integer count6 = r.getManZ() + r.getManB() + r.getManC() + r.getManD() + r.getManE();
        Integer count6 = r.getManZ();
        questionAttr.setManZ(r.getManZ());
        Float a = 0.00f;
        Float b = 0.00f;
        Float c = 0.00f;
        Float d = 0.00f;
        Float e = 0.00f;
        Float z = 0.00f;
        if (r.getManA() != 0 && count6 != 0) {
          a = ((float) r.getManA() / (float) count6) * 100;
          a = (float) (Math.round(a * 100)) / 100;
        }
        questionAttr.setManAF(df.format(a));
        if (r.getManB() != 0 && count6 != 0) {
          b = ((float) r.getManB() / (float) count6) * 100;
          b = (float) (Math.round(b * 100)) / 100;
        }
        questionAttr.setManBF(df.format(b));
        if (r.getManC() != 0 && count6 != 0) {
          c = ((float) r.getManC() / (float) count6) * 100;
          c = (float) (Math.round(c * 100)) / 100;
        }
        questionAttr.setManCF(df.format(c));
        if (r.getManD() != 0 && count6 != 0) {
          d = ((float) r.getManD() / (float) count6) * 100;
          d = (float) (Math.round(d * 100)) / 100;
        }
        questionAttr.setManDF(df.format(d));
        if (r.getManE() != 0 && count6 != 0) {
          e = ((float) r.getManE() / (float) count6) * 100;
          e = (float) (Math.round(e * 100)) / 100;
        }
        questionAttr.setManEF(df.format(e));
        if (count6 != 0) {
          if (r.getManA() != 0 && r.getManB() != 0) {
            z = (((float) (r.getManA() + r.getManB())) / (float) count6) * 100;
            z = (float) (Math.round(z * 100)) / 100;
            questionAttr.setManZF(df.format(z));
          } else if (r.getManA() != 0 && r.getManB() == 0) {
            z = ((float) r.getManA() / (float) count6) * 100;
            z = (float) (Math.round(z * 100)) / 100;
            questionAttr.setManZF(df.format(z));
          } else if (r.getManA() == 0 && r.getManB() != 0) {
            z = (float) (Math.round(z * 100)) / 100;
            z = ((float) r.getManB() / (float) count6) * 100;
            questionAttr.setManZF(df.format(z));
          } else {
            questionAttr.setManZF(df.format(z));
          }
        } else {
          questionAttr.setManZF(df.format(z));
        }
        list2.add(questionAttr);
      });
      return list2;
    } else {
      return null;
    }

  }

  @Override
  public List<QuestionAttr> findManResponseZYiKua(String classId) {
		/*DecimalFormat df = new DecimalFormat("0.00");
		// TODO Auto-generated method stub

		ClassInfo classInfo = classInfoDao.get(classId);
		if (classInfo != null) {
			Field<Integer> count1 = DSL.choose(RESEARCH_ANSWER_RECORD.ANSWER).when("0", RESEARCH_ANSWER_RECORD.ID).count()
					.as("count1");

			Field<Integer> count2 = DSL.choose(RESEARCH_ANSWER_RECORD.ANSWER).when("1", RESEARCH_ANSWER_RECORD.ID).count()
					.as("count2");

			Field<Integer> count3 = DSL.choose(RESEARCH_ANSWER_RECORD.ANSWER).when("2", RESEARCH_ANSWER_RECORD.ID).count()
					.as("count3");

			Field<Integer> count4 = DSL.choose(RESEARCH_ANSWER_RECORD.ANSWER).when("3", RESEARCH_ANSWER_RECORD.ID).count()
					.as("count4");
			Field<Integer> count5 = DSL.choose(RESEARCH_ANSWER_RECORD.ANSWER).when("4", RESEARCH_ANSWER_RECORD.ID).count()
					.as("count5");

			List<String> rIds = new ArrayList<String>();
			String[] qId = {"1","2","3","4","5","6","7","8","9","10"};
			List<String> qIds =java.util.Arrays.asList(qId);
			List<String> rcIds = new ArrayList<String>();

			List<ResearchQuestionary> list = researchQuestionaryDao.execute(d -> {
	            Table<Record1<String>> basic = (d.select(RESEARCH_QUESTIONARY.ID)
	                    .from(RESEARCH_QUESTIONARY)
	                    .leftJoin(CLASS_INFO).on(CLASS_INFO.ID.eq(RESEARCH_QUESTIONARY.CLASS_ID))
	                    .leftJoin(PROJECT).on(PROJECT.ID.eq(CLASS_INFO.PROJECT_ID))
	                    .where(CLASS_INFO.DELETE_FLAG.eq(ClassInfo.DELETE_FLASE))
	                    .and(CLASS_INFO.IMPLEMENTATION_YEAR.eq(classInfo.getImplementationYear()))
						.and(CLASS_INFO.IMPLEMENTATION_MONTH.eq(classInfo.getImplementationMonth()))
						.and(PROJECT.DELETE_FLAG.eq(ClassInfo.DELETE_FLASE))
						.and(RESEARCH_QUESTIONARY.TYPE.eq(4))
	            ).asTable("b");

	            return d.select(Fields.start()
	                    .add(RESEARCH_QUESTIONARY.ID)
	                    .end())
	                    .from(RESEARCH_QUESTIONARY)
	                    .innerJoin(basic).on(basic.field(RESEARCH_QUESTIONARY.ID).eq(RESEARCH_QUESTIONARY.ID))
	                    .fetch( r -> {
	                        ResearchQuestionary cf = r.into(ResearchQuestionary.class);
	                        rIds.add(r.getValue(RESEARCH_QUESTIONARY.ID));
	                        return cf;
	                    });
	        });

			Map<String, ResearchRecord> researchRecordMap = researchRecordDao.execute(x -> x.select(RESEARCH_RECORD.ID).from(RESEARCH_RECORD).where(RESEARCH_RECORD.RESEARCH_QUESTIONARY_ID.in(rIds)))
	                .fetch(r -> {
	                	ResearchRecord cf = r.into(ResearchRecord.class);
	                	rcIds.add(r.getValue(RESEARCH_RECORD.ID));
                        return cf;
                    }).stream()
	                .collect(Collectors.toMap(ResearchRecord::getId, m -> m));


			Map<String, QuestionAttr> questionAttrMap = questionAttrDao.execute(x ->
			x.select(QUESTION.ORDER,QUESTION.CONTENT,count1,count2,count3,count4,count5,RESEARCH_ANSWER_RECORD.ANSWER.count())
			.from(QUESTION_ATTR)
			.leftJoin(QUESTION)
			.on(QUESTION.ID.eq(QUESTION_ATTR.QUESTION_ID))
			.leftJoin(RESEARCH_ANSWER_RECORD)
			.on(RESEARCH_ANSWER_RECORD.ANSWER.eq(QUESTION_ATTR.NAME)
					.and(RESEARCH_ANSWER_RECORD.QUESTION_ID.eq(QUESTION.ID)))
//			.where(RESEARCH_ANSWER_RECORD.RESEARCH_RECORD_ID.in(rIds))
			.where(RESEARCH_ANSWER_RECORD.QUESTION_ID.in(qIds)
			.and(RESEARCH_ANSWER_RECORD.RESEARCH_RECORD_ID.in(rcIds)))
			.groupBy(QUESTION.CONTENT).orderBy(QUESTION.ORDER.asc())
    	       .fetch(r ->{
    	    	   QuestionAttr questionAttr = new QuestionAttr();
    	    	   questionAttr.setQuestionContent(r.getValue(QUESTION.CONTENT));
    	    	   questionAttr.setQuestionOrder(r.getValue(QUESTION.ORDER));
					questionAttr.setManA(r.getValue(count1));
					questionAttr.setManB(r.getValue(count2));
					questionAttr.setManC(r.getValue(count3));
					questionAttr.setManD(r.getValue(count4));
					questionAttr.setManE(r.getValue(count5));
					Integer count6 = r.getValue(count1) + r.getValue(count2) + r.getValue(count3) + r.getValue(count4) + r.getValue(count5);
					questionAttr.setManZ(count6);
					Float a = 0.00f;
					Float b = 0.00f;
					Float c = 0.00f;
					Float d = 0.00f;
					Float e = 0.00f;
					Float z = 0.00f;
					if (r.getValue(count1) != 0 && count6 != 0) {
						a = ((float) r.getValue(count1) / (float) count6) * 100;
						a = (float)(Math.round(a*100))/100;
					}
					questionAttr.setManAF(df.format(a));
					if (r.getValue(count2) != 0 && count6 != 0) {
						b = ((float) r.getValue(count2) / (float) count6) * 100;
						b = (float)(Math.round(b*100))/100;
					}
					questionAttr.setManBF(df.format(b));
					if (r.getValue(count3) != 0 && count6 != 0) {
						c = ((float) r.getValue(count3) / (float) count6) * 100;
						c = (float)(Math.round(c*100))/100;
					}
					questionAttr.setManCF(df.format(c));
					if (r.getValue(count4) != 0 && count6 != 0) {
						d = ((float) r.getValue(count4) / (float) count6) * 100;
						d = (float)(Math.round(d*100))/100;
					}
					questionAttr.setManDF(df.format(d));
					if (r.getValue(count5) != 0 && count6 != 0) {
						e = ((float) r.getValue(count5) / (float) count6) * 100;
						e = (float)(Math.round(e*100))/100;
					}
					questionAttr.setManEF(df.format(e));
					if (count6 != 0) {
						if (r.getValue(count1) != 0 && r.getValue(count2) != 0) {
							z = (((float) (r.getValue(count1) + r.getValue(count2))) / (float) count6) * 100;
							z = (float)(Math.round(z*100))/100;
							questionAttr.setManZF(df.format(z));
						} else if (r.getValue(count1) != 0 && r.getValue(count2) == 0) {
							z = ((float) r.getValue(count1) / (float) count6) * 100;
							z = (float)(Math.round(z*100))/100;
							questionAttr.setManZF(df.format(z));
						} else if (r.getValue(count1) == 0 && r.getValue(count2) != 0) {
							z = ((float) r.getValue(count2) / (float) count6) * 100;
							z = (float)(Math.round(z*100))/100;
							questionAttr.setManZF(df.format(z));
						} else {
							questionAttr.setManZF(df.format(z));
						}
					} else {
						questionAttr.setManZF(df.format(z));
					}
					return questionAttr;
    	       }).stream()
    	       .collect(Collectors.toMap(QuestionAttr::getQuestionContent, m -> m)));


		    List<QuestionAttr> mapValuesList = new ArrayList<QuestionAttr>(questionAttrMap.values());
		    mapValuesList.sort((lec1, lec2) -> lec1.getQuestionOrder().compareTo(lec2.getQuestionOrder()));

			return mapValuesList;
		} else {
			return null;
		}*/
    DecimalFormat df = new DecimalFormat("0.00");
    // TODO Auto-generated method stub

    ClassInfo classInfo = classInfoDao.get(classId);
    if (classInfo != null) {
      Field<Integer> count1 = DSL.choose(RESEARCH_ANSWER_RECORD.ANSWER)
          .when("0", RESEARCH_ANSWER_RECORD.ID).count()
          .as("count1");

      Field<Integer> count2 = DSL.choose(RESEARCH_ANSWER_RECORD.ANSWER)
          .when("1", RESEARCH_ANSWER_RECORD.ID).count()
          .as("count2");

      Field<Integer> count3 = DSL.choose(RESEARCH_ANSWER_RECORD.ANSWER)
          .when("2", RESEARCH_ANSWER_RECORD.ID).count()
          .as("count3");

      Field<Integer> count4 = DSL.choose(RESEARCH_ANSWER_RECORD.ANSWER)
          .when("3", RESEARCH_ANSWER_RECORD.ID).count()
          .as("count4");
      Field<Integer> count5 = DSL.choose(RESEARCH_ANSWER_RECORD.ANSWER)
          .when("4", RESEARCH_ANSWER_RECORD.ID).count()
          .as("count5");

      List<String> rIds = new ArrayList<String>();
      String[] qId = {"1", "2", "3", "4", "5", "6", "7", "8", "9", "10"};
      List<String> qIds = java.util.Arrays.asList(qId);
      List<String> rcIds = new ArrayList<String>();

      List<ResearchQuestionary> list = researchQuestionaryDao.execute(d -> {
        Table<Record1<String>> basic = (d.select(RESEARCH_QUESTIONARY.ID)
            .from(RESEARCH_QUESTIONARY)
            .leftJoin(CLASS_INFO).on(CLASS_INFO.ID.eq(RESEARCH_QUESTIONARY.CLASS_ID))
            .leftJoin(PROJECT).on(PROJECT.ID.eq(CLASS_INFO.PROJECT_ID))
            .where(CLASS_INFO.DELETE_FLAG.eq(ClassInfo.DELETE_FLASE))
            .and(CLASS_INFO.IMPLEMENTATION_YEAR.eq(classInfo.getImplementationYear()))
            .and(CLASS_INFO.IMPLEMENTATION_MONTH.eq(classInfo.getImplementationMonth()))
            .and(PROJECT.DELETE_FLAG.eq(ClassInfo.DELETE_FLASE))
            .and(RESEARCH_QUESTIONARY.TYPE.eq(4))
        ).asTable("b");

        return d.select(Fields.start()
            .add(RESEARCH_QUESTIONARY.ID)
            .end())
            .from(RESEARCH_QUESTIONARY)
            .innerJoin(basic).on(basic.field(RESEARCH_QUESTIONARY.ID).eq(RESEARCH_QUESTIONARY.ID))
            .fetch(r -> {
              ResearchQuestionary cf = r.into(ResearchQuestionary.class);
              rIds.add(r.getValue(RESEARCH_QUESTIONARY.ID));
              return cf;
            });
      });

      Map<String, ResearchRecord> researchRecordMap = researchRecordDao.execute(
          x -> x.select(RESEARCH_RECORD.ID).from(RESEARCH_RECORD)
              .where(RESEARCH_RECORD.RESEARCH_QUESTIONARY_ID.in(rIds)))
          .fetch(r -> {
            ResearchRecord cf = r.into(ResearchRecord.class);
            rcIds.add(r.getValue(RESEARCH_RECORD.ID));
            return cf;
          }).stream()
          .collect(Collectors.toMap(ResearchRecord::getId, m -> m));

      Map<String, QuestionAttr> questionAttrMap = questionAttrDao.execute(x ->
          x.select(QUESTION.ORDER, QUESTION.CONTENT, count1, count2, count3, count4, count5,
              RESEARCH_ANSWER_RECORD.ANSWER.count())
              .from(QUESTION_ATTR)
              .leftJoin(QUESTION)
              .on(QUESTION.ID.eq(QUESTION_ATTR.QUESTION_ID))
              .leftJoin(RESEARCH_ANSWER_RECORD.forceIndex("ids_t_research_answer_record_index_f_question_id"))
              .on(RESEARCH_ANSWER_RECORD.ANSWER.eq(QUESTION_ATTR.NAME)
                  .and(RESEARCH_ANSWER_RECORD.QUESTION_ID.eq(QUESTION.ID)))
//		.where(RESEARCH_ANSWER_RECORD.RESEARCH_RECORD_ID.in(rIds))
              .where(RESEARCH_ANSWER_RECORD.QUESTION_ID.in(qIds)
                  .and(RESEARCH_ANSWER_RECORD.RESEARCH_RECORD_ID.in(rcIds)))
              .groupBy(QUESTION.CONTENT).orderBy(QUESTION.ORDER.asc())
              .fetch(r -> {
                QuestionAttr questionAttr = new QuestionAttr();
                questionAttr.setQuestionContent(r.getValue(QUESTION.CONTENT));
                questionAttr.setQuestionOrder(r.getValue(QUESTION.ORDER));
                questionAttr.setManA(r.getValue(count1));
                questionAttr.setManB(r.getValue(count2));
                questionAttr.setManC(r.getValue(count3));
                questionAttr.setManD(r.getValue(count4));
                questionAttr.setManE(r.getValue(count5));
                Integer count6 = r.getValue(count1) + r.getValue(count2) + r.getValue(count3) + r
                    .getValue(count4) + r.getValue(count5);
                questionAttr.setManZ(count6);
                return questionAttr;
              })
              .stream()
              .collect(Collectors.toMap(QuestionAttr::getQuestionContent, m -> m)));

      List<QuestionAttr> mapValuesList = new ArrayList<QuestionAttr>(questionAttrMap.values());
      mapValuesList
          .sort((lec1, lec2) -> lec1.getQuestionOrder().compareTo(lec2.getQuestionOrder()));
      List<QuestionAttr> list2 = new ArrayList<>();

      mapValuesList.forEach(r -> {
        QuestionAttr questionAttr = new QuestionAttr();
        questionAttr.setQuestionContent(r.getQuestionContent());
        questionAttr.setManA(r.getManA());
        questionAttr.setManB(r.getManB());
        questionAttr.setManC(r.getManC());
        questionAttr.setManD(r.getManD());
        questionAttr.setManE(r.getManE());
//			Integer count6 = r.getManZ() + r.getManB() + r.getManC() + r.getManD() + r.getManE();
        Integer count6 = r.getManZ();
        questionAttr.setManZ(r.getManZ());
        Float a = 0.00f;
        Float b = 0.00f;
        Float c = 0.00f;
        Float d = 0.00f;
        Float e = 0.00f;
        Float z = 0.00f;
        if (r.getManA() != 0 && count6 != 0) {
          a = ((float) r.getManA() / (float) count6) * 100;
          a = (float) (Math.round(a * 100)) / 100;
        }
        questionAttr.setManAF(df.format(a));
        if (r.getManB() != 0 && count6 != 0) {
          b = ((float) r.getManB() / (float) count6) * 100;
          b = (float) (Math.round(b * 100)) / 100;
        }
        questionAttr.setManBF(df.format(b));
        if (r.getManC() != 0 && count6 != 0) {
          c = ((float) r.getManC() / (float) count6) * 100;
          c = (float) (Math.round(c * 100)) / 100;
        }
        questionAttr.setManCF(df.format(c));
        if (r.getManD() != 0 && count6 != 0) {
          d = ((float) r.getManD() / (float) count6) * 100;
          d = (float) (Math.round(d * 100)) / 100;
        }
        questionAttr.setManDF(df.format(d));
        if (r.getManE() != 0 && count6 != 0) {
          e = ((float) r.getManE() / (float) count6) * 100;
          e = (float) (Math.round(e * 100)) / 100;
        }
        questionAttr.setManEF(df.format(e));
        if (count6 != 0) {
          if (r.getManA() != 0 && r.getManB() != 0) {
            z = (((float) (r.getManA() + r.getManB())) / (float) count6) * 100;
            z = (float) (Math.round(z * 100)) / 100;
            questionAttr.setManZF(df.format(z));
          } else if (r.getManA() != 0 && r.getManB() == 0) {
            z = ((float) r.getManA() / (float) count6) * 100;
            z = (float) (Math.round(z * 100)) / 100;
            questionAttr.setManZF(df.format(z));
          } else if (r.getManA() == 0 && r.getManB() != 0) {
            z = (float) (Math.round(z * 100)) / 100;
            z = ((float) r.getManB() / (float) count6) * 100;
            questionAttr.setManZF(df.format(z));
          } else {
            questionAttr.setManZF(df.format(z));
          }
        } else {
          questionAttr.setManZF(df.format(z));
        }
        list2.add(questionAttr);
      });
      return list2;
    } else {
      return null;
    }

  }

  public static long dateToLong(Date date) {
    return date.getTime();
  }

  @Override
  public List<QuestionAttr> findManYiC(String classId) {
    // TODO Auto-generated method stub
//		List<GroupConfigurationValue> glist = groupConfigurationValueDao.execute(e -> {
//			List<GroupConfigurationValue> all = e.selectFrom(GROUP_CONFIGURATION_VALUE)
//					.fetch()
//					.into(GroupConfigurationValue.class);
//			return all;
//		});

//		com.zxy.product.train.jooq.tables.Organization org2 = ORGANIZATION.as("org2");
//		Field<String> org2Name = org2.NAME.as("org2Name");
    List<QuestionAttr> list = researchQuestionaryDao
        .execute(x -> x.select(Fields.start()
            .add(QUESTION.CONTENT)
            .add(RESEARCH_ANSWER_RECORD.ANSWER)
            .add(TRAINEE.PHONE_NUMBER)
            .add(MEMBER.FULL_NAME)
            .add(DIMENSION_QUESTION.ORDER)
            .add(TRAINEE.NEW_COMPANY)
            .end()))
        .from(RESEARCH_QUESTIONARY).leftJoin(DIMENSION)
        .on(RESEARCH_QUESTIONARY.ID.eq(DIMENSION.RESEARCH_QUESTIONARY_ID))
        .leftJoin(DIMENSION_QUESTION).on(DIMENSION_QUESTION.DIMENSION_ID.eq(DIMENSION.ID))
        .leftJoin(QUESTION)
        .on(DIMENSION_QUESTION.QUESTION_ID.eq(QUESTION.ID)).leftJoin(QUESTION_ATTR)
        .on(QUESTION_ATTR.QUESTION_ID.eq(QUESTION.ID)).leftJoin(RESEARCH_RECORD)
        .on(RESEARCH_RECORD.RESEARCH_QUESTIONARY_ID.eq(RESEARCH_QUESTIONARY.ID)
            .and(RESEARCH_RECORD.STATUS.eq(1)))
        .leftJoin(RESEARCH_ANSWER_RECORD)
        .on(RESEARCH_ANSWER_RECORD.QUESTION_ID.eq(QUESTION.ID)
            .and(RESEARCH_ANSWER_RECORD.RESEARCH_RECORD_ID.eq(RESEARCH_RECORD.ID)))
        .leftJoin(TRAINEE)
        .on(TRAINEE.CLASS_ID.eq(RESEARCH_QUESTIONARY.CLASS_ID)
            .and(TRAINEE.MEMBER_ID.eq(RESEARCH_RECORD.MEMBER_ID)))
        .leftJoin(MEMBER)
        .on(TRAINEE.MEMBER_ID.eq(MEMBER.ID))
        .where(RESEARCH_QUESTIONARY.TYPE.eq(4)
            .and(RESEARCH_QUESTIONARY.CLASS_ID.eq(classId))
            .and(DIMENSION.ORDER.eq(3))
            .and(TRAINEE.DELETE_FLAG.eq(0))
            .and(TRAINEE.AUDIT_STATUS.eq(1))
            .and(RESEARCH_ANSWER_RECORD.ANSWER.ne(""))
            .and(TRAINEE.TYPE.eq(0)))
        .orderBy(DIMENSION_QUESTION.ORDER)
        .fetch(r -> {
          QuestionAttr questionAttr = new QuestionAttr();
          questionAttr.setQuestionContent(r.getValue(QUESTION.CONTENT));
          questionAttr.setAnswer(r.getValue(RESEARCH_ANSWER_RECORD.ANSWER));
          questionAttr.setmName(r.getValue(MEMBER.FULL_NAME));
          questionAttr.setoName(r.getValue(TRAINEE.NEW_COMPANY));
          questionAttr.setPhone(r.getValue(TRAINEE.PHONE_NUMBER));
          return questionAttr;
        });
//		list.forEach(r -> {
//			String path = r.getPath();
//			String cName = r.getoName();
//			if (glist != null && glist.size() > 0) {
//				for (int i = 0; i < glist.size(); i++) {
//					if (path!=null&&path.contains(glist.get(i).getPath())) {
//						r.setoName(glist.get(i).getShortName());
//						break;
//					} else {
//						r.setoName(cName);
//					}
//				}
//			} else {
//				r.setoName(cName);
//			}
//		});
    return list;
  }

  @Override
  public List<QuestionAttr> findManYiCDW(String classId) {
//		// TODO Auto-generated method stub
//		List<GroupConfigurationValue> glist = groupConfigurationValueDao.execute(e -> {
//			List<GroupConfigurationValue> all = e.selectFrom(GROUP_CONFIGURATION_VALUE)
//					.fetch()
//					.into(GroupConfigurationValue.class);
//			return all;
//		});

    List<QuestionAttr> list = researchQuestionaryDao
        .execute(x -> x.selectDistinct(Fields.start()
            .add(QUESTION.CONTENT)
            .add(RESEARCH_ANSWER_RECORD.ANSWER)
            .add(RESEARCH_ANSWER_RECORD.IDEA)
            .add(TRAINEE.PHONE_NUMBER)
            .add(MEMBER.FULL_NAME)
            .add(DIMENSION_QUESTION.ORDER)
            .add(DIMENSION.ORDER)
            .add(TRAINEE.NEW_COMPANY)
            .end()))
        .from(RESEARCH_QUESTIONARY)
        .leftJoin(DIMENSION)
        .on(RESEARCH_QUESTIONARY.ID.eq(DIMENSION.RESEARCH_QUESTIONARY_ID))
        .leftJoin(DIMENSION_QUESTION).on(DIMENSION_QUESTION.DIMENSION_ID.eq(DIMENSION.ID))
        .leftJoin(QUESTION)
        .on(DIMENSION_QUESTION.QUESTION_ID.eq(QUESTION.ID))
        .leftJoin(QUESTION_ATTR)
        .on(QUESTION_ATTR.QUESTION_ID.eq(QUESTION.ID))
        .leftJoin(RESEARCH_RECORD)
        .on(RESEARCH_RECORD.RESEARCH_QUESTIONARY_ID.eq(RESEARCH_QUESTIONARY.ID)
            .and(RESEARCH_RECORD.STATUS.eq(1)))
        .leftJoin(RESEARCH_ANSWER_RECORD)
        .on(RESEARCH_ANSWER_RECORD.QUESTION_ID.eq(QUESTION.ID)
            .and(RESEARCH_ANSWER_RECORD.RESEARCH_RECORD_ID.eq(RESEARCH_RECORD.ID)))
        .leftJoin(TRAINEE)
        .on(TRAINEE.CLASS_ID.eq(RESEARCH_QUESTIONARY.CLASS_ID)
            .and(TRAINEE.MEMBER_ID.eq(RESEARCH_RECORD.MEMBER_ID)))
        .leftJoin(MEMBER)
        .on(TRAINEE.MEMBER_ID.eq(MEMBER.ID))
        .where(RESEARCH_QUESTIONARY.TYPE.eq(4)
            .and(RESEARCH_QUESTIONARY.CLASS_ID.eq(classId))
            .and(TRAINEE.DELETE_FLAG.eq(0))
            .and(TRAINEE.AUDIT_STATUS.eq(1))
            .and(TRAINEE.TYPE.eq(0))
            .and(RESEARCH_ANSWER_RECORD.ANSWER.ne(""))
        )
        .orderBy(DIMENSION_QUESTION.ORDER.asc(), QUESTION_ATTR.NAME.asc())
        .fetch(r -> {
          QuestionAttr questionAttr = new QuestionAttr();
          questionAttr.setQuestionContent(r.getValue(QUESTION.CONTENT));
          questionAttr.setAnswer(r.getValue(RESEARCH_ANSWER_RECORD.ANSWER));
          questionAttr.setIdea(r.getValue(RESEARCH_ANSWER_RECORD.IDEA));
          questionAttr.setmName(r.getValue(MEMBER.FULL_NAME));
          questionAttr.setoName(r.getValue(TRAINEE.NEW_COMPANY));
          questionAttr.setPhone(r.getValue(TRAINEE.PHONE_NUMBER));
          questionAttr.setQuestionOrder(r.getValue(DIMENSION.ORDER));
          return questionAttr;
        });
    List<QuestionAttr> qaTwo = new ArrayList<QuestionAttr>();
    List<QuestionAttr> qaTwo2 = new ArrayList<QuestionAttr>();
    if (list != null && list.size() > 0) {
      for (int i = 0; i < list.size(); i++) {
        if (list.get(i).getQuestionOrder() != 3 && (list.get(i).getAnswer().equals("2") ||
            list.get(i).getAnswer().equals("3") || list.get(i).getAnswer().equals("4"))) {
          QuestionAttr a = list.get(i);
          a.setSort(Integer.valueOf(list.get(i).getAnswer()));
          a.setAnswer(list.get(i).getIdea());
          qaTwo.add(a);
        } else if (list.get(i).getQuestionOrder() == 3) {
          QuestionAttr a = list.get(i);
          if (list.get(i).getQuestionContent().contains("价值")) {
            a.setSort(5);
          } else if (list.get(i).getQuestionContent().contains("意见")) {
            a.setSort(7);
          } else {
            a.setSort(6);
          }
          qaTwo2.add(list.get(i));
        }
      }
    }
    qaTwo.sort((label1, label2) -> {
      if (label1.getQuestionContent().equals(label2.getQuestionContent())) {
        return label1.getSort().compareTo(label2.getSort());
      }
      return label1.getQuestionContent().compareTo(label2.getQuestionContent());
    });
    qaTwo.addAll(qaTwo2);
//		qaTwo.forEach(r -> {
//			String path = r.getPath();
//			String cName = r.getoName();
//			if (glist != null && glist.size() > 0) {
//				for (int i = 0; i < glist.size(); i++) {
//					if (path!=null&&path.contains(glist.get(i).getPath())) {
//						r.setoName(glist.get(i).getShortName());
//						break;
//					} else {
//						r.setoName(cName);
//					}
//				}
//			} else {
//				r.setoName(cName);
//			}
//		});
    return qaTwo;
  }

  private BigDecimal Integer(String answer) {
    // TODO Auto-generated method stub
    return null;
  }

  @Override
  public int insert(String classId, Optional<Long> startTime, Optional<Long> endTime,
      Optional<String> detail) {
    // TODO Auto-generated method stub
    Optional<ResearchQuestionary> rQuestionary = researchQuestionaryDao.execute(x -> {
      return x.selectFrom(RESEARCH_QUESTIONARY)
          .where(RESEARCH_QUESTIONARY.CLASS_ID.eq(classId), RESEARCH_QUESTIONARY.TYPE.eq(4))
          .fetchOptionalInto(ResearchQuestionary.class);
    });
    Optional<ClassInfo> cInfo = classInfoDao.execute(x -> {
      return x.selectFrom(CLASS_INFO).where(CLASS_INFO.ID.eq(classId))
          .fetchOptionalInto(ClassInfo.class);
    });
    Optional<ClassEvaluate> cEvaluate = classEvaluateDao.execute(x -> {
      return x.selectFrom(CLASS_EVALUATE)
          .where(CLASS_EVALUATE.CLASS_ID.eq(classId), CLASS_EVALUATE.TYPE.eq(4),
              CLASS_EVALUATE.DELETE_FLAG.eq(0)).fetchOptionalInto(ClassEvaluate.class);
    });
    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
    Long sTime = null;
    Long eTime = null;
    String detailM = null;
    if (cInfo.isPresent()) {
      ClassInfo findClassAndProjectByClassId = classInfoService
          .findClassAndProjectByClassId(cInfo.get().getId());
      Long t = cInfo.get().getReturnDate();
      sTime = t - 86400000;
      eTime = sTime + 604800000;
      String ad = sdf.format(new Date(sTime));
      String rd = sdf.format(new Date(eTime));
      String arrDate = ad.replace('-', '.');
      String retDate = rd.replaceAll("-", ".");
      String name = "";
      if (findClassAndProjectByClassId != null) {
        name = findClassAndProjectByClassId.getClassName();
      }
      detailM = "亲爱的学员:<br>" +
          "为了了解您对本次培训项目的意见，帮助公司持续优化提升培训质量，请您评价如下各项指标，并填写文字建议。谢谢您的大力支持！<br>" +
          "培训项目：" + name + "    计划时间" + arrDate + "至" + retDate;
    }
    if (rQuestionary.isPresent()) {
      researchQuestionaryDao.delete(rQuestionary.get().getId());
    }
    if (cEvaluate.isPresent()) {
      classEvaluateDao.delete(cEvaluate.get().getId());
    }
    List<ClassOfflineCourse> findByClassId = offlineCourseService
        .findByClassId(classId, Optional.empty(), Optional.empty(), Optional.of(1));
    ResearchQuestionary researchQuestionary = new ResearchQuestionary();
    researchQuestionary.forInsert();
    researchQuestionary.setClassId(classId);
    researchQuestionary.setType(4);
    researchQuestionary.setAnswerPaperRule(ResearchQuestionary.ANSWER_PAPER_RULE);
    researchQuestionary.setName(ResearchQuestionary.NAME);
    if (startTime.isPresent() && endTime.isPresent()) {
      startTime.ifPresent(researchQuestionary::setStartTime);
      endTime.ifPresent(researchQuestionary::setEndTime);
    } else if (sTime != null && eTime != null) {
      researchQuestionary.setStartTime(sTime);
      researchQuestionary.setEndTime(eTime);
    }
    if (detail.isPresent()) {
      detail.ifPresent(researchQuestionary::setQuestionaryDetail);
    } else if (detailM != null) {
      researchQuestionary.setQuestionaryDetail(detailM);
    }
    researchQuestionaryDao.insert(researchQuestionary);

    ClassEvaluate evaluate = new ClassEvaluate();
    evaluate.forInsert();
    evaluate.setClassId(classId);
    evaluate.setType(4);
    evaluate.setResourceId(researchQuestionary.getId());
    evaluate.setResourceName(ResearchQuestionary.NAME);
    evaluate.setDeleteFlag(0);
    if (startTime.isPresent() && endTime.isPresent()) {
      startTime.ifPresent(evaluate::setStartTime);
      endTime.ifPresent(evaluate::setEndTime);
    } else if (sTime != null && eTime != null) {
      evaluate.setStartTime(sTime);
      evaluate.setEndTime(eTime);
    }
    classEvaluateDao.insert(evaluate);
    List<Dimension> dimensionList = new ArrayList<Dimension>();
    Dimension dimension1 = new Dimension();
    Dimension dimension2 = new Dimension();
    Dimension dimension3 = new Dimension();
    dimension1.forInsert();
    dimension2.forInsert();
    dimension3.forInsert();
    dimension1.setName("总体评估");
    dimension2.setName("课程师资评估");
    dimension3.setName("开放性问卷");
    dimension1.setOrder(1);
    dimension2.setOrder(2);
    dimension3.setOrder(3);
    dimension1.setResearchQuestionaryId(researchQuestionary.getId());
    dimension2.setResearchQuestionaryId(researchQuestionary.getId());
    dimension3.setResearchQuestionaryId(researchQuestionary.getId());
    dimensionList.add(dimension1);
    dimensionList.add(dimension2);
    dimensionList.add(dimension3);
    dimensionDao.insert(dimensionList);
    List<Question> questionList = new ArrayList<Question>();
    if (findByClassId != null && findByClassId.size() > 0) {
      for (ClassOfflineCourse d : findByClassId) {
        Question question = new Question();
        question.forInsert();
        question.setContent(d.getName() + ",课程讲授技巧或过程控制 ");
        question.setType(1);
        question.setClassOfflineCourseId(d.getId());
        question.setQuestionDepotId(ResearchQuestionary.RESEARCH_QUESTIONARY_QUESTION_DEPOT_ID);
        questionList.add(question);
      }
      questionDao.insert(questionList);
    }
    List<QuestionAttr> questionAttrList = new ArrayList<QuestionAttr>();
    List<DimensionQuestion> dimensionQuestionList = new ArrayList<DimensionQuestion>();
    List<DimensionQuestion> dimensionQuestionList2 = new ArrayList<DimensionQuestion>();
    Integer index1 = null;
    for (int i = 0; i <= 9; i++) {
      index1 = i + 1;
      DimensionQuestion dimensionQuestion = new DimensionQuestion();
      dimensionQuestion.forInsert();
      dimensionQuestion.setDimensionId(dimension1.getId());
      dimensionQuestion.setQuestionId(index1.toString());
      dimensionQuestionList2.add(dimensionQuestion);
    }
    for (int i = 0; i <= 2; i++) {
      DimensionQuestion dimensionQuestion = new DimensionQuestion();
      dimensionQuestion.forInsert();
      dimensionQuestion.setDimensionId(dimension3.getId());
      if (i == 0) {
        dimensionQuestion.setQuestionId("11");
      } else if (i == 1) {
        dimensionQuestion.setQuestionId("12");
      } else {
        dimensionQuestion.setQuestionId("13");
      }
      dimensionQuestionList2.add(dimensionQuestion);
    }

    if (questionList.size() > 0) {
      for (Question d : questionList) {
        QuestionAttr questionAttr0 = new QuestionAttr();
        QuestionAttr questionAttr1 = new QuestionAttr();
        QuestionAttr questionAttr2 = new QuestionAttr();
        QuestionAttr questionAttr3 = new QuestionAttr();
        DimensionQuestion dimensionQuestion = new DimensionQuestion();
        dimensionQuestion.forInsert();
        dimensionQuestion.setDimensionId(dimension2.getId());
        dimensionQuestion.setQuestionId(d.getId());
        for (int i = 0; i <= 3; i++) {
          if (i == 0) {
            questionAttr0.forInsert();
            questionAttr0.setQuestionId(d.getId());
            questionAttr0.setName("0");
            questionAttr0.setValue("满意");
            questionAttr0.setScore(4);
            questionAttr0.setType(Integer.toString(1));
          } else if (i == 1) {
            questionAttr1.forInsert();
            questionAttr1.setQuestionId(d.getId());
            questionAttr1.setName("1");
            questionAttr1.setValue("基本满意");
            questionAttr1.setScore(3);
            questionAttr1.setType(Integer.toString(1));
          } else if (i == 2) {
            questionAttr2.forInsert();
            questionAttr2.setQuestionId(d.getId());
            questionAttr2.setName("2");
            questionAttr2.setValue("不满意");
            questionAttr2.setScore(2);
            questionAttr2.setType(Integer.toString(1));
          } else if (i == 3) {
            questionAttr3.forInsert();
            questionAttr3.setQuestionId(d.getId());
            questionAttr3.setName("3");
            questionAttr3.setValue("很不满意");
            questionAttr3.setScore(1);
            questionAttr3.setType(Integer.toString(1));
          }
        }
        questionAttrList.add(questionAttr0);
        questionAttrList.add(questionAttr1);
        questionAttrList.add(questionAttr2);
        questionAttrList.add(questionAttr3);
        dimensionQuestionList.add(dimensionQuestion);
      }
      questionAttrDao.insert(questionAttrList);
    }
    dimensionQuestionDao.insert(dimensionQuestionList);
    dimensionQuestionDao.insert(dimensionQuestionList2);
    return 0;
  }

  @Override
  public List<ResearchAnswerRecord> insertResearchAnswerRecords(String memberId,
      String researchQuestionaryId, List<ResearchAnswerRecord> researchAnswerRecords) {

		/*researchAnswerRecordDao.insert(researchAnswerRecords.stream().map(t -> {
			t.forInsert();
			return t;
		}).collect(Collectors.toList()));

		Optional<ResearchRecord> optional = researchRecordDao.fetchOne(RESEARCH_RECORD.MEMBER_ID.eq(memberId), RESEARCH_RECORD.RESEARCH_QUESTIONARY_ID.eq(researchQuestionaryId));

		if (!optional.isPresent()) {
			throw new UnprocessableException(ErrorCode.ResearchRecordError);
		}

		ResearchRecord researchRecord = optional.get();
		researchRecord.setSubmitTime(System.currentTimeMillis());
		researchRecord.setStatus(ResearchRecord.STATUS_FINISHED);
		researchRecord.setScore(countResearchRecordScore(researchAnswerRecords));
		researchRecordDao.update(researchRecord);

		return researchAnswerRecords;*/

    Optional<ResearchRecord> researchRecord = researchRecordDao.fetchOne(
        RESEARCH_RECORD.RESEARCH_QUESTIONARY_ID.eq(researchQuestionaryId)
            .and(RESEARCH_RECORD.MEMBER_ID.eq(memberId)));
//                ).orElseThrow(() -> new UnprocessableException(ErrorCode.ResearchRecordError));
    if (researchRecord.isPresent()) {

      if (ResearchRecord.STATUS_FINISHED != researchRecord.get().getStatus()) { // 已经交过卷了，不能重复交卷
        Map<String, ResearchAnswerRecord> answers = researchAnswerRecords.stream().map(t -> {
          t.forInsert();
          t.setResearchRecordId(researchRecord.get().getId());
          return t;
        }).collect(Collectors.toMap(ResearchAnswerRecord::getQuestionId, e -> e, (t1, t2) -> t2));
        List<ResearchAnswerRecord> answerRecords = new ArrayList<>(answers.values());
        researchRecordDao.execute(e -> {
          return e.update(RESEARCH_RECORD)
              .set(RESEARCH_RECORD.STATUS, ResearchRecord.STATUS_FINISHED)
              .where(RESEARCH_RECORD.ID.eq(researchRecord.get().getId()))
              .execute();
        });
        researchAnswerRecordDao.insert(answerRecords);
      }
    } else {
      ResearchRecord r = new ResearchRecord();
      r.forInsert();
      r.setMemberId(memberId);
      r.setResearchQuestionaryId(researchQuestionaryId);
      researchRecordDao.insert(r);

      Map<String, ResearchAnswerRecord> answers = researchAnswerRecords.stream().map(t -> {
        t.forInsert();
        t.setResearchRecordId(r.getId());
        return t;
      }).collect(Collectors.toMap(ResearchAnswerRecord::getQuestionId, e -> e, (t1, t2) -> t2));
      List<ResearchAnswerRecord> answerRecords = new ArrayList<>(answers.values());
      researchRecordDao.execute(e -> {
        return e.update(RESEARCH_RECORD)
            .set(RESEARCH_RECORD.STATUS, ResearchRecord.STATUS_FINISHED)
            .where(RESEARCH_RECORD.ID.eq(r.getId()))
            .execute();
      });
      researchAnswerRecordDao.insert(answerRecords);
    }
    messageSender
        .send(MessageTypeContent.SUBMIT_SATISFACTION_QUESTIONNAIRE, MessageHeaderContent.MEMBERID,
            memberId);

    return researchAnswerRecords;

  }

  @Override
  public List<ResearchAnswerRecord> insertSatisfiedResearchAnswerRecords(String memberId,
      String researchQuestionaryId, List<ResearchAnswerRecord> researchAnswerRecords,
      String classId) {

		/*researchAnswerRecordDao.insert(researchAnswerRecords.stream().map(t -> {
			t.forInsert();
			return t;
		}).collect(Collectors.toList()));

		Optional<ResearchRecord> optional = researchRecordDao.fetchOne(RESEARCH_RECORD.MEMBER_ID.eq(memberId), RESEARCH_RECORD.RESEARCH_QUESTIONARY_ID.eq(researchQuestionaryId));

		if (!optional.isPresent()) {
			throw new UnprocessableException(ErrorCode.ResearchRecordError);
		}

		ResearchRecord researchRecord = optional.get();
		researchRecord.setSubmitTime(System.currentTimeMillis());
		researchRecord.setStatus(ResearchRecord.STATUS_FINISHED);
		researchRecord.setScore(countResearchRecordScore(researchAnswerRecords));
		researchRecordDao.update(researchRecord);

		return researchAnswerRecords;*/

    Optional<ResearchRecord> researchRecord = researchRecordDao.fetchOne(
        RESEARCH_RECORD.RESEARCH_QUESTIONARY_ID.eq(researchQuestionaryId)
            .and(RESEARCH_RECORD.MEMBER_ID.eq(memberId)));
//                ).orElseThrow(() -> new UnprocessableException(ErrorCode.ResearchRecordError));
    if (researchRecord.isPresent()) {

      if (ResearchRecord.STATUS_FINISHED != researchRecord.get().getStatus()) { // 已经交过卷了，不能重复交卷
        Map<String, ResearchAnswerRecord> answers = researchAnswerRecords.stream().map(t -> {
          t.forInsert();
          t.setResearchRecordId(researchRecord.get().getId());
          return t;
        }).collect(Collectors.toMap(ResearchAnswerRecord::getQuestionId, e -> e, (t1, t2) -> t2));
        List<ResearchAnswerRecord> answerRecords = new ArrayList<>(answers.values());
        researchRecordDao.execute(e -> {
          return e.update(RESEARCH_RECORD)
              .set(RESEARCH_RECORD.STATUS, ResearchRecord.STATUS_FINISHED)
              .where(RESEARCH_RECORD.ID.eq(researchRecord.get().getId()))
              .execute();
        });
        researchAnswerRecordDao.insert(answerRecords);
        Optional<ResearchQuestionary> fetchOne = researchQuestionaryDao
            .fetchOne(RESEARCH_QUESTIONARY.CLASS_ID.eq(classId), RESEARCH_QUESTIONARY.TYPE.eq(4));
        if (fetchOne.isPresent()) {
          messageSender.send(MessageTypeContent.SATISFACTION_QUESTIONARE_STATISTICS,
              MessageHeaderContent.CLASSID, classId);
        }
      }
    } else {
      ResearchRecord r = new ResearchRecord();
      r.forInsert();
      r.setMemberId(memberId);
      r.setResearchQuestionaryId(researchQuestionaryId);
      researchRecordDao.insert(r);

      Map<String, ResearchAnswerRecord> answers = researchAnswerRecords.stream().map(t -> {
        t.forInsert();
        t.setResearchRecordId(r.getId());
        return t;
      }).collect(Collectors.toMap(ResearchAnswerRecord::getQuestionId, e -> e, (t1, t2) -> t2));
      List<ResearchAnswerRecord> answerRecords = new ArrayList<>(answers.values());
      researchRecordDao.execute(e -> {
        return e.update(RESEARCH_RECORD)
            .set(RESEARCH_RECORD.STATUS, ResearchRecord.STATUS_FINISHED)
            .where(RESEARCH_RECORD.ID.eq(r.getId()))
            .execute();
      });
      researchAnswerRecordDao.insert(answerRecords);
      Optional<ResearchQuestionary> fetchOne = researchQuestionaryDao
          .fetchOne(RESEARCH_QUESTIONARY.CLASS_ID.eq(classId), RESEARCH_QUESTIONARY.TYPE.eq(4));
      if (fetchOne.isPresent()) {
        messageSender.send(MessageTypeContent.SATISFACTION_QUESTIONARE_STATISTICS,
            MessageHeaderContent.CLASSID, classId);
      }
    }
    messageSender
        .send(MessageTypeContent.SUBMIT_SATISFACTION_QUESTIONNAIRE, MessageHeaderContent.MEMBERID,
            memberId);

    return researchAnswerRecords;

  }

  @Override
  public Optional<ResearchQuestionary> getSimpleData(String id) {
    return researchQuestionaryDao.getOptional(id);
  }

  @Override
  public List<Dimension> findDimensionsByResearchId(String researchQuestionaryId) {
    Result<Record> record = dimensionDao.execute(e -> {
      return e.selectDistinct(
          Fields.start()
              .add(DIMENSION)
              .add(QUESTION)
              .add(QUESTION_ATTR)
              .add(DIMENSION_QUESTION)
              .end()
      )
          .from(DIMENSION)
          .leftJoin(DIMENSION_QUESTION).on(DIMENSION.ID.eq(DIMENSION_QUESTION.DIMENSION_ID))
          .leftJoin(QUESTION).on(DIMENSION_QUESTION.QUESTION_ID.eq(QUESTION.ID))
          .leftJoin(QUESTION_ATTR).on(QUESTION_ATTR.QUESTION_ID.eq(QUESTION.ID))
          .leftJoin(RESEARCH_QUESTIONARY)
          .on(DIMENSION.RESEARCH_QUESTIONARY_ID.eq(RESEARCH_QUESTIONARY.ID))
          .where(
              DIMENSION.RESEARCH_QUESTIONARY_ID.eq(researchQuestionaryId),
              QUESTION.QUESTION_DEPOT_ID
                  .eq(ResearchQuestionary.RESEARCH_QUESTIONARY_QUESTION_DEPOT_ID)
          )
          .orderBy(DIMENSION.ORDER.asc(), DIMENSION_QUESTION.ORDER.asc())
          .fetch();
    });

    Map<String, QuestionAttr> questionAttrMapKeyById = record.into(QUESTION_ATTR)
        .into(QuestionAttr.class).stream()
        .filter(e -> e.getId() != null)
        .collect(Collectors.toMap(QuestionAttr::getId, e -> e, (t1, t2) -> t2));

    Map<String, List<QuestionAttr>> questionAttrListMapKeyByQuestionId = findAttrsByQuestionId(
        questionAttrMapKeyById.values());

    Map<String, Question> questionMapKeyById = record.into(QUESTION).into(Question.class).stream()
        .filter(e -> e.getId() != null)
        .collect(Collectors.toMap(Question::getId, e -> e, (t1, t2) -> t2));

    Map<String, DimensionQuestion> dimensionQuestionMapKeyById = record.into(DIMENSION_QUESTION)
        .into(DimensionQuestion.class).stream()
        .filter(e -> e.getId() != null)
        .collect(Collectors.toMap(DimensionQuestion::getId, e -> e, (t1, t2) -> t2));

    Map<String, List<Question>> questionListMapKeyByDimensionId = findQuestionsByDimension(
        questionAttrListMapKeyByQuestionId, questionMapKeyById, dimensionQuestionMapKeyById);

    Map<String, Dimension> dimensionMapKeyById = record.into(DIMENSION).into(Dimension.class)
        .stream()
        .filter(e -> e.getId() != null)
        .collect(Collectors.toMap(Dimension::getId, e -> e, (t1, t2) -> t2));

    List<Dimension> dimensions = dimensionMapKeyById.values().stream().map(t -> {
      t.setQuestions(questionListMapKeyByDimensionId.get(t.getId()));
      return t;
    }).collect(Collectors.toList());

    return dimensions;
  }

  @Override
  public List<Dimension> dimensionsByResearchId(String researchQuestionaryId) {
    return dimensionDao.execute(x -> {
      return x.select(Fields.start().add(DIMENSION).end())
          .from(DIMENSION)
          .where(DIMENSION.RESEARCH_QUESTIONARY_ID.eq(researchQuestionaryId))
          .orderBy(DIMENSION.ORDER.asc()).fetch().into(Dimension.class);
    });
  }

  private Map<String, List<QuestionAttr>> findAttrsByQuestionId(
      Collection<QuestionAttr> questionAttrs) {
    Map<String, List<QuestionAttr>> map = new HashMap<>();
    questionAttrs.stream().forEach(e -> {
      if (map.get(e.getQuestionId()) == null) {
        map.put(e.getQuestionId(), new ArrayList<>());
      }
      map.get(e.getQuestionId()).add(e);
    });
    return map;
  }

  private Map<String, List<Question>> findQuestionsByDimension(
      Map<String, List<QuestionAttr>> questionAttrListMap,
      Map<String, Question> questionMap,
      Map<String, DimensionQuestion> dimensionQuestionMap) {

    Map<String, List<Question>> map = new HashMap<>();
    dimensionQuestionMap.values().stream().forEach(t -> {
      if (map.get(t.getDimensionId()) == null) {
        map.put(t.getDimensionId(), new ArrayList<>());
      }
      Question question = questionMap.get(t.getQuestionId());
      question.setDimensionId(t.getDimensionId());
      question.setOrder(questionMap.get(t.getQuestionId()).getOrder());
      question.setQuestionAttrs(questionAttrListMap.get(question.getId()));
      map.get(t.getDimensionId()).add(question);
    });
    return map;
  }

  @Override
  public List<ResearchAnswerRecordMap> findRecordsByResearchQuestionaryId(
      String researchQuestionaryId) {
    List<ResearchRecord> researchRecords = researchRecordDao
        .fetch(RESEARCH_RECORD.RESEARCH_QUESTIONARY_ID.eq(researchQuestionaryId));
    List<ResearchAnswerRecord> researchAnswerRecords =
        researchAnswerRecordDao.fetch(RESEARCH_ANSWER_RECORD.RESEARCH_RECORD_ID.in(
            researchRecords.stream().map(ResearchRecord::getId).collect(Collectors.toList())));

    return researchRecords.stream().map(t -> {
      ResearchAnswerRecordMap map = new ResearchAnswerRecordMap();
      map.setResearchRecordId(t.getId());
      map.setResearchAnswerRecords(findResearchAnswerRecords(t.getId(), researchAnswerRecords));
      return map;
    }).collect(Collectors.toList());
  }

  private List<ResearchAnswerRecord> findResearchAnswerRecords(String researchRecordId,
      List<ResearchAnswerRecord> researchAnswerRecords) {
    return researchAnswerRecords.stream()
        .filter(t -> t.getResearchRecordId().equals(researchRecordId)).collect(Collectors.toList());
  }

  @Override
  public Optional<ResearchQuestionary> getData(String classId) {
    // TODO Auto-generated method stub
    return researchQuestionaryDao
        .fetchOne(RESEARCH_QUESTIONARY.CLASS_ID.eq(classId), RESEARCH_QUESTIONARY.TYPE.eq(4));
  }

  @Override
  public Optional<ResearchQuestionary> getCompetencyData(String classId) {
    // TODO Auto-generated method stub
    return researchQuestionaryDao
        .fetchOne(RESEARCH_QUESTIONARY.CLASS_ID.eq(classId), RESEARCH_QUESTIONARY.TYPE.eq(6));
  }

  @Override
  public ResearchRecord get(String id) {
    // TODO Auto-generated method stub
    ResearchRecord researchRecord = researchRecordDao.get(id);
    researchRecord.setMember(memberDao.get(researchRecord.getMemberId()));
    return researchRecord;
  }

  @Override
  public Optional<ResearchRecord> getResearchRecordByResearchIdAndMemberId(
      String researchQuestionaryId,
      String memberId) {
    // TODO Auto-generated method stub
    return researchRecordDao
        .fetchOne(RESEARCH_RECORD.RESEARCH_QUESTIONARY_ID.eq(researchQuestionaryId),
            RESEARCH_RECORD.MEMBER_ID.eq(memberId));
  }

  @Override
  public ResearchRecord insert(String researchQuestionaryId, String memberId) {
    // TODO Auto-generated method stub
    ResearchRecord researchRecord = new ResearchRecord();
    researchRecord.forInsert();
    researchRecord.setMemberId(memberId);
    researchRecord.setResearchQuestionaryId(researchQuestionaryId);
    researchRecordDao.insert(researchRecord);
    Optional<Member> fetchOne = memberDao.fetchOne(MEMBER.ID.eq(memberId));
    if (fetchOne.isPresent()) {
      researchRecord.setMember(fetchOne.get());
    }
    return researchRecord;
  }

  @Override
  public ResearchRecord getByResearchIdAndMemberId(String researchId, String memberId) {
    // TODO Auto-generated method stub
    Optional<ResearchRecord> record = researchRecordDao
        .fetchOne(RESEARCH_RECORD.RESEARCH_QUESTIONARY_ID.eq(researchId),
            RESEARCH_RECORD.MEMBER_ID.eq(memberId));
    ResearchQuestionary researchQuestionary = researchQuestionaryDao.get(researchId);
    ResearchRecord researchRecord = new ResearchRecord();
    if (record.isPresent()) {
      researchRecord = record.get();
    }
    researchRecord.setResearchQuestionary(researchQuestionary);
    return researchRecord;
  }

  @Override
  public int delete(String classId, Optional<Integer> type) {
    Integer num = 4;
    if (!type.equals(Optional.empty())) {
      num = type.get();
    }
    // TODO Auto-generated method stub
    researchQuestionaryDao
        .delete(RESEARCH_QUESTIONARY.CLASS_ID.eq(classId), RESEARCH_QUESTIONARY.TYPE.eq(num));
    //删除满意度问卷同时更新代办数量
    sender.send(MessageTypeContent.AGENCY_CLASSID, MessageHeaderContent.CLASSID, classId);
    return 0;
  }

  @Override
  public PagedResult<ResearchQuestionary> findResearch(int page, int pageSize, String memberId,
      Optional<String> status) {
    Optional<Long> end = Optional.of(System.currentTimeMillis());
    Field<String> className = PROJECT.NAME.as("className");
    Field<String> recordId = RESEARCH_RECORD.ID.as("recordId");
    Field<Integer> recordStatus = RESEARCH_RECORD.STATUS.as("recordStatus");

    Field<String> classId = RESEARCH_QUESTIONARY.CLASS_ID.as("classId");
    Field<Integer> isPartyCadre =PROJECT.IS_PARTY_CADRE.as("isPartyCadre");
    SelectOnConditionStep<Record> step = researchQuestionaryDao.execute(x -> x
        .select(Fields.start()
            .add(RESEARCH_QUESTIONARY.ID, RESEARCH_QUESTIONARY.TYPE, RESEARCH_QUESTIONARY.NAME,
                CLASS_EVALUATE.END_TIME, CLASS_EVALUATE.START_TIME, className,
                recordId,
                classId,
                recordStatus,
                CLASS_EVALUATE.RELEASE,
                    isPartyCadre)
            .end())
        .from(RESEARCH_QUESTIONARY)
        .innerJoin(RESEARCH_RECORD)
        .on(RESEARCH_RECORD.RESEARCH_QUESTIONARY_ID.eq(RESEARCH_QUESTIONARY.ID).and(RESEARCH_RECORD.MEMBER_ID.eq(memberId)))
        .innerJoin(CLASS_INFO).on(CLASS_INFO.ID.eq(RESEARCH_QUESTIONARY.CLASS_ID))
        .innerJoin(PROJECT).on(PROJECT.ID.eq(CLASS_INFO.PROJECT_ID))
        .innerJoin(CLASS_EVALUATE).on(CLASS_EVALUATE.RESOURCE_ID.eq(RESEARCH_QUESTIONARY.ID))
        .innerJoin(TRAINEE).on(TRAINEE.CLASS_ID.eq(RESEARCH_QUESTIONARY.CLASS_ID))
    );
    Condition c = null;
    //Stream<Optional<Condition>> conditions = null;
    if (status.isPresent()) {
      c = Stream.of(end.map(RESEARCH_QUESTIONARY.END_TIME::gt)).filter(Optional::isPresent)
          .map(Optional::get).reduce((acc, item) -> acc.and(item))
          .orElse(DSL.trueCondition());
    } else {
      c = DSL.trueCondition();
    }
    if (status.isPresent()) {
      List<String> classIdList=getNoCommitQuestionaryClass(memberId);
      SelectConditionStep<Record> condStep = step.where(c.and(TRAINEE.MEMBER_ID.eq(memberId))
          .and(RESEARCH_RECORD.STATUS.isNull()
              .or(RESEARCH_RECORD.STATUS.eq(0)))
              .and(CLASS_INFO.ID.in(classIdList))
          .and(TRAINEE.DELETE_FLAG.eq(0))
          .and(TRAINEE.AUDIT_STATUS.eq(1))
          .and(TRAINEE.TYPE.eq(0))
          .and(RESEARCH_QUESTIONARY.TYPE.eq(ResearchQuestionary.TYPE_NEW_SATISFACTION_QUESTIONARY))
          .and(PROJECT.DELETE_FLAG.eq(0))
          // 联合索引顺序为: DELETE_FLAG,RELEASE,TYPE
          .and(CLASS_EVALUATE.DELETE_FLAG.eq(0))
          .and(CLASS_EVALUATE.RELEASE.eq(1))
          .and(CLASS_EVALUATE.TYPE.eq(ClassEvaluate.TYPE_EVA_STU_NEW))
              .and(RESEARCH_QUESTIONARY.IS_ENSEMBLE.eq(1))
      );
      // 获取行数
      Integer count = researchQuestionaryDao.execute(e -> e.fetchCount(condStep));
      List<ResearchQuestionary> list = condStep.groupBy(CLASS_INFO.ID).having().orderBy(RESEARCH_QUESTIONARY.END_TIME.desc())
          .limit((page - 1) * pageSize, pageSize).fetch(r -> {
            ResearchQuestionary researchQuestionary = new ResearchQuestionary();
            researchQuestionary.setId(r.getValue(RESEARCH_QUESTIONARY.ID));
            researchQuestionary.setType(r.getValue(RESEARCH_QUESTIONARY.TYPE));
            researchQuestionary.setClassName(r.getValue(className));
                Integer isParty=r.getValue(isPartyCadre);
                if(isParty==1){
                  researchQuestionary.setName("满意度问卷(学员)");
                  researchQuestionary.setCommitStatus(0);
                }else{
                  researchQuestionary.setName(r.getValue(RESEARCH_QUESTIONARY.NAME));
                  researchQuestionary.setCommitStatus(r.getValue(recordStatus));
                }
            researchQuestionary.setName(r.getValue(RESEARCH_QUESTIONARY.NAME));
            researchQuestionary.setResearchRecordId(r.getValue(recordId));
            researchQuestionary.setClassId(r.getValue(classId));
            researchQuestionary.setEndTime(r.getValue(RESEARCH_QUESTIONARY.END_TIME));
            researchQuestionary.setStartTime(r.getValue(RESEARCH_QUESTIONARY.START_TIME));
            researchQuestionary.setIsPartyCadre(r.getValue(isPartyCadre));//是否是党干部进修班
            return researchQuestionary;
          });
      return PagedResult.create(count, list);
    } else {
      SelectConditionStep<Record> condStep = step.where(
          c.and(TRAINEE.MEMBER_ID.eq(memberId))
              .and(RESEARCH_QUESTIONARY.TYPE.eq(ResearchQuestionary.TYPE_NEW_SATISFACTION_QUESTIONARY))
              .and(TRAINEE.DELETE_FLAG.eq(0))
              .and(TRAINEE.AUDIT_STATUS.eq(1))
              .and(TRAINEE.TYPE.eq(0))
              .and(CLASS_EVALUATE.DELETE_FLAG.eq(0))
              .and(CLASS_EVALUATE.RELEASE.eq(1))
              .and(CLASS_EVALUATE.TYPE.eq(ClassEvaluate.TYPE_EVA_STU_NEW))
                  .and(RESEARCH_QUESTIONARY.IS_ENSEMBLE.eq(1))
      );
      // 获取行数
      Integer count = researchQuestionaryDao.execute(e -> e.fetchCount(condStep));
      List<ResearchQuestionary> list = condStep.groupBy(CLASS_INFO.ID).orderBy(RESEARCH_QUESTIONARY.END_TIME.desc())
          .limit((page - 1) * pageSize, pageSize).fetch(r -> {
            ResearchQuestionary researchQuestionary = new ResearchQuestionary();
            researchQuestionary.setId(r.getValue(RESEARCH_QUESTIONARY.ID));
            researchQuestionary.setType(r.getValue(RESEARCH_QUESTIONARY.TYPE));
            researchQuestionary.setClassName(r.getValue(className));
            Integer isParty=r.getValue(isPartyCadre);
            if(isParty==1){
              researchQuestionary.setName("满意度问卷(学员)");
              Integer recordStatusNew=existQuestionaryStatus(memberId,r.getValue(classId));
              researchQuestionary.setCommitStatus(recordStatusNew);
            }else{
              researchQuestionary.setName(r.getValue(RESEARCH_QUESTIONARY.NAME));
              researchQuestionary.setCommitStatus(r.getValue(recordStatus));
            }
            researchQuestionary.setResearchRecordId(r.getValue(recordId));
            researchQuestionary.setClassId(r.getValue(classId));
            researchQuestionary.setEndTime(r.getValue(CLASS_EVALUATE.END_TIME));
            researchQuestionary.setStartTime(r.getValue(CLASS_EVALUATE.START_TIME));
            researchQuestionary.setIsPartyCadre(r.getValue(isPartyCadre));//是否是党干部进修班
            return researchQuestionary;
          });
      return PagedResult.create(count, list);
    }
  }

  @Override
  public int save(String memberId, String classId, String researchQuestionaryId) {
    // TODO Auto-generated method stub
    Optional<Trainee> record = traineeDao
        .fetchOne(TRAINEE.CLASS_ID.eq(classId), TRAINEE.MEMBER_ID.eq(memberId),
            TRAINEE.DELETE_FLAG.eq(0), TRAINEE.TYPE.eq(0));
    if (record.isPresent()) {
      Trainee t = record.get();
      t.setCommitQuestionary(1);
      t.setModifyDate(null);
      traineeDao.update(t);
      //发消息，更新学员的待办消息
      messageSender.send(
              MessageTypeContent.SUBMIT_RESEARCH_QUESTIONARY_NEW,MessageHeaderContent.MEMBERID,
              memberId);
    }
    Integer praiseNum = traineeDao
        .execute(y -> y.select(Fields.start().add(TRAINEE.ID.count()).end()).from(TRAINEE)
            .where(TRAINEE.CLASS_ID.eq(classId)).and(TRAINEE.COMMIT_QUESTIONARY.eq(1))
            .and(TRAINEE.TYPE.eq(0))
            .and(TRAINEE.DELETE_FLAG.eq(0))
        ).fetchOne(TRAINEE.ID.count());
    Optional<ClassInfo> classInfo = classInfoDao.getOptional(classId);
    if (classInfo.isPresent()) {
      ClassInfo c = classInfo.get();
      c.setSubmitNum(praiseNum);
      classInfoDao.update(c);
    }
//		messageSender.send(MessageTypeContent.SUBMIT_SATISFACTION_QUESTIONNAIRE, MessageHeaderContent.MEMBERID,memberId);
    return 0;
  }

  @Override
  public ResearchQuestionary update(String classId, Long sTime, Long eTime, Optional<Integer> type,
      Optional<String> name) {
    int num = type.isPresent() ? type.get() : 4;
    //如果是党校培训班，需要更改其他的周问卷信息
    boolean flag=existPartyCadreClass(classId);
    logger.error("更改班级问卷详情，是否是党校培训班={}",flag);
    if(flag){
      List<ResearchQuestionary> researchQuestionaryList=researchQuestionaryDao.execute(dslContext -> {
        return dslContext.select(Fields.start().add(RESEARCH_QUESTIONARY).end())
                .from(RESEARCH_QUESTIONARY)
                .where(RESEARCH_QUESTIONARY.CLASS_ID.eq(classId)
                                .and(RESEARCH_QUESTIONARY.TYPE.eq(num))
                        //.and(RESEARCH_QUESTIONARY.IS_ENSEMBLE.eq(0))
                ).fetchInto(ResearchQuestionary.class);
      });
      logger.error("更改班级问卷详情，周问卷个数={}",researchQuestionaryList==null?0:researchQuestionaryList.size());
      if(researchQuestionaryList!=null&&!researchQuestionaryList.isEmpty()){
        for(ResearchQuestionary research:researchQuestionaryList){
          research.setStartTime(sTime);
          research.setEndTime(eTime);
        }
        researchQuestionaryDao.update(researchQuestionaryList);
        sender.send(MessageTypeContent.AGENCY_CLASSID, MessageHeaderContent.CLASSID, classId);
        Optional<ResearchQuestionary> returnResearch=researchQuestionaryList.stream().filter(x -> x.getIsEnsemble()==1).findFirst();
        return returnResearch.isPresent()?returnResearch.get():null;
      }else{
        return null;
      }
    }else{
      Optional<ResearchQuestionary> fetchOne = researchQuestionaryDao
              .fetchOne(RESEARCH_QUESTIONARY.CLASS_ID.eq(classId), RESEARCH_QUESTIONARY.TYPE.eq(num));
      logger.error("更改班级问卷详情，type={},class_id={},sTime={},eTime={}",num,classId,sTime,eTime);
      if (fetchOne.isPresent()) {
        ResearchQuestionary researchQuestionary = fetchOne.get();
        name.ifPresent(researchQuestionary::setName);
        researchQuestionary.setStartTime(sTime);
        researchQuestionary.setEndTime(eTime);
        researchQuestionaryDao.update(researchQuestionary);
        sender.send(MessageTypeContent.AGENCY_CLASSID, MessageHeaderContent.CLASSID, classId);
        return researchQuestionary;
      } else {
        return null;
      }
    }

  }


  @Override
  public List<Question> attr(String classId) {
    // TODO Auto-generated method stub
    com.zxy.product.train.jooq.tables.Organization org2 = ORGANIZATION.as("org2");
    return researchQuestionaryDao
        .execute(x -> x.select(Fields.start()
            .add(QUESTION)
            .end()))
        .from(QUESTION)
        .leftJoin(DIMENSION_QUESTION)
        .on(DIMENSION_QUESTION.QUESTION_ID.eq(QUESTION.ID))
        .leftJoin(DIMENSION)
        .on(DIMENSION_QUESTION.DIMENSION_ID.eq(DIMENSION.ID))
        .leftJoin(RESEARCH_QUESTIONARY)
        .on(RESEARCH_QUESTIONARY.ID.eq(DIMENSION.RESEARCH_QUESTIONARY_ID))
        .leftJoin(QUESTION_ATTR)
        .on(QUESTION_ATTR.QUESTION_ID.eq(QUESTION.ID))
        .where(RESEARCH_QUESTIONARY.TYPE.eq(4)
            .and(RESEARCH_QUESTIONARY.CLASS_ID.eq(classId))
            .and(DIMENSION.ORDER.eq(3)))
        .orderBy(DIMENSION_QUESTION.ORDER.asc())
        .fetch(r -> {
          Question question = r.into(QUESTION).into(Question.class);
          return question;
        });
  }

  @Override
  public ClassInfo updateClass(String classId, String zong, String ke) {
    // TODO Auto-generated method stub
    ClassInfo classInfo = null;
    if (classId != null && classId != "") {
      classInfo = classInfoDao.get(classId);
      if (classInfo != null) {
        classInfo.setTotalitySatisfied(Double.parseDouble(zong));
        classInfo.setCourseSatisfied(Double.parseDouble(ke));
        classInfoDao.update(classInfo);
      }
    }
    return classInfo;
  }

  @Override
  public Integer findContract(String classId, String memberId) {
    // TODO Auto-generated method stub
    List<Project> projectList = projectDao
        .execute(x -> x
            .selectDistinct(Fields.start().add(PROJECT.ID).end())
            .from(PROJECT)
            .leftJoin(CLASS_INFO).on(CLASS_INFO.PROJECT_ID.eq(PROJECT.ID))
            .where(PROJECT.DELETE_FLAG.eq(Project.DELETE_FLASE))
            .and(PROJECT.ID.eq(CLASS_INFO.PROJECT_ID))
            .and(PROJECT.CONTACT_MEMBER_ID.eq(memberId))
            .and(CLASS_INFO.ID.eq(classId)))
        .fetch(r -> {
          Project c = r.into(Project.class);
          return c;
        });
    if (projectList != null && projectList.size() > 0) {
      return 1;
    }
    return 0;
  }

  @Override
  public ResearchQuestionary makeCompetencyQuestionnair(String classId, Optional<Long> startTime,
      Optional<Long> endTime, Optional<String> detail) {
    // TODO Auto-generated method stub
    ResearchQuestionary researchQuestionary = null;
    Integer praiseNum = traineeDao
        .execute(y -> y.select(Fields.start().add(TRAINEE.ID.count()).end()).from(TRAINEE)
            .where(TRAINEE.CLASS_ID.eq(classId)).and(TRAINEE.COMMIT_ABILITY.eq(1))
        ).fetchOne(TRAINEE.ID.count());
    ClassInfo classInfo = classInfoService.get(classId);
    if (classInfo != null) {
      if (praiseNum == null || praiseNum < 1) {
        Optional<ResearchQuestionary> rQuestionary = researchQuestionaryDao.execute(x -> {
          return x.selectFrom(RESEARCH_QUESTIONARY)
              .where(RESEARCH_QUESTIONARY.CLASS_ID.eq(classId), RESEARCH_QUESTIONARY.TYPE.eq(6))
              .fetchOptionalInto(ResearchQuestionary.class);
        });
        Optional<ClassEvaluate> cEvaluate = classEvaluateDao.execute(x -> {
          return x.selectFrom(CLASS_EVALUATE)
              .where(CLASS_EVALUATE.CLASS_ID.eq(classId), CLASS_EVALUATE.TYPE.eq(6),
                  CLASS_EVALUATE.DELETE_FLAG.eq(0)).fetchOptionalInto(ClassEvaluate.class);
        });
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Long sTime = null;
        Long eTime = null;
        String detailM = null;
        if (classInfo != null) {
          Long t = classInfo.getReturnDate();
          Long t2 = classInfo.getArriveDate();
          sTime = t - 86400000;
          eTime = t + 518400000;
          String ad = sdf.format(new Date(t2));
          String rd = sdf.format(new Date(t));
          String arrDate = ad.replace('-', '.');
          String retDate = rd.replaceAll("-", ".");
          String name = "";
          name = classInfo.getClassName();
          detailM = name + "班能力习得问卷";
        }
        if (rQuestionary.isPresent()) {
          delete(rQuestionary.get().getId(), Optional.empty());
          researchQuestionaryDao.delete(rQuestionary.get().getId());
        }
        if (cEvaluate.isPresent()) {
          classEvaluateDao.delete(cEvaluate.get().getId());
        }

        researchQuestionary = new ResearchQuestionary();
        researchQuestionary.forInsert();
        researchQuestionary.setClassId(classId);
        researchQuestionary.setType(6);
        researchQuestionary.setAnswerPaperRule(ResearchQuestionary.ANSWER_PAPER_RULE);
        researchQuestionary.setName(ResearchQuestionary.NAME_COMPETENCY);
        if (startTime.isPresent() && endTime.isPresent()) {
          startTime.ifPresent(researchQuestionary::setStartTime);
          endTime.ifPresent(researchQuestionary::setEndTime);
        } else if (sTime != null && eTime != null) {
          researchQuestionary.setStartTime(sTime);
          researchQuestionary.setEndTime(eTime);
        }
        if (detail.isPresent()) {
          detail.ifPresent(researchQuestionary::setQuestionaryDetail);
        } else if (detailM != null) {
          researchQuestionary.setQuestionaryDetail(detailM);
        }
        researchQuestionaryDao.insert(researchQuestionary);

        ClassEvaluate evaluate = new ClassEvaluate();
        evaluate.forInsert();
        evaluate.setClassId(classId);
        evaluate.setType(6);
        evaluate.setResourceId(researchQuestionary.getId());
        evaluate.setResourceName(ResearchQuestionary.NAME_COMPETENCY);
        evaluate.setDeleteFlag(0);
        evaluate.setRelease(0);
        if (startTime.isPresent() && endTime.isPresent()) {
          startTime.ifPresent(evaluate::setStartTime);
          endTime.ifPresent(evaluate::setEndTime);
        } else if (sTime != null && eTime != null) {
          evaluate.setStartTime(sTime);
          evaluate.setEndTime(eTime);
        }
        classEvaluateDao.insert(evaluate);
        List<Dimension> dimensionList = new ArrayList<Dimension>();
        Dimension dimension1 = new Dimension();
        Dimension dimension2 = new Dimension();
        Dimension dimension3 = new Dimension();
        Dimension dimension4 = new Dimension();
        Dimension dimension5 = new Dimension();
        dimension1.forInsert();
        dimension2.forInsert();
        dimension3.forInsert();
        dimension4.forInsert();
        dimension5.forInsert();
        dimension1.setName("标题点击可修改..");
        dimension2.setName("标题点击可修改..");
        dimension3.setName("标题点击可修改..");
        dimension4.setName("标题点击可修改..");
        dimension5.setName("标题点击可修改..");
        dimension1.setOrder(1);
        dimension2.setOrder(2);
        dimension3.setOrder(3);
        dimension4.setOrder(4);
        dimension5.setOrder(5);
        dimension1.setResearchQuestionaryId(researchQuestionary.getId());
        dimension2.setResearchQuestionaryId(researchQuestionary.getId());
        dimension3.setResearchQuestionaryId(researchQuestionary.getId());
        dimension4.setResearchQuestionaryId(researchQuestionary.getId());
        dimension5.setResearchQuestionaryId(researchQuestionary.getId());
        dimensionList.add(dimension1);
        dimensionList.add(dimension2);
        dimensionList.add(dimension3);
        dimensionList.add(dimension4);
        dimensionList.add(dimension5);
        dimensionDao.insert(dimensionList);
      }
//			classInfo.setQuestionnaireStatus(1);
//			classInfoDao.update(classInfo);
//			sender.send(MessageTypeContent.AGENCY_CLASSID, MessageHeaderContent.CLASSID, classId);
    }
    return researchQuestionary;
  }

  @Override
  public int Save(String classId, List<Dimension> newList, List<Dimension> delList) {
    // TODO Auto-generated method stub
    Optional<ResearchQuestionary> fetchOne = researchQuestionaryDao
        .fetchOne(RESEARCH_QUESTIONARY.CLASS_ID.eq(classId), RESEARCH_QUESTIONARY.TYPE.eq(6));
    if (fetchOne.isPresent()) {
      ClassInfo classInfo = classInfoDao.get(classId);
      classInfo.setQuestionnaireStatus(1);
      classInfoDao.update(classInfo);
      List<Dimension> originalList = dimensionsByResearchId(fetchOne.get().getId());
      StringBuffer sb = new StringBuffer();
      originalList.stream().forEach(x -> {
        sb.append(x.getId());
      });
      //新增集合
      List<Dimension> addList = newList.stream().filter(x -> {
        return !sb.toString().contains(x.getId());
      }).map(y -> {
        y.forInsert();
        return y;
      }).collect(Collectors.toList());
      dimensionDao.insert(addList);
      //更新集合
      List<Dimension> updateList = newList.stream().filter(x -> {
        return sb.toString().contains(x.getId());
      }).collect(Collectors.toList());
      dimensionDao.update(updateList);
      //删除集合
      List<String> ids = delList.stream().map(x -> {
        return x.getId();
      }).collect(Collectors.toList());
      dimensionDao.delete(ids);
      sender.send(MessageTypeContent.COMPETENCY_QUESTIONNAIRE,
          MessageHeaderContent.RESEARCHQUESTIONARYID, fetchOne.get().getId(),
          MessageHeaderContent.CLASSID, classId);
    }
    return 0;
  }


  @Override
  public List<ResearchQuestionary> findEvaluateResearch(String classId, String memberId,
      Integer[] typeAry) {
    Field<String> recordId = RESEARCH_RECORD.ID.as("recordId");
    Field<Integer> recordStatus = RESEARCH_RECORD.STATUS.as("recordStatus");
    return researchQuestionaryDao.execute(x -> x.selectDistinct(Fields.start()
        .add(RESEARCH_QUESTIONARY.ID, RESEARCH_QUESTIONARY.TYPE, RESEARCH_QUESTIONARY.NAME,
            RESEARCH_QUESTIONARY.END_TIME, RESEARCH_QUESTIONARY.START_TIME, recordId,
            RESEARCH_QUESTIONARY.CLASS_ID, recordStatus, CLASS_EVALUATE.RELEASE).end())
        .from(RESEARCH_QUESTIONARY)
        .innerJoin(RESEARCH_RECORD)
        .on(RESEARCH_RECORD.RESEARCH_QUESTIONARY_ID.eq(RESEARCH_QUESTIONARY.ID))
        .innerJoin(CLASS_EVALUATE).on(CLASS_EVALUATE.RESOURCE_ID.eq(RESEARCH_QUESTIONARY.ID))
        .innerJoin(CLASS_INFO).on(CLASS_EVALUATE.CLASS_ID.eq(CLASS_INFO.ID))
        .innerJoin(TRAINEE).on(TRAINEE.CLASS_ID.eq(RESEARCH_QUESTIONARY.CLASS_ID))
        .where(
            RESEARCH_QUESTIONARY.CLASS_ID.eq(classId)
                .and(TRAINEE.MEMBER_ID.eq(memberId))
                .and(TRAINEE.DELETE_FLAG.eq(0))
                .and(TRAINEE.AUDIT_STATUS.eq(1))
                .and(TRAINEE.TYPE.eq(0))
                .and(RESEARCH_QUESTIONARY.TYPE.ne(7))
                // 联合索引顺序为DELETE_FLAG,RELEASE,TYPE
                .and(CLASS_EVALUATE.DELETE_FLAG.eq(0))
                .and(CLASS_EVALUATE.RELEASE.eq(1))
                // 新旧满意度问卷区分
                .and(CLASS_EVALUATE.TYPE.eq(DSL
                    .when(CLASS_INFO.ARRIVE_DATE.lt(ClassInfo.SATISFACTION_TIME),
                        ClassEvaluate.TYPE_EVA_STU)
                    .otherwise(ClassEvaluate.TYPE_EVA_STU_NEW)))
        )
        .orderBy(RESEARCH_QUESTIONARY.TYPE.asc()))
        .fetch(r -> {
          ResearchQuestionary researchQuestionary = new ResearchQuestionary();
          researchQuestionary.setId(r.getValue(RESEARCH_QUESTIONARY.ID));
          researchQuestionary.setType(r.getValue(RESEARCH_QUESTIONARY.TYPE));
          researchQuestionary.setName(r.getValue(RESEARCH_QUESTIONARY.NAME));
          researchQuestionary.setResearchRecordId(r.getValue(recordId));
          Integer value = r.getValue(recordStatus);
          if (value != null) {
            researchQuestionary.setCommitStatus(value);
          } else {
            researchQuestionary.setCommitStatus(0);
          }
          researchQuestionary.setEndTime(r.getValue(RESEARCH_QUESTIONARY.END_TIME));
          researchQuestionary.setStartTime(r.getValue(RESEARCH_QUESTIONARY.START_TIME));
          researchQuestionary.setClassId(r.getValue(RESEARCH_QUESTIONARY.CLASS_ID));
          return researchQuestionary;
        });

//		return classEvaluateDao
//				.execute(x -> x.selectDistinct(Fields.start().add(CLASS_EVALUATE).end())
//						.from(CLASS_EVALUATE).leftJoin(CLASS_INFO).on(CLASS_EVALUATE.CLASS_ID.eq(CLASS_INFO.ID).and(CLASS_INFO.DELETE_FLAG.eq(0)))
//						.where(CLASS_EVALUATE.DELETE_FLAG.eq(0))
//						.and(CLASS_EVALUATE.CLASS_ID.eq(classId))
//						.and(CLASS_EVALUATE.RELEASE.eq(1))
//						.and(CLASS_EVALUATE.TYPE.eq(4).or(CLASS_EVALUATE.TYPE.eq(5)).or(CLASS_EVALUATE.TYPE.eq(6).and(CLASS_INFO.QUESTIONNAIRE_STATUS.eq(1))))
//						.orderBy(CLASS_EVALUATE.TYPE.asc()))
//				.fetch(r -> r.into(ClassEvaluate.class));
  }

  @Override
  public Optional<ResearchQuestionary> getDatan(String classId) {
    // TODO Auto-generated method stub
    return researchQuestionaryDao
        .fetchOne(RESEARCH_QUESTIONARY.CLASS_ID.eq(classId), RESEARCH_QUESTIONARY.TYPE.eq(6));
  }

  @Override
  public ResearchQuestionary getDatas(String classId, String memberId) {
    // TODO Auto-generated method stub
    Optional<Trainee> record = traineeDao
        .fetchOne(TRAINEE.CLASS_ID.eq(classId), TRAINEE.MEMBER_ID.eq(memberId),
            TRAINEE.DELETE_FLAG.eq(0), TRAINEE.TYPE.eq(0));
    Optional<ResearchQuestionary> fetchOne = researchQuestionaryDao
        .fetchOne(RESEARCH_QUESTIONARY.CLASS_ID.eq(classId), RESEARCH_QUESTIONARY.TYPE.eq(5));
    if (fetchOne.isPresent()) {
      ResearchQuestionary researchQuestionary = fetchOne.get();
      if (record.isPresent()) {
        researchQuestionary.setCommitStatuss(record.get().getCommitFourDegrees());
      }
      return researchQuestionary;
    } else {
      return null;
    }
  }

  @Override
  public int saven(String memberId, String classId, String researchQuestionaryId) {
    // TODO Auto-generated method stub
    Optional<Trainee> record = traineeDao
        .fetchOne(TRAINEE.CLASS_ID.eq(classId), TRAINEE.MEMBER_ID.eq(memberId),
            TRAINEE.DELETE_FLAG.eq(0), TRAINEE.TYPE.eq(0));
    if (record.isPresent()) {
      Trainee t = record.get();
      t.setCommitAbility(1);
      t.setModifyDate(null);
      traineeDao.update(t);
    }
    Integer praiseNum = traineeDao
        .execute(y -> y.select(Fields.start().add(TRAINEE.ID.count()).end()).from(TRAINEE)
            .where(TRAINEE.CLASS_ID.eq(classId)).and(TRAINEE.COMMIT_ABILITY.eq(1))
            .and(TRAINEE.TYPE.eq(0))
            .and(TRAINEE.DELETE_FLAG.eq(0))
        ).fetchOne(TRAINEE.ID.count());
    Optional<ClassInfo> classInfo = classInfoDao.getOptional(classId);
    if (classInfo.isPresent()) {
      ClassInfo c = classInfo.get();
      c.setAbilitySubmitNum(praiseNum);
      classInfoDao.update(c);
    }
//		messageSender.send(MessageTypeContent.SUBMIT_SATISFACTION_QUESTIONNAIREN, MessageHeaderContent.MEMBERID,memberId);
    return 0;
  }

  @Override
  public int saves(String memberId, String classId, String researchQuestionaryId) {
    // TODO Auto-generated method stub
    Optional<Trainee> record = traineeDao
        .fetchOne(TRAINEE.CLASS_ID.eq(classId), TRAINEE.MEMBER_ID.eq(memberId),
            TRAINEE.DELETE_FLAG.eq(0), TRAINEE.TYPE.eq(0));
    if (record.isPresent()) {
      Trainee t = record.get();
      t.setCommitFourDegrees(1);
      t.setModifyDate(null);
      traineeDao.update(t);
    }
    Integer praiseNum = traineeDao
        .execute(y -> y.select(Fields.start().add(TRAINEE.ID.count()).end()).from(TRAINEE)
            .where(TRAINEE.CLASS_ID.eq(classId)).and(TRAINEE.COMMIT_FOUR_DEGREES.eq(1))
            .and(TRAINEE.TYPE.eq(0))
            .and(TRAINEE.DELETE_FLAG.eq(0))
        ).fetchOne(TRAINEE.ID.count());
    Optional<ClassInfo> classInfo = classInfoDao.getOptional(classId);
    if (classInfo.isPresent()) {
      ClassInfo c = classInfo.get();
      c.setFourDegreesSubmitNum(praiseNum);
      classInfoDao.update(c);
    }
//		messageSender.send(MessageTypeContent.SUBMIT_SATISFACTION_QUESTIONNAIRES, MessageHeaderContent.MEMBERID,memberId);
    return 0;
  }

  @Override
  public int deleten(String classId) {
    Optional<ResearchQuestionary> fetchOne = researchQuestionaryDao
        .fetchOne(RESEARCH_QUESTIONARY.CLASS_ID.eq(classId), RESEARCH_QUESTIONARY.TYPE.eq(6));
    if (fetchOne.isPresent()) {
      dimensionDao.delete(DIMENSION.RESEARCH_QUESTIONARY_ID.eq(fetchOne.get().getId()));
    }
    researchQuestionaryDao
        .delete(RESEARCH_QUESTIONARY.CLASS_ID.eq(classId), RESEARCH_QUESTIONARY.TYPE.eq(6));
    //删除能力习得问卷同时更新代办数量
    ClassInfo classInfo = classInfoDao.get(classId);
    if (classInfo != null) {
      classInfo.setQuestionnaireStatus(0);
      classInfoDao.update(classInfo);
    }
    sender.send(MessageTypeContent.AGENCY_CLASSID, MessageHeaderContent.CLASSID, classId);
    return 0;
  }

  @Override
  public ClassEvaluate updaten(String classId, Optional<Long> sTime, Optional<Long> eTime) {
    // TODO Auto-generated method stub
    Optional<ResearchQuestionary> fetchOne = researchQuestionaryDao
        .fetchOne(RESEARCH_QUESTIONARY.CLASS_ID.eq(classId), RESEARCH_QUESTIONARY.TYPE.eq(6));
    Optional<ClassEvaluate> fetchClassEvaluate = classEvaluateDao
        .fetchOne(CLASS_EVALUATE.CLASS_ID.eq(classId), CLASS_EVALUATE.TYPE.eq(6));
    if (fetchOne.isPresent()) {
      ResearchQuestionary researchQuestionary = fetchOne.get();
      sTime.ifPresent(researchQuestionary::setStartTime);
      eTime.ifPresent(researchQuestionary::setEndTime);
      researchQuestionaryDao.update(researchQuestionary);
    }
    if (fetchClassEvaluate.isPresent()) {
      ClassEvaluate classEvaluate = fetchClassEvaluate.get();
      sTime.ifPresent(classEvaluate::setStartTime);
      eTime.ifPresent(classEvaluate::setEndTime);
      classEvaluate.setRelease(1);
      classEvaluateDao.update(classEvaluate);
      sender.send(MessageTypeContent.AGENCY_CLASSID, MessageHeaderContent.CLASSID, classId);
      return classEvaluate;
    } else {
      sender.send(MessageTypeContent.AGENCY_CLASSID, MessageHeaderContent.CLASSID, classId);
      return null;
    }
  }

  @Override
  public int delS(String[] classIds) {
    // TODO Auto-generated method stub
    researchQuestionaryDao
        .delete(RESEARCH_QUESTIONARY.CLASS_ID.in(classIds), RESEARCH_QUESTIONARY.TYPE.eq(5));
    classEvaluateDao.delete(CLASS_EVALUATE.CLASS_ID.in(classIds), CLASS_EVALUATE.TYPE.eq(5));
    for (int i = 0; i < classIds.length; i++) {
      sender.send(MessageTypeContent.AGENCY_CLASSID, MessageHeaderContent.CLASSID, classIds[i]);
    }
    return 0;
  }

  @Override
  public int savel(String memberId, String classId, String researchQuestionaryId) {
    // TODO Auto-generated method stub
    Optional<ResearchQuestionary> rq = researchQuestionaryDao
        .fetchOne(RESEARCH_QUESTIONARY.ID.eq(researchQuestionaryId),
            RESEARCH_QUESTIONARY.TYPE.eq(7));
    Optional<Trainee> record = null;
    if (rq.isPresent()) {
      record = traineeDao.fetchOne(TRAINEE.CLASS_ID.eq(classId),
          TRAINEE.MEMBER_ID.eq(rq.get().getPublishMemberId()), TRAINEE.DELETE_FLAG.eq(0),
          TRAINEE.TYPE.eq(0));
    }
    if (record.isPresent()) {
      Trainee t = record.get();
      t.setCommitSuperiorLeadership(1);
      t.setModifyDate(null);
      traineeDao.update(t);
    }

    Integer praiseNum = traineeDao
        .execute(y -> y.select(Fields.start().add(TRAINEE.ID.count()).end())
            .from(TRAINEE)
            .where(TRAINEE.CLASS_ID.eq(classId)).and(TRAINEE.COMMIT_SUPERIOR_LEADERSHIP.eq(1))
            .and(TRAINEE.TYPE.eq(0))
            .and(TRAINEE.DELETE_FLAG.eq(0))
        ).fetchOne(TRAINEE.ID.count());
    Optional<ClassInfo> classInfo = classInfoDao.getOptional(classId);
    if (classInfo.isPresent()) {
      ClassInfo c = classInfo.get();
      c.setSuperiorLeadershipSubmitNum(praiseNum);
      classInfoDao.update(c);
    }
//		messageSender.send(MessageTypeContent.SUBMIT_SATISFACTION_QUESTIONNAIREL, MessageHeaderContent.MEMBERID,memberId);
    return 0;
  }

  @Override
  public Optional<ResearchQuestionary> getDatal(String classId) {
    // TODO Auto-generated method stub
    return researchQuestionaryDao
        .fetchOne(RESEARCH_QUESTIONARY.CLASS_ID.eq(classId), RESEARCH_QUESTIONARY.TYPE.eq(7));
  }

  @Override
  public PagedResult<ResearchQuestionary> findResearchl(int page, int pageSize, String memberId,
      Optional<String> stutas) {
    // TODO Auto-generated method stub
    Optional<Integer> d = Optional.of(0);
    Optional<Long> end = Optional.of(System.currentTimeMillis());
    Field<String> className = PROJECT.NAME.as("className");
    Field<String> recordId = RESEARCH_RECORD.ID.as("recordId");
    Field<Integer> recordStatus = RESEARCH_RECORD.STATUS.as("recordStatus");
    Field<String> classId = RESEARCH_QUESTIONARY.CLASS_ID.as("classId");
    SelectOnConditionStep<Record> step = researchQuestionaryDao.execute(x -> x
        .selectDistinct(Fields.start()
            .add(RESEARCH_QUESTIONARY.ID, RESEARCH_QUESTIONARY.TYPE, RESEARCH_QUESTIONARY.NAME,
                RESEARCH_QUESTIONARY.END_TIME, RESEARCH_QUESTIONARY.START_TIME, className, recordId,
                classId,
                recordStatus)
            .end())
        .from(RESEARCH_QUESTIONARY)
        .leftJoin(RESEARCH_RECORD)
        .on(RESEARCH_RECORD.RESEARCH_QUESTIONARY_ID.eq(RESEARCH_QUESTIONARY.ID)
            .and(RESEARCH_RECORD.MEMBER_ID.eq(memberId)))
        .leftJoin(CLASS_INFO).on(CLASS_INFO.ID.eq(RESEARCH_QUESTIONARY.CLASS_ID))
        .leftJoin(PROJECT).on(PROJECT.ID.eq(CLASS_INFO.PROJECT_ID).and(PROJECT.DELETE_FLAG.eq(0)))
        .leftJoin(CLASS_EVALUATE)
        .on(CLASS_EVALUATE.RESOURCE_ID.eq(RESEARCH_QUESTIONARY.ID).and(CLASS_EVALUATE.RELEASE.eq(1))
            .and(CLASS_EVALUATE.TYPE.eq(7))
            .and(CLASS_EVALUATE.DELETE_FLAG.eq(0)))
        .leftJoin(TRAINEE)
        .on(TRAINEE.CLASS_ID.eq(RESEARCH_QUESTIONARY.CLASS_ID).and(TRAINEE.DELETE_FLAG.eq(0))
            .and(TRAINEE.AUDIT_LEADERSHIP.eq(memberId))
            .and(TRAINEE.AUDIT_STATUS.eq(1))
            .and(TRAINEE.TYPE.eq(0))
        )
    );
    Condition c = null;
    //Stream<Optional<Condition>> conditions = null;
    if (stutas.isPresent()) {
      c = Stream.of(end.map(RESEARCH_QUESTIONARY.END_TIME::gt)).filter(Optional::isPresent)
          .map(Optional::get).reduce((acc, item) -> acc.and(item))
          .orElse(DSL.trueCondition());
    } else {
      c = DSL.trueCondition();
    }
    if (stutas.isPresent()) {
      SelectConditionStep<Record> condStep = step.where(c.and(TRAINEE.AUDIT_LEADERSHIP.eq(memberId))
          .and(RESEARCH_RECORD.STATUS.isNull()
              .or(RESEARCH_RECORD.STATUS.eq(0))
          )
          .and(RESEARCH_QUESTIONARY.TYPE.eq(7))
          .and(RESEARCH_QUESTIONARY.MEMBER_ID.eq(memberId))
          .and(CLASS_EVALUATE.RELEASE.eq(1)));
      // 获取行数
      Integer count = researchQuestionaryDao.execute(e -> e.fetchCount(condStep));
      List<ResearchQuestionary> list = condStep.orderBy(RESEARCH_QUESTIONARY.END_TIME.desc())
          .limit((page - 1) * pageSize, pageSize).fetch(r -> {
            ResearchQuestionary researchQuestionary = new ResearchQuestionary();
            researchQuestionary.setId(r.getValue(RESEARCH_QUESTIONARY.ID));
            researchQuestionary.setType(r.getValue(RESEARCH_QUESTIONARY.TYPE));
            researchQuestionary.setClassName(r.getValue(className));
            researchQuestionary.setName(r.getValue(RESEARCH_QUESTIONARY.NAME));
            researchQuestionary.setResearchRecordId(r.getValue(recordId));
            researchQuestionary.setClassId(r.getValue(classId));
            researchQuestionary.setCommitStatus(r.getValue(recordStatus));
//				Integer value = r.getValue(RESEARCH_RECORD.STATUS);
//				if (value != null) {
//					researchQuestionary.setCommitStatus(r.getValue(RESEARCH_RECORD.STATUS));
//				} else {
//					researchQuestionary.setCommitStatus(0);
//				}
//				researchQuestionary.setCommitStatuss(r.getValue(TRAINEE.COMMIT_FOUR_DEGREES));
//				researchQuestionary.setCommitStatusn(r.getValue(TRAINEE.COMMIT_ABILITY));
            researchQuestionary.setEndTime(r.getValue(RESEARCH_QUESTIONARY.END_TIME));
            researchQuestionary.setStartTime(r.getValue(RESEARCH_QUESTIONARY.START_TIME));
            return researchQuestionary;
          });
      return PagedResult.create(count, list);
    } else {
      SelectConditionStep<Record> condStep = step
          .where(c.and(TRAINEE.AUDIT_LEADERSHIP.eq(memberId)).and(RESEARCH_QUESTIONARY.TYPE.eq(7))
              .and(RESEARCH_QUESTIONARY.MEMBER_ID.eq(memberId))
              .and(CLASS_EVALUATE.RELEASE.eq(1)));
      // 获取行数
      Integer count = researchQuestionaryDao.execute(e -> e.fetchCount(condStep));
      List<ResearchQuestionary> list = condStep.orderBy(RESEARCH_QUESTIONARY.END_TIME.desc())
          .limit((page - 1) * pageSize, pageSize).fetch(r -> {
            ResearchQuestionary researchQuestionary = new ResearchQuestionary();
            researchQuestionary.setId(r.getValue(RESEARCH_QUESTIONARY.ID));
            researchQuestionary.setType(r.getValue(RESEARCH_QUESTIONARY.TYPE));
            researchQuestionary.setClassName(r.getValue(className));
            researchQuestionary.setName(r.getValue(RESEARCH_QUESTIONARY.NAME));
            researchQuestionary.setResearchRecordId(r.getValue(recordId));
            researchQuestionary.setClassId(r.getValue(classId));
            researchQuestionary.setCommitStatus(r.getValue(recordStatus));
//				researchQuestionary.setCommitStatuss(r.getValue(TRAINEE.COMMIT_FOUR_DEGREES));
//				researchQuestionary.setCommitStatusn(r.getValue(TRAINEE.COMMIT_ABILITY));
            researchQuestionary.setEndTime(r.getValue(RESEARCH_QUESTIONARY.END_TIME));
            researchQuestionary.setStartTime(r.getValue(RESEARCH_QUESTIONARY.START_TIME));
            return researchQuestionary;
          });
      return PagedResult.create(count, list);
    }
  }

  @Override
  public void delQuestionnaire(String id, String classId) {
    // TODO Auto-generated method stub
    researchQuestionaryDao.delete(id);
    ClassInfo classInfo = classInfoDao.get(classId);
    if (classInfo != null) {
      classInfo.setQuestionnaireStatus(0);
      classInfoDao.update(classInfo);
    }
    classEvaluateDao.delete(CLASS_EVALUATE.RESOURCE_ID.eq(id));
  }

  @Override
  public List<ResearchAnswerRecord> insertResearchAnswerRecordsLeader(String memberId,
      String researchQuestionaryId,
      List<ResearchAnswerRecord> researchAnswerRecords, String calssId) {
    // TODO Auto-generated method stub
    Optional<ResearchRecord> researchRecord = researchRecordDao.fetchOne(
        RESEARCH_RECORD.RESEARCH_QUESTIONARY_ID.eq(researchQuestionaryId)
            .and(RESEARCH_RECORD.MEMBER_ID.eq(memberId)));
//                ).orElseThrow(() -> new UnprocessableException(ErrorCode.ResearchRecordError));
    if (researchRecord.isPresent()) {

      if (ResearchRecord.STATUS_FINISHED != researchRecord.get().getStatus()) { // 已经交过卷了，不能重复交卷
        Map<String, ResearchAnswerRecord> answers = researchAnswerRecords.stream().map(t -> {
          t.forInsert();
          t.setResearchRecordId(researchRecord.get().getId());
          return t;
        }).collect(Collectors.toMap(ResearchAnswerRecord::getQuestionId, e -> e, (t1, t2) -> t2));
        List<ResearchAnswerRecord> answerRecords = new ArrayList<>(answers.values());
        researchRecordDao.execute(e -> {
          return e.update(RESEARCH_RECORD)
              .set(RESEARCH_RECORD.STATUS, ResearchRecord.STATUS_FINISHED)
              .where(RESEARCH_RECORD.ID.eq(researchRecord.get().getId()))
              .execute();
        });
        researchAnswerRecordDao.insert(answerRecords);
      }
    } else {
      ResearchRecord r = new ResearchRecord();
      r.forInsert();
      r.setMemberId(memberId);
      r.setResearchQuestionaryId(researchQuestionaryId);
      researchRecordDao.insert(r);

      Map<String, ResearchAnswerRecord> answers = researchAnswerRecords.stream().map(t -> {
        t.forInsert();
        t.setResearchRecordId(r.getId());
        return t;
      }).collect(Collectors.toMap(ResearchAnswerRecord::getQuestionId, e -> e, (t1, t2) -> t2));
      List<ResearchAnswerRecord> answerRecords = new ArrayList<>(answers.values());
      researchRecordDao.execute(e -> {
        return e.update(RESEARCH_RECORD)
            .set(RESEARCH_RECORD.STATUS, ResearchRecord.STATUS_FINISHED)
            .where(RESEARCH_RECORD.ID.eq(r.getId()))
            .execute();
      });
      researchAnswerRecordDao.insert(answerRecords);
    }
//		Integer praiseNum = researchQuestionaryDao.execute(y->y.select(Fields.start().add(RESEARCH_QUESTIONARY.ID.count()).end())
//				.from(RESEARCH_QUESTIONARY)
//				.leftJoin(RESEARCH_RECORD).on(RESEARCH_RECORD.RESEARCH_QUESTIONARY_ID.eq(RESEARCH_QUESTIONARY.ID))
//        		.where(RESEARCH_QUESTIONARY.CLASS_ID.eq(calssId))
//        		.and(RESEARCH_QUESTIONARY.TYPE.eq(7))
//        		.and(RESEARCH_RECORD.STATUS.eq(1))
//        		).fetchOne(RESEARCH_QUESTIONARY.ID.count());
//		Optional<ClassInfo> classInfo = classInfoDao.getOptional(calssId);
//		if (classInfo.isPresent()) {
//			ClassInfo c = classInfo.get();
//			c.setSuperiorLeadershipSubmitNum(praiseNum);
//			classInfoDao.update(c);
//		}
    messageSender
        .send(MessageTypeContent.SUBMIT_SATISFACTION_QUESTIONNAIREL, MessageHeaderContent.MEMBERID,
            memberId);
    return researchAnswerRecords;
  }

  private QuestionAttr initTarget(String content) {
    QuestionAttr question = new QuestionAttr();
    question.setQuestionContent(content);
    question.setManA(0);
    question.setManB(0);
    question.setManC(0);
    question.setManD(0);
    question.setManE(0);
    question.setManF(0);
    question.setManZ(0);
    return question;
  }

  private QuestionAttr initTargetT(String content, String id) {
    QuestionAttr question = new QuestionAttr();
    question.setQuestionContent(content);
    question.setQuestionId(id);
    question.setManA(0);
    question.setManB(0);
    question.setManC(0);
    question.setManD(0);
    question.setManE(0);
    question.setManF(0);
    question.setManZ(0);
    return question;
  }

  private void updateTarget(QuestionAttr target, String answer) {
    if (answer != null) {
      switch (answer) {
        case "0":
          target.setManA(target.getManA() + 1);
          break;
        case "1":
          target.setManB(target.getManB() + 1);
          break;
        case "2":
          target.setManC(target.getManC() + 1);
          break;
        case "3":
          target.setManD(target.getManD() + 1);
          break;
        case "4":
          target.setManE(target.getManE() + 1);
          break;
        case "6":
          target.setManF(target.getManF() + 1);
          break;
      }
      if (!answer.equals("6")) {
        target.setManZ(target.getManZ() + 1);
      }
    }

  }

  @Override
  public List<QuestionAttr> findSatisfactionQuestionnairStatisticsA(String classId) {
    List<QuestionAttr> list2 = new ArrayList<>();
    List<ClassStatistics> occupyList = classStatisticsDao.execute(x -> {
      return x.selectFrom(CLASS_STATISTICS).where(
          CLASS_STATISTICS.SATISFACTION_DIMENSION.eq(1).and(CLASS_STATISTICS.CLASS_ID.eq(classId)))
          .orderBy(CLASS_STATISTICS.ORDER).fetch().into(ClassStatistics.class);
    });
    if (occupyList != null && occupyList.size() > 0) {
      occupyList.forEach(r -> {
        QuestionAttr questionAttr = new QuestionAttr();
        questionAttr.setQuestionContent(r.getContent());
        questionAttr.setManA(r.getSatisfaction());
        questionAttr.setManB(r.getBasicSatisfaction());
        questionAttr.setManC(r.getCommonly());
        questionAttr.setManD(r.getDissatisfied());
        questionAttr.setManE(r.getVeryDissatisfied());
        questionAttr.setManZ(r.getCount());
        questionAttr.setManAF(r.getSatisfiedRate());
        questionAttr.setManBF(r.getBasicSatisfactionRate());
        questionAttr.setManCF(r.getCommonlyRate());
        questionAttr.setManDF(r.getDissatisfiedRate());
        questionAttr.setManEF(r.getVeryDissatisfiedRate());
        questionAttr.setManZF(r.getSatisfactionRate());
        questionAttr.setSort(r.getOrder());
        list2.add(questionAttr);
      });

      list2.sort((label1, label2) -> label1.getSort().compareTo(label2.getSort()));
      return list2;
    } else {
      return null;
    }

  }

  ;

  @Override
  public List<QuestionAttr> findSatisfactionQuestionnairStatisticsB(String classId) {
    List<QuestionAttr> list2 = new ArrayList<>();
    List<ClassStatistics> occupyList = classStatisticsDao.execute(x -> {
      return x.selectFrom(CLASS_STATISTICS).where(
          CLASS_STATISTICS.SATISFACTION_DIMENSION.eq(2).and(CLASS_STATISTICS.CLASS_ID.eq(classId)))
          .orderBy(CLASS_STATISTICS.ORDER).fetch().into(ClassStatistics.class);
    });
    if (occupyList != null && occupyList.size() > 0) {
      occupyList.forEach(r -> {
        QuestionAttr questionAttr = new QuestionAttr();
        questionAttr.setQuestionContent(r.getContent());
        questionAttr.setManA(r.getSatisfaction());
        questionAttr.setManB(r.getBasicSatisfaction());
        questionAttr.setManC(r.getCommonly());
        questionAttr.setManD(r.getDissatisfied());
        questionAttr.setManE(r.getVeryDissatisfied());
        questionAttr.setManZ(r.getCount());
        questionAttr.setManF(r.getRecommendedNumber());
        questionAttr.setManCMF(r.getTeacherSatisfaction());
        questionAttr.setManZF(r.getSatisfactionRate());
        questionAttr.setManAF(r.getSatisfiedRate());
        questionAttr.setManBF(r.getBasicSatisfactionRate());
        questionAttr.setManCF(r.getCommonlyRate());
        questionAttr.setManDF(r.getDissatisfiedRate());
        questionAttr.setManEF(r.getVeryDissatisfiedRate());
        questionAttr.setManFF(r.getRecommendationRate());
        questionAttr.setSort(r.getOrder());
        list2.add(questionAttr);
      });

      list2.sort((label1, label2) -> label1.getSort().compareTo(label2.getSort()));
      return list2;
    } else {
      return null;
    }

  }

  @Override
  public ResearchRecord findResearchRecord(String memberId, String classId, int type) {
    Optional<ResearchQuestionary> fetchOne = researchQuestionaryDao
        .fetchOne(RESEARCH_QUESTIONARY.CLASS_ID.eq(classId), RESEARCH_QUESTIONARY.TYPE.eq(type));
    if (fetchOne.isPresent()) {
      Optional<ResearchRecord> fetchOne2 = researchRecordDao
          .fetchOne(RESEARCH_RECORD.RESEARCH_QUESTIONARY_ID.eq(fetchOne.get().getId()),
              RESEARCH_RECORD.MEMBER_ID.eq(memberId));
      if (fetchOne2.isPresent()) {
        ResearchRecord r = fetchOne2.get();
        return r;
      } else {
        return null;
      }
    } else {
      return null;
    }
  }

  @Override
  public int savePartyCadre(String memberId, String classId, String researchQuestionaryId) {
    Optional<Trainee> record = traineeDao
            .fetchOne(TRAINEE.CLASS_ID.eq(classId), TRAINEE.MEMBER_ID.eq(memberId),
                    TRAINEE.DELETE_FLAG.eq(0), TRAINEE.TYPE.eq(0));
    List<Integer> statusList=researchRecordDao.execute(dslContext -> {
      return dslContext.select(Fields.start().add(RESEARCH_RECORD.STATUS).end())
              .from(RESEARCH_RECORD)
              .innerJoin(RESEARCH_QUESTIONARY).on(RESEARCH_QUESTIONARY.ID.eq(RESEARCH_RECORD.RESEARCH_QUESTIONARY_ID))
              .where(RESEARCH_QUESTIONARY.TYPE.eq(ResearchQuestionary.TYPE_NEW_SATISFACTION_QUESTIONARY)
                      .and(RESEARCH_QUESTIONARY.CLASS_ID.eq(classId)).and(RESEARCH_RECORD.MEMBER_ID.eq(memberId)))
              .fetch(RESEARCH_RECORD.STATUS);
    });
    //如果存在未完成回答的问卷，不去更新状态
    if(statusList==null||statusList.contains(0)||statusList.contains(null)){
      return 0;
    }
    if (record.isPresent()) {
      Trainee t = record.get();
      t.setCommitQuestionary(1);
      t.setModifyDate(null);
      traineeDao.update(t);
      //发消息，更新学员的待办消息
      messageSender.send(
              MessageTypeContent.SUBMIT_RESEARCH_QUESTIONARY_NEW,MessageHeaderContent.MEMBERID,
                      memberId);
    }
    Integer praiseNum = traineeDao
            .execute(y -> y.select(Fields.start().add(TRAINEE.ID.count()).end()).from(TRAINEE)
                    .where(TRAINEE.CLASS_ID.eq(classId)).and(TRAINEE.COMMIT_QUESTIONARY.eq(1))
                    .and(TRAINEE.TYPE.eq(0))
                    .and(TRAINEE.DELETE_FLAG.eq(0))
            ).fetchOne(TRAINEE.ID.count());
    Optional<ClassInfo> classInfo = classInfoDao.getOptional(classId);
    if (classInfo.isPresent()) {
      ClassInfo c = classInfo.get();
      c.setSubmitNum(praiseNum);
      classInfoDao.update(c);
    }
    return 0;
  }
  //判断是否是党校培训班
  private boolean existPartyCadreClass(String classId){
    ClassInfo classInfo = classInfoDao.get(classId);
    Optional<Project> project=projectDao.fetchOne(PROJECT.ID.eq(classInfo.getProjectId()));
    Integer isPartyCadre=project.get().getIsPartyCadre();
    boolean flag=(isPartyCadre==1);
    return flag;
  }

  //获取当前用户没有完成问卷的班级ids
  private List<String> getNoCommitQuestionaryClass(String memberId){
    return traineeDao.execute(dslContext -> {
      return dslContext.selectDistinct(TRAINEE.CLASS_ID).from(TRAINEE).where(

              TRAINEE.MEMBER_ID.eq(memberId).and(TRAINEE.COMMIT_QUESTIONARY.isNull()
                      .or(TRAINEE.COMMIT_QUESTIONARY.eq(0)))).fetch(TRAINEE.CLASS_ID);
    });
  }
  //判断当前用户是否完成当前班级的满意度问卷
  private int existQuestionaryStatus(String memberId,String classId){
   Optional<Trainee> trainee=traineeDao.fetchOne(TRAINEE.MEMBER_ID.eq(memberId).and(TRAINEE.CLASS_ID.eq(classId)));
    //如果存在未完成回答的问卷，不去更新状态
    if(trainee.isPresent()&&trainee.get().getCommitQuestionary()!=null&&trainee.get().getCommitQuestionary()==1){
      return 1;
    }else{
      return 0;
    }
  }
}
