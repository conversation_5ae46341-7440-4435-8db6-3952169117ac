package com.zxy.product.train.service.support;

import com.zxy.common.base.helper.PagedResult;
import com.zxy.common.dao.Fields;
import com.zxy.common.dao.support.CommonDao;
import com.zxy.common.encrypt.SM4.SM4Utils;
import com.zxy.common.message.provider.MessageSender;
import com.zxy.product.train.api.ClassstaffClassService;
import com.zxy.product.train.api.TrainChatGroupService;
import com.zxy.product.train.content.MessageHeaderContent;
import com.zxy.product.train.content.MessageTypeContent;
import com.zxy.product.train.entity.ClassstaffClass;
import com.zxy.product.train.entity.GroupConfigurationValue;
import com.zxy.product.train.entity.Member;
import com.zxy.product.train.entity.Trainee;
import com.zxy.product.train.jooq.tables.Organization;
import org.jooq.Field;
import org.jooq.Record;
import org.jooq.SelectConditionStep;
import org.jooq.impl.DSL;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.zxy.product.train.jooq.Tables.CLASSSTAFF_CLASS;
import static com.zxy.product.train.jooq.Tables.CLASS_INFO;
import static com.zxy.product.train.jooq.Tables.GROUP_CONFIGURATION_VALUE;
import static com.zxy.product.train.jooq.Tables.MEMBER;
import static com.zxy.product.train.jooq.Tables.ORGANIZATION;
import static com.zxy.product.train.jooq.Tables.TRAINEE;

/**
 * Created by 田聪 on 2017/2/16
 */
@Service
public class ClassstaffClassServiceSupport implements ClassstaffClassService {
    private CommonDao<ClassstaffClass> dao;

    private CommonDao<GroupConfigurationValue> groupConfigurationValueDao;

    private TrainChatGroupService trainChatGroupService;

    private MessageSender messageSender;

	@Autowired
	public void setGroupConfigurationValueDao(CommonDao<GroupConfigurationValue> groupConfigurationValueDao) {
		this.groupConfigurationValueDao = groupConfigurationValueDao;
	}
    @Autowired
    public void setDao(CommonDao<ClassstaffClass> dao) {
        this.dao = dao;
    }

    @Autowired
    public void setTrainChatGroupService(TrainChatGroupService trainChatGroupService) {
        this.trainChatGroupService = trainChatGroupService;
    }

    @Autowired
    public void setMessageSender(MessageSender messageSender) {
        this.messageSender = messageSender;
    }

    @Override
    public PagedResult<ClassstaffClass> find(int page, int pageSize, String classId) {

    	List<GroupConfigurationValue> glist = groupConfigurationValueDao.execute(e -> {
			List<GroupConfigurationValue> all = e.selectFrom(GROUP_CONFIGURATION_VALUE)
					.fetch()
					.into(GroupConfigurationValue.class);
			return all;
		});
        // 重命名
//	    Organization org = ORGANIZATION.as("org");
        Field<String> orgName = ORGANIZATION.NAME.as("orgName");
        Field<String> memberId = MEMBER.ID.as("memberId");
        Organization org2 = ORGANIZATION.as("org2");
        Field<String> org2Name = org2.NAME.as("org2Name");
        // 构建语句
        SelectConditionStep<Record> step = dao
                .execute(x -> x
                        .selectDistinct(Fields.start()
                                .add(CLASSSTAFF_CLASS.ID, CLASSSTAFF_CLASS.TYPE, memberId, MEMBER.FULL_NAME, MEMBER.NAME, orgName,
                                        CLASS_INFO.CLASS_TEACHER, CLASSSTAFF_CLASS.CALL_NAME, MEMBER.PHONE_NUMBER,
                                        MEMBER.EMAIL, CLASSSTAFF_CLASS.SORT, CLASSSTAFF_CLASS.CREATE_TIME, org2Name, ORGANIZATION.LEVEL,ORGANIZATION.PATH)
                                .end())
                        .from(CLASSSTAFF_CLASS).leftJoin(MEMBER).on(CLASSSTAFF_CLASS.MEMBER_ID.eq(MEMBER.ID))
                        .leftJoin(ORGANIZATION).on(MEMBER.ORGANIZATION_ID.eq(ORGANIZATION.ID))
                        .leftJoin(org2).on(org2.ID.eq(ORGANIZATION.COMPANY_ID)))
//                .leftJoin(GROUP_CONFIGURATION_VALUE).on(ORGANIZATION.COMPANY_ID.eq(GROUP_CONFIGURATION_VALUE.ORGANIZATION_ID))
                .leftJoin(CLASS_INFO).on(CLASS_INFO.ID.eq(CLASSSTAFF_CLASS.CLASS_ID))
                .where(CLASSSTAFF_CLASS.CLASS_ID.eq(classId))
                .and(CLASSSTAFF_CLASS.DELETE_FLAG.eq(ClassstaffClass.DELETE_FLASE));

        // 获取行数
        Integer count = dao.execute(x -> x.fetchCount(step));

        // 获取列表
        List<ClassstaffClass> list = step.orderBy(CLASSSTAFF_CLASS.SORT.asc(), CLASSSTAFF_CLASS.CREATE_TIME.desc())
                .limit((page - 1) * pageSize, pageSize).fetch(r -> {
                    ClassstaffClass cc = new ClassstaffClass();
                    cc.setId(r.getValue(CLASSSTAFF_CLASS.ID));
                    cc.setCallName(r.getValue(CLASSSTAFF_CLASS.CALL_NAME));
                    cc.setType(r.getValue(CLASSSTAFF_CLASS.TYPE));
                    Member member = new Member();
                    member.setId(r.getValue(memberId));
                    member.setName(r.getValue(MEMBER.NAME));
                    member.setFullName(r.getValue(MEMBER.FULL_NAME));
                    member.setPhoneNumber(SM4Utils.decryptDataCBC(r.getValue(MEMBER.PHONE_NUMBER)));
                    member.setEmail(SM4Utils.decryptDataCBC(r.getValue(MEMBER.EMAIL)));
                    cc.setMember(member);
                    cc.setOrganizationName(r.getValue(orgName));
                    cc.setClassTeacher(r.getValue(CLASS_INFO.CLASS_TEACHER));
                    cc.setSort(r.getValue(CLASSSTAFF_CLASS.SORT));
                    cc.setPath(r.getValue(ORGANIZATION.PATH));
                    cc.setCompanyName(r.getValue(org2Name));
                    cc.setOrganizationLevel(r.getValue(ORGANIZATION.LEVEL));
                    return cc;
                });
        list.forEach(r -> {
			String path = r.getPath();
			String cName = r.getCompanyName();
			if (glist != null && glist.size() > 0) {
				for (int i = 0; i < glist.size(); i++) {
					if (path!=null&&path.contains(glist.get(i).getPath())) {
						r.setCompanyName(glist.get(i).getShortName());
						break;
					} else {
						r.setCompanyName(cName);
					}
				}
			} else {
				r.setCompanyName(cName);
			}
		});
        return PagedResult.create(count, list);
    }

    @Override
    public Integer findClassstaff(String memberId) {
        List<ClassstaffClass> list = dao.execute(x -> {
            return x.select(Fields.start().add(CLASSSTAFF_CLASS.ID).end()).from(CLASSSTAFF_CLASS).where(CLASSSTAFF_CLASS.DELETE_FLAG.eq(ClassstaffClass.DELETE_FLASE).and(CLASSSTAFF_CLASS.MEMBER_ID.eq(memberId)))
            .fetchInto(ClassstaffClass.class);
//            return x.selectFrom(CLASSSTAFF_CLASS).where(CLASSSTAFF_CLASS.MEMBER_ID.eq(memberId).and(CLASSSTAFF_CLASS.DELETE_FLAG.ne(ClassstaffClass.DELETE_TRUE)))
//                    .fetchInto(ClassstaffClass.class);
        });
        if (list != null && list.size() > 0) {
            return 1;
        }
        return 0;
    }

    @Override
    public ClassstaffClass update(String id, Optional<String> callName, Optional<Integer> sort,
                                  Optional<Integer> delete) {
        ClassstaffClass cc = dao.get(id);
        Integer oldOrder = cc.getSort();
        callName.ifPresent(cc::setCallName);
        if (sort.isPresent()) {
            cc.setSort(getOrgOrder(ClassstaffClass.UPDATE, sort, cc.getClassId()));
        }
        delete.ifPresent(cc::setDeleteFlag);
        if (delete.isPresent()) {
            updateReleatedOrgOrder(ClassstaffClass.DELETE, id, cc.getSort(), cc.getClassId());

            // 如果班主任在IM群聊中，则标记为删除
            messageSender.send(MessageTypeContent.CHAT_GROUP_MEMBER_DELETE,
                               MessageHeaderContent.CLASSID, cc.getClassId(),
                               MessageHeaderContent.MEMBERID, cc.getMemberId());

        }
        ClassstaffClass update = dao.update(cc);

        if (sort.isPresent()) {
            updateReleatedOrgOrder(ClassstaffClass.UPDATE, id, oldOrder, cc.getClassId());
        }
        return update;
    }

    @Override
    public String[] findMemberIds(String classId) {
        return dao.execute(x -> x.selectDistinct(Fields.start().add(CLASSSTAFF_CLASS.MEMBER_ID).end())
                .from(CLASSSTAFF_CLASS).where(CLASSSTAFF_CLASS.DELETE_FLAG.eq(ClassstaffClass.DELETE_FLASE))
                .and(CLASSSTAFF_CLASS.CLASS_ID.eq(classId))).fetch(r -> {
            return r.getValue(CLASSSTAFF_CLASS.MEMBER_ID);
        }).toArray(new String[0]);
    }

    @Override
    public List<ClassstaffClass> findForMessage(String classId) {
        return dao.execute(x -> x.selectDistinct(Fields.start().add(CLASSSTAFF_CLASS).add(MEMBER).end())
                .from(CLASSSTAFF_CLASS).leftJoin(MEMBER).on(CLASSSTAFF_CLASS.MEMBER_ID.eq(MEMBER.ID))
                .where(CLASSSTAFF_CLASS.DELETE_FLAG.eq(ClassstaffClass.DELETE_FLASE))
                .and(CLASSSTAFF_CLASS.CLASS_ID.eq(classId))).orderBy(CLASSSTAFF_CLASS.SORT).fetch(r -> {
            ClassstaffClass cc = r.into(ClassstaffClass.class);
            Member mb = r.into(Member.class);
            mb.setPhoneNumber(SM4Utils.decryptDataCBC(mb.getPhoneNumber()));
            cc.setMember(mb);
            return cc;
        });
    }

    @Override
    public List<ClassstaffClass> findByClassMember(List<String> classIds) {
        return dao.execute(x -> x.selectDistinct(Fields.start().add(CLASSSTAFF_CLASS.CLASS_ID).add(MEMBER.NAME,MEMBER.FULL_NAME).end())
                .from(CLASSSTAFF_CLASS).leftJoin(MEMBER).on(CLASSSTAFF_CLASS.MEMBER_ID.eq(MEMBER.ID))
                .where(CLASSSTAFF_CLASS.DELETE_FLAG.eq(ClassstaffClass.DELETE_FLASE))
                .and(CLASSSTAFF_CLASS.CLASS_ID.in(classIds))).orderBy(CLASSSTAFF_CLASS.SORT).fetch(r -> {
            ClassstaffClass cc = r.into(ClassstaffClass.class);
            cc.setMemberName(r.getValue(MEMBER.FULL_NAME));
            return cc;
        });
    }

    @Override
    public int[] insertAll(String classId, List<String> memberIdList) {
        // 查找已存在的班务人员的memberid
        List<String> existsIds = Arrays.asList(this.findMemberIds(classId));
        // 可添加数量
        int addCount = ClassstaffClass.MAX_COUNT - existsIds.size();
        if (addCount > 0) {
            // 声明新增集合
            List<ClassstaffClass> list = new ArrayList<>();
            // 过滤已存在的memberid
            List<String> addIdList = memberIdList.stream().filter(x -> {
                return !existsIds.contains(x);
            }).collect(Collectors.toList());
            // 过滤数量取最后添加的id
            List<String> addMemberIdList = new ArrayList<>();
            if (addCount > addIdList.size()) {
                addMemberIdList = addIdList;
            } else {
                addMemberIdList = addIdList.subList(0, addCount);
            }
            // 新增人员集合
            addMemberIdList.forEach(x -> {
                ClassstaffClass cc = new ClassstaffClass();
                cc.forInsert();
                cc.setMemberId(x);
                cc.setCallName(ClassstaffClass.MASTER_CALLNAME);
                cc.setDeleteFlag(ClassstaffClass.DELETE_FLASE);
                cc.setClassId(classId);
                cc.setType(ClassstaffClass.TYPE_STAFF);
                cc.setSort(ClassstaffClass.DEFAULT_SORT);
                list.add(cc);
                ClassstaffClass c = dao.insert(cc);
                updateReleatedOrgOrder(ClassstaffClass.ADD, c.getId(), c.getSort(), classId);

                // 如果存在IM群聊，则同步新增到群聊列表
                messageSender.send(MessageTypeContent.CHAT_GROUP_MEMBER_ADD,
                                   MessageHeaderContent.CLASSID, classId,
                                   MessageHeaderContent.MEMBERID, x);
            });



//            dao.insert(list);
            return new int[]{list.size(), memberIdList.size() - list.size()};
        }
        // 返回成功数和失败数
        return new int[]{0, memberIdList.size()};
    }

    @Override
    public int insert(String classId, String memberId) {
        // 查找已存在的班务人员的memberid
        List<String> existsIds = Arrays.asList(this.findMemberIds(classId));
        // 如果存在该memberid，返回班务人员已存在
        if (existsIds.contains(memberId)) {
            return 999;
        }
        ;
        // 如果班务已满，返回班务人员最多10人
        if (existsIds.size() >= ClassstaffClass.MAX_COUNT) {
            return 10;
        }
        ClassstaffClass cc = new ClassstaffClass();
        cc.forInsert();
        cc.setType(ClassstaffClass.TYPE_STAFF);
        cc.setMemberId(memberId);
        cc.setCallName(ClassstaffClass.MASTER_CALLNAME);
        cc.setDeleteFlag(ClassstaffClass.DELETE_FLASE);
        cc.setClassId(classId);
        cc.setSort(ClassstaffClass.DEFAULT_SORT);
        dao.insert(cc);
        updateReleatedOrgOrder(ClassstaffClass.ADD, cc.getId(), cc.getSort(), classId);

        // 如果存在IM群聊，则同步新增到群聊列表
        messageSender.send(MessageTypeContent.CHAT_GROUP_MEMBER_ADD,
                           MessageHeaderContent.CLASSID, classId,
                           MessageHeaderContent.MEMBERID, memberId);
        // 添加成功，返回1
        return 1;
    }

    @Override
    public ClassstaffClass insertMasterTeacher(String classId, String memberId) {
        List<ClassstaffClass> list = dao.fetch(CLASSSTAFF_CLASS.CLASS_ID.eq(classId)
                .and(CLASSSTAFF_CLASS.DELETE_FLAG.eq(ClassstaffClass.DELETE_FLASE)));
        for (ClassstaffClass cc : list) {
            // 如果存在班主任，删除
            if (cc.getType() == ClassstaffClass.TYPE_MASTER) {
                dao.delete(cc.getId());
            }
            // 如果存在相同的memberId，删除
            if (cc.getMemberId().equals(memberId)) {
                dao.delete(cc.getId());
            }
        }
        // 新增班主任
        ClassstaffClass newCC = new ClassstaffClass();
        newCC.forInsert();
        newCC.setType(ClassstaffClass.TYPE_STAFF);
        newCC.setMemberId(memberId);
        newCC.setCallName(ClassstaffClass.MASTER_CALLNAME);
        newCC.setDeleteFlag(ClassstaffClass.DELETE_FLASE);
        newCC.setClassId(classId);
        newCC.setSort(ClassstaffClass.MASTER_SORT);
        ClassstaffClass insert = dao.insert(newCC);
        updateReleatedOrgOrder(ClassstaffClass.ADD, newCC.getId(), newCC.getSort(), classId);
        return insert;
    }

    @Override
    public ClassstaffClass findstaffClassByClassId(String classId, String currentUserId) {
        List<ClassstaffClass> list = dao.execute(x -> x.select(Fields.start().add(CLASSSTAFF_CLASS.ID, CLASSSTAFF_CLASS.CLASS_ID).end())
                .from(CLASSSTAFF_CLASS)
                .where(CLASSSTAFF_CLASS.CLASS_ID.eq(classId)
                        .and(CLASSSTAFF_CLASS.MEMBER_ID.eq(currentUserId))
                        .and(CLASSSTAFF_CLASS.DELETE_FLAG.eq(ClassstaffClass.DELETE_FLASE))))
                .fetch(r -> r.into(ClassstaffClass.class));
        if (list != null && list.size() > 0) {
            return list.get(0);
        }
        return null;
    }

    @Override
    public int countByClass(String classId) {
        return dao.execute(x ->{
            return x.selectCount().from(CLASSSTAFF_CLASS).where(CLASSSTAFF_CLASS.CLASS_ID.eq(classId)).fetchOne(0,Integer.class);
        });
    }


    /**
     * 更新学员的order
     */
    private void updateReleatedOrgOrder(int operType,String id, Integer order, String classId) {
        if (operType == Trainee.ADD || operType == Trainee.DELETE) {
            // 找到当前组织同级的,顺序比它大的组织
            List<String> orgIds = getOrgIds(order, Optional.empty(), classId);
            orgIds.remove(id);

            // 更新被影响的组织顺序
            updateOrgOrder(orgIds, operType, classId);
        } else if (operType == Trainee.UPDATE) {
            ClassstaffClass s = dao.get(id);
            int addBase = Integer.compare(order, s.getSort());
            int max = order - s.getSort() > 0 ? order : s.getSort();
            int min = order - s.getSort() < 0 ? order : s.getSort();
            List<String> orgIds = getOrgIds(min, Optional.of(max), classId);
            orgIds.remove(id);

            // 更新被影响的组织顺序
            updateOrgOrder(orgIds, addBase, classId);
        }
        messageSender.send(MessageTypeContent.TRAIN_CLASS_UPDATE, MessageHeaderContent.ID, classId);
    }

    /**
     * 获取指定范围的学员的id
     */
    private List<String> getOrgIds(int order, Optional<Integer> maxOrder, String classId) {
        return maxOrder.map(max -> {
            return dao.execute(d -> d.select(CLASSSTAFF_CLASS.ID)
                    .from(CLASSSTAFF_CLASS).where(CLASSSTAFF_CLASS.SORT.between(order, max)
                            .and(CLASSSTAFF_CLASS.DELETE_FLAG.eq(ClassstaffClass.DELETE_FLASE))
                            .and(CLASSSTAFF_CLASS.CLASS_ID.eq(classId))).fetch(CLASSSTAFF_CLASS.ID));
        }).orElseGet(() -> {
            return dao.execute(d -> d.select(CLASSSTAFF_CLASS.ID).from(CLASSSTAFF_CLASS).where(CLASSSTAFF_CLASS.SORT.ge(order)
                    .and(CLASSSTAFF_CLASS.DELETE_FLAG.eq(ClassstaffClass.DELETE_FLASE))
                    .and(CLASSSTAFF_CLASS.CLASS_ID.eq(classId))).fetch(CLASSSTAFF_CLASS.ID));
        });
    }

    /**
     * 更新结算单位顺序
     */
    private void updateOrgOrder(List<String> orgIds, int addBase, String classId) {
        if (!orgIds.isEmpty() && addBase != 0) {
            if (addBase > 0) {
                dao.execute(d -> d.update(CLASSSTAFF_CLASS).set(CLASSSTAFF_CLASS.SORT, CLASSSTAFF_CLASS.SORT.add(addBase)).where(CLASSSTAFF_CLASS.ID.in(orgIds).and(CLASSSTAFF_CLASS.CLASS_ID.eq(classId))).execute());
            } else {
                dao.execute(d -> d.update(CLASSSTAFF_CLASS).set(CLASSSTAFF_CLASS.SORT, CLASSSTAFF_CLASS.SORT.add(addBase)).where(CLASSSTAFF_CLASS.ID.in(orgIds), CLASSSTAFF_CLASS.SORT.gt(0), CLASSSTAFF_CLASS.CLASS_ID.eq(classId)).execute());
            }
        }
    }


//    /**
//     * 获取结算单位顺序
//     */
    private Integer getOrgOrder(int operType,Optional<Integer> order, String classId) {
        // 获取最大order
        Integer maxOrder = dao.execute(d -> {
            return d.select(DSL.max(CLASSSTAFF_CLASS.SORT)).from(CLASSSTAFF_CLASS)
                    .where(CLASSSTAFF_CLASS.DELETE_FLAG.eq(ClassstaffClass.DELETE_FLASE), CLASSSTAFF_CLASS.CLASS_ID.eq(classId)).fetchOne(DSL.max(TRAINEE.SORT));
        });


        if (maxOrder == null) {
            return 1;
        } else {
            return order.map(o -> {
                if (operType == ClassstaffClass.ADD) {
                    return Integer.compare(o, maxOrder) > 0 ? maxOrder + 1 : o;
                } else {
                    return Integer.compare(o, maxOrder) > 0 ? maxOrder : o;
                }
            }).orElseGet(() -> {
                return maxOrder + 1;
            });
        }
    }

    @Override
    public int delTeacher(String classId, String memberId) {
        int num = dao.execute(r->r.delete(CLASSSTAFF_CLASS).where(CLASSSTAFF_CLASS.CLASS_ID.eq(classId)
                .and(CLASSSTAFF_CLASS.MEMBER_ID.eq(memberId)))).execute();
        return num;
    }

	@Override
	public List<ClassstaffClass> findTepIds(String classId, String[] memberIds) {
		return dao.execute(x -> x.select(Fields.start().add(MEMBER.FULL_NAME, MEMBER.PHONE_NUMBER).end()).from(CLASSSTAFF_CLASS).leftJoin(MEMBER).on(MEMBER.ID.eq(CLASSSTAFF_CLASS.MEMBER_ID)).where(CLASSSTAFF_CLASS.MEMBER_ID.in(memberIds))
				.and(CLASSSTAFF_CLASS.DELETE_FLAG.eq(ClassstaffClass.DELETE_FLASE)).and(CLASSSTAFF_CLASS.CLASS_ID.eq(classId))).fetch(r->{
            ClassstaffClass classstaffClass = new ClassstaffClass();
            classstaffClass.setMemberName(r.getValue(MEMBER.FULL_NAME));
            classstaffClass.setPhoneNumber(SM4Utils.decryptDataCBC(r.getValue(MEMBER.PHONE_NUMBER)));
            return classstaffClass;
        });
	}

    @Override
    public boolean findclassstaffClass(String classId, String memberId) {
        List<ClassstaffClass> classstaffClassList = dao.fetch(CLASSSTAFF_CLASS.CLASS_ID.eq(classId),
                CLASSSTAFF_CLASS.MEMBER_ID.eq(memberId));
        return classstaffClassList != null && classstaffClassList.size() > 0;
    }
}
