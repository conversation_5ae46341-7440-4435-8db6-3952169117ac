package com.zxy.product.train.service.support;

import com.zxy.common.base.helper.PagedResult;
import com.zxy.common.dao.Fields;
import com.zxy.common.dao.support.CommonDao;
import com.zxy.common.encrypt.SM4.SM4Utils;
import com.zxy.common.message.provider.MessageSender;
import com.zxy.product.train.api.ClassInfoService;
import com.zxy.product.train.api.ClassQuotaDetailService;
import com.zxy.product.train.api.ClassQuotaService;
import com.zxy.product.train.api.ClassRequiredService;
import com.zxy.product.train.api.OrganizationService;
import com.zxy.product.train.api.ProjectApprovalService;
import com.zxy.product.train.api.ProjectOccupyService;
import com.zxy.product.train.api.ProjectService;
import com.zxy.product.train.api.QuestionnaireSurveyService;
import com.zxy.product.train.api.TraineeService;
import com.zxy.product.train.content.MessageHeaderContent;
import com.zxy.product.train.content.MessageTypeContent;
import com.zxy.product.train.entity.ClassInfo;
import com.zxy.product.train.entity.ClassQuota;
import com.zxy.product.train.entity.ClassQuotaDetail;
import com.zxy.product.train.entity.ClassRequired;
import com.zxy.product.train.entity.ClassResource;
import com.zxy.product.train.entity.ClassroomConfiguration;
import com.zxy.product.train.entity.ConfigurationValue;
import com.zxy.product.train.entity.DeleteDataTrain;
import com.zxy.product.train.entity.Member;
import com.zxy.product.train.entity.Organization;
import com.zxy.product.train.entity.Project;
import com.zxy.product.train.entity.ProjectApproval;
import com.zxy.product.train.entity.ResearchQuestionary;
import com.zxy.product.train.entity.Trainee;
import com.zxy.product.train.util.StringUtils;
import org.jooq.Condition;
import org.jooq.Field;
import org.jooq.Record;
import org.jooq.Record1;
import org.jooq.Result;
import org.jooq.SelectConditionStep;
import org.jooq.SelectOnConditionStep;
import org.jooq.Table;
import org.jooq.impl.DSL;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.zxy.product.train.entity.Project.DELETE_FLASE;
import static com.zxy.product.train.entity.Project.STATUS_APPROVAL;
import static com.zxy.product.train.jooq.Tables.CLASSROOM_CONFIGURATION;
import static com.zxy.product.train.jooq.Tables.CLASS_INFO;
import static com.zxy.product.train.jooq.Tables.CLASS_REQUIRED;
import static com.zxy.product.train.jooq.Tables.CLASS_RESOURCE;
import static com.zxy.product.train.jooq.Tables.CONFIGURATION_VALUE;
import static com.zxy.product.train.jooq.Tables.MEMBER;
import static com.zxy.product.train.jooq.Tables.ORGANIZATION;
import static com.zxy.product.train.jooq.Tables.ORGANIZATION_DETAIL;
import static com.zxy.product.train.jooq.Tables.PROJECT;
import static com.zxy.product.train.jooq.Tables.PROJECT_APPROVAL;
import static com.zxy.product.train.jooq.Tables.SETTLEMENT_CONFIGURATION_VALUE;

//import static com.zxy.product.train.jooq.Tables.GRANT_DETAIL;

/**
 * Created by 田聪 on 2017/2/8.
 */
@Service
public class ProjectServiceSupport implements ProjectService {
    public static final Logger logger = LoggerFactory.getLogger(ProjectServiceSupport.class);

    private CommonDao<Project> dao;
    private CommonDao<ClassroomConfiguration> classroomConfigurationDao;
    private CommonDao<ClassResource> classResourceDao;

    private ProjectApprovalService projectApprovalDao;
    private CommonDao<ClassRequired> classRequiredCommonDao;
    private MessageSender sender;
    private QuestionnaireSurveyService questionnaireSurveyService;
    private CommonDao<ResearchQuestionary> researchQuestionaryDao;
    private ClassQuotaService classQuotaService;
    private TraineeService traineeService;
    private ClassQuotaDetailService classQuotaDetailService;
    private CommonDao<ClassInfo> classInfoDao;
    private ClassInfoService classInfoService;
    private ProjectOccupyService occupyService;
    private CommonDao<Member> memberDao;
    private CommonDao<Organization> orgDao;
    private CommonDao<ConfigurationValue> configurationValueDao;
    private OrganizationService organizationService;
    private ClassRequiredService classRequiredService;
    @Resource
    private CommonDao<DeleteDataTrain> deleteDataTrainCommonDao;
    @Autowired
    public void setClassRequiredService(ClassRequiredService classRequiredService) {
        this.classRequiredService = classRequiredService;
    }
    @Autowired
    public void setOrganizationService(OrganizationService organizationService) {
        this.organizationService = organizationService;
    }

    @Autowired
    public void setConfigurationValueDao(CommonDao<ConfigurationValue> configurationValueDao) {
        this.configurationValueDao = configurationValueDao;
    }

    @Autowired
    public void setClassroomConfigurationDao(CommonDao<ClassroomConfiguration> classroomConfigurationDao) {
        this.classroomConfigurationDao = classroomConfigurationDao;
    }

    @Autowired
    public void setClassRequiredCommonDao(CommonDao<ClassRequired> classRequiredCommonDao) {
        this.classRequiredCommonDao = classRequiredCommonDao;
    }

    @Autowired
    public void setClassQuotaService(ClassQuotaService classQuotaService) {
        this.classQuotaService = classQuotaService;
    }

    @Autowired
    public void setTraineeService(TraineeService traineeService) {
        this.traineeService = traineeService;
    }

    @Autowired
    public void setClassQuotaDetailService(ClassQuotaDetailService classQuotaDetailService) {
        this.classQuotaDetailService = classQuotaDetailService;
    }

    @Autowired
    public void setResearchQuestionaryDao(CommonDao<ResearchQuestionary> researchQuestionaryDao) {
        this.researchQuestionaryDao = researchQuestionaryDao;
    }

    @Autowired
    public void setQuestionnaireSurveyService(QuestionnaireSurveyService questionnaireSurveyService) {
        this.questionnaireSurveyService = questionnaireSurveyService;
    }

    @Autowired
    public void setProjectApprovalDao(ProjectApprovalService projectApprovalDao) {
        this.projectApprovalDao = projectApprovalDao;
    }

    @Autowired
    public void setClassResourceDao(CommonDao<ClassResource> classResourceDao) {
        this.classResourceDao = classResourceDao;
    }

    @Autowired
    public void setOccupyService(ProjectOccupyService occupyService) {
        this.occupyService = occupyService;
    }

    public CommonDao<ClassInfo> getClassInfoDao() {
        return classInfoDao;
    }

    @Autowired
    public void setClassInfoDao(CommonDao<ClassInfo> classInfoDao) {
        this.classInfoDao = classInfoDao;
    }

    @Autowired
    public void setClassInfoService(ClassInfoService classInfoService) {
        this.classInfoService = classInfoService;
    }

    @Autowired
    public void setDao(CommonDao<Project> dao) {
        this.dao = dao;
    }

    @Autowired
    public void setSender(MessageSender sender) {
        this.sender = sender;
    }

    @Autowired
    public void setMemberDao(CommonDao<Member> memberDao) {
        this.memberDao = memberDao;
    }

    @Autowired
    public void setOrgDao(CommonDao<Organization> orgDao) {
        this.orgDao = orgDao;
    }


    // 参数： MIS编码、班级名称、主办部门、计划年份、计划月份、审核结果、组织id
    // 参数： 实施年份、实施月份、班级状态、
    // 参数： 当前登录用户id
    // 返回结果：id、培训班名称、MIS编码、需求方联系人、需求方单位、计划年份、计划月份、审核结果、计划人数、计划天数、报到日、返程日、实施状态、班主任
    @Override
    public PagedResult<Project> find(int page, int pageSize, Optional<String> MIScode, Optional<String> className,
                                     Optional<String> demandId, Optional<Integer> planYear, Optional<Integer> planMonth,
                                     Optional<Integer> auditStatus, Optional<String> organizationId, Optional<Integer> reachYear,
                                     Optional<Integer> reachMonth, Optional<Integer> classStatus, Optional<String> memberId) {
        // 重命名
        com.zxy.product.train.jooq.tables.Member member2 = MEMBER.as("member2");
        Field<Integer> reachStatus = CLASS_INFO.STATUS.as("reachStatus");
        Field<Integer> approvalStatus = PROJECT.STATUS.as("approvalStatus");
        Field<String> organizationName = ORGANIZATION.NAME.as("organizationName");
        Field<String> memberFullName = MEMBER.FULL_NAME.as("memberFullName");
        Field<Long> cTime = PROJECT.CREATE_TIME.as("cTime");
        long nowDateNew = System.currentTimeMillis() - 86400000;
        // 构建查询语句
        SelectOnConditionStep<Record> step = dao.execute(x -> x
                .selectDistinct(Fields.start()
                        .add(PROJECT.ID, PROJECT.NAME, PROJECT.CODE, PROJECT.CONTACT_MEMBER_ID, memberFullName, organizationName, PROJECT.YEAR,
                                PROJECT.MONTH, approvalStatus, PROJECT.AMOUNT, PROJECT.DAYS, CLASS_INFO.ARRIVE_DATE, PROJECT.STUDENT_STATUS,
                                CLASS_INFO.RETURN_DATE, reachStatus, member2.FULL_NAME, cTime, CLASS_INFO.NOTICE, PROJECT.ORGANIZATION_ID)
                        .end())
                .from(PROJECT).leftJoin(CLASS_INFO).on(PROJECT.ID.eq(CLASS_INFO.PROJECT_ID).and(CLASS_INFO.DELETE_FLAG.eq(DELETE_FLASE))).leftJoin(MEMBER)
                .on(PROJECT.CONTACT_MEMBER_ID.eq(MEMBER.ID)).leftJoin(member2)
                .on(CLASS_INFO.CLASS_TEACHER.eq(member2.ID)).leftJoin(ORGANIZATION)
                .on(ORGANIZATION.ID.eq(PROJECT.ORGANIZATION_ID)));
//        if (organizationId.isPresent()) {
//            step = step.leftJoin(GRANT_DETAIL).on(PROJECT.ORGANIZATION_ID.eq(GRANT_DETAIL.ORGANIZATION_ID))
//                    .leftJoin(ORGANIZATION_DETAIL).on(ORGANIZATION_DETAIL.SUB.eq(PROJECT.ORGANIZATION_ID));
//        }
        Optional<Integer> implementationMonth = Optional.empty();
        Optional<Integer> implementationYear = Optional.empty();
        Optional<Integer> projectYear = Optional.empty();
        Optional<Integer> projectMonth = Optional.empty();
        if (reachMonth.isPresent()) {
            implementationMonth = reachMonth.get() == 0 ? Optional.empty() : reachMonth;
        }
        if (reachYear.isPresent()) {
            implementationYear = reachYear.get() == 0 ? Optional.empty() : reachYear;
        }
        if (planYear.isPresent()) {
            projectYear = planYear.get() == 0 ? Optional.empty() : planYear;
        }
        if (planMonth.isPresent()) {
            projectMonth = planMonth.get() == 0 ? Optional.empty() : planMonth;
        }
        Stream<Optional<Condition>> conditions = Stream.of(MIScode.map(PROJECT.CODE::contains),
                className.map(PROJECT.NAME::contains), demandId.map(PROJECT.ORGANIZATION_ID::eq),
                projectYear.map(PROJECT.YEAR::eq), projectMonth.map(PROJECT.MONTH::eq), auditStatus.map(PROJECT.STATUS::eq),
                organizationId.map(ORGANIZATION_DETAIL.ROOT::eq), implementationYear.map(CLASS_INFO.IMPLEMENTATION_YEAR::eq),
                implementationMonth.map(CLASS_INFO.IMPLEMENTATION_MONTH::eq),
                memberId.map(PROJECT.CONTACT_MEMBER_ID::eq));
        // 合并条件
        Condition c = null;
        if (classStatus.isPresent()) {
            if (classStatus.get().equals(1)) {
                c = conditions.filter(Optional::isPresent).map(Optional::get).reduce((acc, item) -> acc.and(item))
                        .orElse(DSL.trueCondition()).and(CLASS_INFO.STATUS.eq(1)).or(PROJECT.STATUS.ne(3)).or(CLASS_INFO.ARRIVE_DATE.isNull());
            } else if (classStatus.get().equals(2)) {
                c = conditions.filter(Optional::isPresent).map(Optional::get).reduce((acc, item) -> acc.and(item))
                        .orElse(DSL.trueCondition()).and(CLASS_INFO.STATUS.eq(2)).and(CLASS_INFO.RETURN_DATE.ge(nowDateNew)
                                .and(PROJECT.STATUS.ne(2)));
            } else if (classStatus.get().equals(3)) {
                c = conditions.filter(Optional::isPresent).map(Optional::get).reduce((acc, item) -> acc.and(item))
                        .orElse(DSL.trueCondition()).and(CLASS_INFO.STATUS.eq(3)).and(PROJECT.STATUS.eq(3));
            }
        } else {
            c = conditions.filter(Optional::isPresent).map(Optional::get).reduce((acc, item) -> acc.and(item))
                    .orElse(DSL.trueCondition());
        }
        // 合并语句
        SelectConditionStep<Record> condStep = step.where(c);
        // 获取行数
        Integer count = dao.execute(e -> e.fetchCount(condStep.and(PROJECT.DELETE_FLAG.eq(0))));
        // 获取列表
        List<Project> list = condStep.and(PROJECT.DELETE_FLAG.eq(0)).orderBy(PROJECT.CREATE_TIME.desc()).limit((page - 1) * pageSize, pageSize).fetch(r -> {
            Project project = new Project();
            project.setId(r.getValue(PROJECT.ID));
            project.setName(r.getValue(PROJECT.NAME));
            project.setCode(r.getValue(PROJECT.CODE));
            project.setContactMemberId(r.getValue(PROJECT.CONTACT_MEMBER_ID));
            project.setContactMemberName(r.getValue(memberFullName));
            project.setOrganizationName(r.getValue(organizationName));
            project.setYear(r.getValue(PROJECT.YEAR));
            project.setMonth(r.getValue(PROJECT.MONTH));
            project.setStatus(r.getValue(approvalStatus));
            project.setAmount(r.getValue(PROJECT.AMOUNT));
            project.setDays(r.getValue(PROJECT.DAYS));
            project.setStudentStatus(r.getValue(PROJECT.STUDENT_STATUS));
            project.setOrganizationId(r.getValue(PROJECT.ORGANIZATION_ID));
            ClassInfo classInfo = new ClassInfo();
            classInfo.setArriveDate(r.getValue(CLASS_INFO.ARRIVE_DATE));
            classInfo.setReturnDate(r.getValue(CLASS_INFO.RETURN_DATE));
            classInfo.setNotice(r.getValue(CLASS_INFO.NOTICE));
            if (project.getStatus() == 3) {
                if (classInfo.getReturnDate() != null && nowDateNew >= classInfo.getReturnDate()) {
                    project.setClassStatus(ClassInfo.STATUS_TRUE);//已实施
                } else if(classInfo.getReturnDate() != null && nowDateNew <= classInfo.getReturnDate()) {
                    if (classInfo.getArriveDate() != null && System.currentTimeMillis() >= classInfo.getArriveDate()) {
                        project.setClassStatus(ClassInfo.STATUS_ING);//实施中
                    } else {
                        project.setClassStatus(ClassInfo.STATUS_FALSE);
                    }
                } else {
                    project.setClassStatus(ClassInfo.STATUS_FALSE);
                }
            } else {
                project.setClassStatus(ClassInfo.STATUS_FALSE);
            }
            project.setClassInfo(classInfo);
            project.setTeacherName(r.getValue(member2.FULL_NAME));
            if (project.getStatus() == 3 || project.getStatus() == 2) {
                project.setAuditStatus(1);
            }
            return project;
        });
        return PagedResult.create(count, list);
    }

    @Override
    public PagedResult<Project> findGround(int page, int pageSize, Optional<String> MIScode, Optional<String> className,
                                           Optional<String> demandId, Optional<Integer> planYear, Optional<Integer> planMonth,
                                           Optional<Integer> auditStatus, Optional<String> organizationId, Optional<Integer> reachYear,
                                           Optional<Integer> reachMonth, Optional<Integer> classStatus, Map<String, Set<String>> grantOrganizationPathMap) {
        long nowDate = System.currentTimeMillis();
        long nowDateNew = System.currentTimeMillis() - 86400000;
        Field<Integer> classState = DSL.nvl(CLASS_INFO.STATUS, 1).as("classStatus");
        //计划id
        List<String> projectIds = new ArrayList<>();
        //联系人id
        List<String> memberIds = new ArrayList<>();
        //需求方id
        List<String> orgIds = new ArrayList<>();
        final String fsql;
        final String fMsql;
        String sql = "1=1";
        String monthSql = "1=1";
        if (reachYear.isPresent()) {
            sql = "`train`.`t_class_info`.`f_implementation_year` = " + reachYear.get() + "";
        }
        if (reachMonth.isPresent()) {
            monthSql = "`train`.`t_class_info`.`f_implementation_month` = " + reachMonth.get() + "";
        }
        fsql = sql;
        fMsql = monthSql;

        List<Condition> conditions = Stream.of(
                organizationId.map(id -> ORGANIZATION.PATH.startsWith(orgDao.get(id).getPath()))
        ).filter(Optional::isPresent).map(Optional::get).collect(Collectors.toList());
        generateOrganizationConditions(grantOrganizationPathMap,conditions);

        List<Project> list = dao.execute(d -> {
            Table<Record1<String>> basic = (d.select(PROJECT.ID)
                    .from(PROJECT)
                    .leftJoin(ORGANIZATION).on(PROJECT.ORGANIZATION_ID.eq(ORGANIZATION.ID))
                    .where(conditions)
                    .and(MIScode.map(PROJECT.CODE::contains).orElse(DSL.trueCondition()))
                    .and(className.map(PROJECT.NAME::contains).orElse(DSL.trueCondition()))
                    .and(planYear.map(PROJECT.YEAR::eq).orElse(DSL.trueCondition()))
                    .and(planMonth.map(PROJECT.MONTH::eq).orElse(DSL.trueCondition()))
                    .and(auditStatus.map(PROJECT.STATUS::eq).orElse(DSL.trueCondition()))
//                    .and(reachYear.map(m -> PROJECT.ID.in(classInfoDao.fetch(CLASS_INFO.IMPLEMENTATION_YEAR.eq(m)).stream().map(ClassInfo::getProjectId).collect(Collectors.toList()))).orElse(DSL.trueCondition()))
//                    .and(reachMonth.map(o -> PROJECT.ID.in(classInfoDao.fetch(CLASS_INFO.IMPLEMENTATION_MONTH.eq(o)).stream().map(ClassInfo::getProjectId).collect(Collectors.toList()))).orElse(DSL.trueCondition()))
                    .and(PROJECT.DELETE_FLAG.eq(Project.DELETE_FLASE))
                    .and(classStatus.map(r -> {
                        if (r == 1) {
                            List<String> prIds = classInfoDao.fetch(CLASS_INFO.ARRIVE_DATE.gt(nowDate).or(CLASS_INFO.ARRIVE_DATE.isNull())).stream().map(ClassInfo::getProjectId)
                                    .collect(Collectors.toList());
                            return PROJECT.ID.in(prIds).or(PROJECT.STATUS.ne(3));
                        } else if (r == 2) {
                            List<String> prIds = classInfoDao.fetch(CLASS_INFO.ARRIVE_DATE.le(nowDate).and(CLASS_INFO.RETURN_DATE.ge(nowDate))).stream().map(ClassInfo::getProjectId)
                                    .collect(Collectors.toList());
                            return PROJECT.ID.in(prIds).and(PROJECT.STATUS.ne(2));
                        }
                        List<String> prIds = classInfoDao.fetch(CLASS_INFO.RETURN_DATE.lt(nowDate)).stream().map(ClassInfo::getProjectId)
                                .collect(Collectors.toList());
                        return PROJECT.ID.in(prIds).and(PROJECT.STATUS.eq(3));
                    }).orElse(DSL.trueCondition()))
            ).asTable("b");

            return d.select(Fields.start()
                    .add(PROJECT.ID, PROJECT.NAME, PROJECT.CODE, PROJECT.CONTACT_MEMBER_ID, PROJECT.YEAR, PROJECT.MONTH, PROJECT.STATUS, PROJECT.CREATE_TIME)
                    .add(PROJECT.STUDENT_STATUS, PROJECT.ORGANIZATION_ID, classState, CLASS_INFO.ARRIVE_DATE, CLASS_INFO.RETURN_DATE,PROJECT.RESERVATION_MEMBER,PROJECT.RESERVATION_TIME)
                    .end())
                    .from(PROJECT)
                    .innerJoin(basic).on(basic.field(PROJECT.ID).eq(PROJECT.ID))
                    .leftJoin(CLASS_INFO).on(CLASS_INFO.PROJECT_ID.eq(PROJECT.ID))
                    .where(fsql).and(fMsql)
                    .orderBy(
                            // 第一优先级：待审核状态优先
                            DSL.when(PROJECT.STATUS.eq(STATUS_APPROVAL), DSL.inline(0)).otherwise(1).asc(),
                            // 待审核状态下按预定时间升序
                            DSL.when(PROJECT.STATUS.eq(STATUS_APPROVAL), PROJECT.RESERVATION_TIME) // 假设RESERVATION_MEMBER应为RESERVATION_TIME
                                    .asc(),
                            // 第二优先级：按classState自定义排序(2,3,1)
                            DSL.choose(classState)
                                    .when(2, DSL.inline(0))// 最高优先级
                                    .when(3, DSL.inline(1))
                                    .when(1, DSL.inline(2)) // 最低优先级
                                    .asc(),
                            // 当classState=1时按负年份降序，否则按到达日期降序
                            DSL.when(classState.eq(1), PROJECT.YEAR.neg().cast(Long.class))
                                    .otherwise(CLASS_INFO.ARRIVE_DATE.cast(Long.class))
                                    .desc(),
                            // 当classState=1时按负月份降序，否则按到达日期降序
                            DSL.when(classState.eq(1), PROJECT.MONTH.neg().cast(Long.class))
                                    .otherwise(CLASS_INFO.ARRIVE_DATE.cast(Long.class))
                                    .desc()
                    )
//                    .orderBy(
////                            // 第一优先级：待审核状态优先
////                            DSL.field(PROJECT.STATUS.eq(STATUS_APPROVAL)).asc(),
////                            // 待审核状态下按预定时间升序
////                            DSL.field(DSL.when(PROJECT.STATUS.eq(STATUS_APPROVAL), PROJECT.RESERVATION_MEMBER)),
////                            classState.sortAsc(2, 3, 1),
////                            DSL.when(classState.eq(1), PROJECT.YEAR.neg().cast(long.class)).otherwise(CLASS_INFO.ARRIVE_DATE).desc(),
////                            DSL.when(classState.eq(1), PROJECT.MONTH.neg().cast(long.class)).otherwise(CLASS_INFO.ARRIVE_DATE).desc()
//                    )
                    .limit((page - 1) * pageSize, pageSize)
                    .fetch(r -> {
                        Project p = r.into(Project.class);
                        if (p.getStatus() == 3 || p.getStatus() == 2) {
                            p.setAuditStatus(1);
                        }
                        projectIds.add(r.getValue(PROJECT.ID));
                        memberIds.add(r.getValue(PROJECT.CONTACT_MEMBER_ID));
                        //添加预定人
                        memberIds.add(r.getValue(PROJECT.RESERVATION_MEMBER));
                        orgIds.add(r.getValue(PROJECT.ORGANIZATION_ID));
                        return p;
                    });
        });

        Map<String, Member> memberMap = memberDao.execute(x -> x.select(MEMBER.ID, MEMBER.FULL_NAME).from(MEMBER).where(MEMBER.ID.in(memberIds)))
                .fetch(r -> r.into(Member.class)).stream()
                .collect(Collectors.toMap(Member::getId, m -> m));

        Map<String, Organization> orgMap = orgDao.execute(x -> x.select(ORGANIZATION.ID, ORGANIZATION.NAME).from(ORGANIZATION).where(ORGANIZATION.ID.in(orgIds)))
                .fetch(r -> r.into(Organization.class)).stream()
                .collect(Collectors.toMap(Organization::getId, o -> o));

        Map<String, ClassInfo> classInfoMap = classInfoDao.execute(x -> x.select(CLASS_INFO.PROJECT_ID, CLASS_INFO.ARRIVE_DATE, CLASS_INFO.RETURN_DATE, CLASS_INFO.STATUS, CLASS_INFO.NOTICE).from(CLASS_INFO))
                .where(CLASS_INFO.PROJECT_ID.in(projectIds)).fetch(r -> r.into(ClassInfo.class)).stream()
                .collect(Collectors.toMap(ClassInfo::getProjectId, c -> c));

        Integer count = dao.execute(d ->
                    d.select(PROJECT.ID.count())
                            .from(PROJECT).leftJoin(CLASS_INFO).on(PROJECT.ID.eq(CLASS_INFO.PROJECT_ID))
                            .leftJoin(ORGANIZATION).on(PROJECT.ORGANIZATION_ID.eq(ORGANIZATION.ID))
                            .where(conditions)
                            .and(MIScode.map(PROJECT.CODE::contains).orElse(DSL.trueCondition()))
                            .and(className.map(PROJECT.NAME::contains).orElse(DSL.trueCondition()))
                            .and(planYear.map(PROJECT.YEAR::eq).orElse(DSL.trueCondition()))
                            .and(planMonth.map(PROJECT.MONTH::eq).orElse(DSL.trueCondition()))
                            .and(auditStatus.map(PROJECT.STATUS::eq).orElse(DSL.trueCondition()))
                            .and(PROJECT.DELETE_FLAG.eq(Project.DELETE_FLASE))
                            .and(fsql).and(fMsql)
                            .and(classStatus.map(r -> {
                                if (r == 1) {
                                    List<String> prIds = classInfoDao.fetch(CLASS_INFO.DELETE_FLAG.eq(ClassInfo.DELETE_FLASE).and(CLASS_INFO.ARRIVE_DATE.gt(nowDate).or(CLASS_INFO.ARRIVE_DATE.isNull()))).stream().map(ClassInfo::getProjectId)
                                            .collect(Collectors.toList());
                                    return PROJECT.ID.in(prIds).or(PROJECT.STATUS.ne(3));
                                } else if (r == 2) {
                                    List<String> prIds = classInfoDao.fetch(CLASS_INFO.ARRIVE_DATE.le(nowDate).and(CLASS_INFO.RETURN_DATE.ge(nowDateNew))).stream().map(ClassInfo::getProjectId)
                                            .collect(Collectors.toList());
                                    return PROJECT.ID.in(prIds).and(PROJECT.STATUS.ne(2));
                                }
                                List<String> prIds = classInfoDao.fetch(CLASS_INFO.RETURN_DATE.lt(nowDateNew)).stream().map(ClassInfo::getProjectId)
                                        .collect(Collectors.toList());
                                return PROJECT.ID.in(prIds).and(PROJECT.STATUS.eq(3));
                            }).orElse(DSL.trueCondition())).fetchOne(PROJECT.ID.count()));

        List<ProjectApproval> approvals = projectApprovalDao.findByProjectIds(projectIds);
        Map<String, ProjectApproval> approvalMap;
        if(!CollectionUtils.isEmpty(approvals)){
            approvalMap = approvals.stream().collect(Collectors.toMap(ProjectApproval::getProjectId, Function.identity(), (p, q) -> q));
        } else {
            approvalMap = new HashMap<>();
        }
        list.forEach(r -> {
            String id = r.getId();
            String orgId = r.getOrganizationId();
            String memberId = r.getContactMemberId();
            if (orgMap.get(orgId) != null) {
                r.setOrganizationName(orgMap.get(orgId).getName());
            }
            if (memberMap.get(memberId) != null) {
                r.setContactMemberName(memberMap.get(memberId).getFullName());
                r.setReservationMemberName(Objects.nonNull(r.getReservationMember()) ? memberMap.get(r.getReservationMember()).getFullName() : null);
            }
            if(Objects.nonNull(approvalMap) && approvalMap.size() > 0 && Objects.nonNull(approvalMap.get(id))){
                r.setProjectApproval(approvalMap.get(id));
            }
            if (classInfoMap.get(id) != null) {
                r.setClassStatus(classInfoMap.get(id).getStatus());
                r.setClassInfo(classInfoMap.get(id));
            } else {
                r.setClassStatus(ClassInfo.STATUS_FALSE);
            }
        });
        return PagedResult.create(count, list);

    }

    /**
     * 拼装组织条件
     *
     * @param grantOrganizationMap 组织Map
     * @param conditions
     * return 组织条件
     */
    private void generateOrganizationConditions(Map<String, Set<String>> grantOrganizationMap, List<Condition> conditions) {
        Set<String> organizationIdSet = grantOrganizationMap.get(com.zxy.product.system.entity.Organization.NOT_INCLUDE_KEY);
        Set<String> pathSet = grantOrganizationMap.get(com.zxy.product.system.entity.Organization.INCLUDE_KEY);
        if (!CollectionUtils.isEmpty(pathSet) || !CollectionUtils.isEmpty(organizationIdSet)) {
            Condition condition;
            if (pathSet.isEmpty()) {
                condition = Optional.of(organizationIdSet).map(ORGANIZATION.ID::in).orElse(DSL.trueCondition());
            } else {
                condition = pathSet.stream().map(ORGANIZATION.PATH::startsWith).reduce(DSL::or)
                        .orElse(DSL.trueCondition());
                if (!organizationIdSet.isEmpty()) {
                    condition = condition.or(ORGANIZATION.ID.in(organizationIdSet));
                }
            }
            conditions.add(condition);
        }
    }

    /**
     * 新增
     */
    @Override
    public Project insert(String name, Optional<String> code, String object, String organizationId, String contactMemberId,
                          String contactPhone, Optional<String> contactEmail, Integer year, Integer month, Integer amount, Integer days,
                          String address, String typeId, String cost, Optional<String> createMemberId) {
        // TODO Auto-generated method stub
        Project project = new Project();
        project.forInsert();
        project.setName(name);
        code.ifPresent(project::setCode);
        contactEmail.ifPresent(project::setContactEmail);
        project.setContactMemberId(contactMemberId);
        project.setOrganizationId(organizationId);
        project.setContactPhone(contactPhone);
        project.setYear(year);
        project.setObject(object);
        project.setMonth(month);
        project.setAmount(amount);
        project.setAddress(address);
        project.setTypeId(typeId);
        project.setDays(days);
        project.setCreateTime(System.currentTimeMillis());
        project.setDeleteFlag(Project.DELETE_FLASE);
        project.setStatus(Project.STATUS_RESERVE);//待预定
        project.setCost(cost);
        project.setStudentStatus(0);
        createMemberId.ifPresent(project::setCreateMember);
        dao.insert(project);
        sender.send(MessageTypeContent.TRAIN_PROJECT_INSERT, MessageHeaderContent.ID, project.getId());
        sender.send(MessageTypeContent.TRAIN_PROJECT_SYNCHRONOUS, MessageHeaderContent.ID, project.getId());
        return project;
    }


    /**
     * 根据ID查询
     */
    @Override
    public Project get(String id) {
        // TODO Auto-generated method stub
        com.zxy.product.train.jooq.tables.ConfigurationValue configurationValue = CONFIGURATION_VALUE.as("configurationValue");
        com.zxy.product.train.jooq.tables.ConfigurationValue configurationValue1 = CONFIGURATION_VALUE.as("configurationValue1");
        Project p = dao.execute(x -> {
            Record result = x.select(Fields.start().add(PROJECT).add(configurationValue.NAME).add(configurationValue1.NAME).add(ORGANIZATION).add(CLASS_INFO).add(MEMBER).end())
                    .from(PROJECT)
                    .leftJoin(MEMBER).on(PROJECT.CONTACT_MEMBER_ID.eq(MEMBER.ID))
                    .leftJoin(CLASS_INFO).on(CLASS_INFO.PROJECT_ID.eq(PROJECT.ID))
                    .leftJoin(ORGANIZATION).on(ORGANIZATION.ID.eq(PROJECT.ORGANIZATION_ID))
                    .leftJoin(configurationValue)
                    .on(configurationValue.ID.eq(PROJECT.TYPE_ID))
                    .leftJoin(configurationValue1)
                    .on(configurationValue1.ID.eq(PROJECT.COST))
                    .where(PROJECT.ID.eq(id).and(PROJECT.DELETE_FLAG.eq(Project.DELETE_FLASE))).fetchOne();
            Project project = result.into(Project.class);
            Member member = result.into(Member.class);
            Organization organization = result.into(Organization.class);
            ClassInfo classInfo = result.into(ClassInfo.class);
            if (project.getStatus() == 3) {
//                if (classInfo.getReturnDate() != null && System.currentTimeMillis() >= classInfo.getReturnDate()) {
//                    project.setClassStatus(ClassInfo.STATUS_TRUE);//已实施
//                } else if(classInfo.getReturnDate() != null && System.currentTimeMillis() <= classInfo.getReturnDate()) {
//                    if (classInfo.getArriveDate() != null && System.currentTimeMillis() >= classInfo.getArriveDate()) {
//                        project.setClassStatus(ClassInfo.STATUS_ING);//实施中
//                    } else {
//                        project.setClassStatus(ClassInfo.STATUS_FALSE);
//                    }
//                } else {
//                    project.setClassStatus(ClassInfo.STATUS_FALSE);
//                }
                if (classInfo != null) {
                    project.setClassStatus(classInfo.getStatus());
                }
            } else {
                project.setClassStatus(ClassInfo.STATUS_FALSE);
            }
            project.setClassInfo(classInfo);
            project.setOrganization(organization);
            project.setMember(member);
            project.setSpecial("否");
            project.setProjectType(result.getValue(configurationValue.NAME));
            project.setProjectLevel(result.getValue(configurationValue1.NAME));
            project.setProjectMonth(project.getYear().toString() + '-' + project.getMonth().toString());
            return project;
        });
        List<ProjectApproval> find = projectApprovalDao.find(id);
        if (find.size() != 0) {
            ProjectApproval projectApproval = find.get(0);
            p.setProjectApproval(projectApproval);
        }
        if(p.getDays()!=null&&p.getDays()<3){
            p.setSpecial("是");
        }
        return p;
    }

    /**
     * 编辑
     */
    @Override
    public Project update(String id, Optional<String> name, Optional<String> code, Optional<String> contactEmail, Optional<String> contactMemberId, Optional<String> organizationId,
                          Optional<String> contactPhone, Optional<Integer> year, Optional<Integer> month, Optional<Integer> amount, Optional<String> object, Optional<Integer> days,
                          Optional<String> address, Optional<String> typeId, Optional<Integer> isOutSide, Optional<String> surveyType, Optional<Long> arriveDate, Optional<Long> returnDate, Optional<String> target, Optional<Integer> status, Optional<String> cost
            , Optional<Integer> beforeAmount, Optional<String> createMember, Optional<Integer> resourceStatus) {
        // TODO Auto-generated method stub
        /*SimpleDateFormat sdf=new SimpleDateFormat("yyyy-MM-dd");
        Integer reachYear = null;
        Integer reachMonth = null;
        if (arriveDate.isPresent()) {
            String d = sdf.format(new Date(arriveDate.get()));
            reachYear = Integer.parseInt(d.substring(0, 4));
            reachMonth = Integer.parseInt(d.substring(5,7));
        }*/
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Project project = get(id);
        int originalAmount = project.getAmount();
        name.ifPresent(project::setName);
        organizationId.ifPresent(project::setOrganizationId);
        contactMemberId.ifPresent(project::setContactMemberId);
        address.ifPresent(project::setAddress);
        typeId.ifPresent(project::setTypeId);
        year.ifPresent(project::setYear);
        cost.ifPresent(project::setCost);
        month.ifPresent(project::setMonth);
        amount.ifPresent(project::setAmount);
        object.ifPresent(project::setObject);
        days.ifPresent(project::setDays);
        code.ifPresent(project::setCode);
        contactEmail.ifPresent(project::setContactEmail);
        status.ifPresent(project::setStatus);
        contactPhone.ifPresent(project::setContactPhone);
        project.setStudentStatus(1);
        Integer findSource = organizationService.findSource(project.getOrganizationId());
        surveyType.ifPresent(project::setSurveyType);
        target.ifPresent(project::setTarget);
        //添加预定人
        project.setReservationTime(System.currentTimeMillis());
        project.setReservationMember(createMember.orElse(null));
        dao.update(project);
        if (project.getClassInfo() != null && project.getClassInfo().getId() != null) {
            ClassInfo classInfo = project.getClassInfo();
            Long aDate = classInfo.getArriveDate();
            Long rDate = classInfo.getReturnDate();
            isOutSide.ifPresent(classInfo::setIsOutside);
            surveyType.ifPresent(classInfo::setSurveyType);
            arriveDate.ifPresent(classInfo::setArriveDate);
            returnDate.ifPresent(classInfo::setReturnDate);
            target.ifPresent(classInfo::setTarget);
            resourceStatus.ifPresent(classInfo::setResourceStatus);
            /*if (arriveDate.isPresent()){
                classInfo.setImplementationYear(reachYear);
                classInfo.setImplementationMonth(reachMonth);
            }*/
            classInfo.setProjectSource(findSource);
            classInfoDao.update(classInfo);
            Optional<ResearchQuestionary> researchQuestionary = questionnaireSurveyService.getData(classInfo.getId());
            if (researchQuestionary.isPresent()) {
                String detailM = null;
                ResearchQuestionary research = researchQuestionary.get();
                String ad = sdf.format(new Date(classInfo.getArriveDate()));
                String rd = sdf.format(new Date(classInfo.getReturnDate()));
                String arrDate = ad.replace('-', '.');
                String retDate = rd.replaceAll("-", ".");
                detailM = "亲爱的学员:  " +
                        "为了了解您对本次培训项目的意见，帮助公司持续优化提升培训质量，请您评价如下各项指标，并填写文字建议。谢谢您的大力支持！          " +
                        "培训项目：" + project.getName() + "    计划时间" + arrDate + "至" + retDate;
                if (detailM != null) {
                    research.setQuestionaryDetail(detailM);
                    researchQuestionaryDao.update(research);
                }
            }
            if (isOutSide.isPresent() && isOutSide.get() == 0) {
                if (project.getStatus() == Project.STATUS_AGREE && amount.isPresent() && amount.get() != originalAmount) {
                    logger.error("#OCCUPY#计划通过修改人数，ID：" + id + ",原来人数：" + originalAmount + ",现人数：" + amount.get());
                    occupyService.correctOccupyByProjectUpdate(id, originalAmount, amount.get());
                }
                //如果预定资源时更改了报道日或返程日，则重新占用资源
                if (arriveDate.isPresent() && returnDate.isPresent() &&
                        (arriveDate.get().longValue() != aDate.longValue() || returnDate.get().longValue() != rDate.longValue())) {
                    //先释放之前的资源，再重新占用新的资源
                    logger.error("#OCCUPY#待审核状态更改报道日和返程日，ID：" + project.getId() + ",原报道日：" + aDate.longValue() + ",原返程日：" + rDate.longValue()
                            + ",现报道日：" + arriveDate.get().longValue() + ",现返程日：" + returnDate.get().longValue());
                    sender.send(MessageTypeContent.TRAIN_PROJECT_APPROVAL_DELETE,
                            MessageHeaderContent.ID, project.getId(),
                            MessageHeaderContent.START_TIME, String.valueOf(aDate.longValue()),
                            MessageHeaderContent.END_TIME, String.valueOf(rDate.longValue())
                    );
                    sender.send(MessageTypeContent.TRAIN_PROJECT_APPROVAL_UPDATE,
                            MessageHeaderContent.ID, project.getId(),
                            MessageHeaderContent.START_TIME, String.valueOf(arriveDate.get().longValue()),
                            MessageHeaderContent.END_TIME, String.valueOf(returnDate.get().longValue())
                    );
                }
            }
            } else {
                classInfoService.insert(id,
                        Optional.empty(),
                        Optional.empty(),
                        arriveDate,
                        returnDate,
                        isOutSide,
                        surveyType,
                        target,
                        Optional.empty(),
                        Optional.empty(),
                        Optional.empty(),
                        Optional.empty(),
                        Optional.empty(),
                        Optional.empty(),
                    /*Optional.of(reachYear),
                    Optional.of(reachMonth),*/
                        Optional.of(ClassInfo.STATUS_FALSE),
                        Optional.of(project.getCreateMember()),
                        resourceStatus,
                        0.00,
                        0.00,
                        Optional.of(findSource),
                        project.getOrganizationId(), 2, 0);
        }
        //sender.send(MessageTypeContent.TRAIN_CLASS_UPDATE, MessageHeaderContent.ID, classInfo.getId());
        //如果计划审批已经通过并且计划人数做了修改，需要修改相应的可用资源额度
        sender.send(MessageTypeContent.TRAIN_PROJECT_UPDATE, MessageHeaderContent.ID, project.getId());
        return project;
    }

    @Override
    public Project approval(String id, Long arriveDate, Long returnDate, Integer isOutside,
                            String address, Integer status, Optional<String> suggestion, Optional<String> surveyType, Optional<String> target, String stas, Optional<String> createMemberId, Optional<Integer> resourceStatus,Integer isPartyCadre) {
        return approval(id,arriveDate,returnDate,isOutside,address,status,suggestion,surveyType,target,stas,createMemberId,resourceStatus,isPartyCadre,
                Optional.empty());
    }
    /**
     * 审核预定
     */
    @Override
    public Project approval(String id, Long arriveDate, Long returnDate, Integer isOutside,
                            String address, Integer status, Optional<String> suggestion, Optional<String> surveyType, Optional<String> target, String stas, Optional<String> createMemberId, Optional<Integer> resourceStatus, Integer isPartyCadre,
                            Optional<Integer> aduitStatus) {
        // TODO Auto-generated method stub
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");

        String d = sdf.format(new Date(returnDate));
        Integer reachYear = null;
        Integer reachMonth = null;
        reachYear = Integer.parseInt(d.substring(0, 4));
        reachMonth = Integer.parseInt(d.substring(5, 7));
        Integer stu = 1;//默认实时状态  1：未实施
        Integer sort = 2;//默认班级排序 1:实施中
        Project project = get(id);
        Integer findSource = organizationService.findSource(project.getOrganizationId());
        //判断报道日与返程日是否超过可用资源
        if (status == Project.STATUS_AGREE && project.getStatus() != Project.STATUS_AGREE && project.getStatus() != Project.STATUS_APPROVAL) {//同意或待审核
            if (isOutside == 0 || "0".equals(isOutside)) {
                logger.error("#OCCUPY#审核资源同意，ID：" + project.getId(), "报道日：" + arriveDate.toString() + ",返程日：" + returnDate.toString());
                //更新可用资源表
                sender.send(MessageTypeContent.TRAIN_PROJECT_APPROVAL_UPDATE,
                        MessageHeaderContent.ID, project.getId(),
                        MessageHeaderContent.START_TIME, arriveDate.toString(),
                        MessageHeaderContent.END_TIME, returnDate.toString()
                );
            }
//            occupyService.updateOccupyByDate(arriveDate, returnDate, project.getAmount(), project.getDays());
        }
        //由于在班级实施前，可以自由审核计划，所以可能会出现审批通过后再审批为不通过，或者审批不通过后再审批为通过的情况
        //如果通过后再审批为不通过，则需要将之前占用的资源释放
        if ((status == Project.STATUS_REFUSE || status == Project.STATUS_RESOURCES_FULL)
                && (project.getStatus() == Project.STATUS_AGREE || project.getStatus() == Project.STATUS_APPROVAL)
                && project.getClassInfo() != null
                && project.getClassInfo().getArriveDate() != null) {
            //更新可用资源表
            Long proReturnDate = project.getClassInfo().getReturnDate();
            if ((isOutside == 0 || "0".equals(isOutside))) {
                sender.send(MessageTypeContent.TRAIN_PROJECT_APPROVAL_DELETE,
                        MessageHeaderContent.ID, project.getId(),
                        MessageHeaderContent.START_TIME, project.getClassInfo().getArriveDate().toString(),
                        MessageHeaderContent.END_TIME, proReturnDate.toString()
                );
                logger.error("#OCCUPY#审核资源不通过，ID：" + project.getId() + ",报道日：" + project.getClassInfo().getArriveDate().toString() +
                        ",返程日：" + proReturnDate.toString());
//            occupyService.updateOccupyByDate(project.getClassInfo().getArriveDate(),
//                    project.getClassInfo().getReturnDate(),
//                    project.getAmount() * -1,
//                    project.getDays());
            }
        }
        //如果通过之后再审批为通过，可能报道日与返程日改变了(fk)
        if (status == Project.STATUS_AGREE && (project.getStatus() == Project.STATUS_AGREE || project.getStatus() == Project.STATUS_APPROVAL)) {//同意或待审核
            //可能改变外部审批
            if (isOutside == 0 || "0".equals(isOutside)) {
                if (project.getClassInfo() != null && (project.getClassInfo().getArriveDate() != arriveDate ||
                        project.getClassInfo().getReturnDate() != returnDate)) {
                    Long proReturnDate = project.getClassInfo().getReturnDate();
                    //先释放之前的资源，再重新占用新的资源
                    if ((project.getClassInfo().getIsOutside()!= null && project.getClassInfo().getIsOutside().equals(0))) {
                        sender.send(MessageTypeContent.TRAIN_PROJECT_APPROVAL_DELETE,
                                MessageHeaderContent.ID, project.getId(),
                                MessageHeaderContent.START_TIME, project.getClassInfo().getArriveDate().toString(),
                                MessageHeaderContent.END_TIME, proReturnDate.toString()
                        );
                    }
                    sender.send(MessageTypeContent.TRAIN_PROJECT_APPROVAL_UPDATE,
                            MessageHeaderContent.ID, project.getId(),
                            MessageHeaderContent.START_TIME, arriveDate.toString(),
                            MessageHeaderContent.END_TIME, returnDate.toString());
                    logger.error("#OCCUPY#审核资源通过，但报道日或返程日变了，ID：" + project.getId() + ",原报道日：" + project.getClassInfo().getArriveDate().toString() +
                            ",原返程日：" + project.getClassInfo().getReturnDate().toString() +
                            ",现报道日：" + arriveDate.toString() + ",现返程日：" + returnDate.toString());

                }
            }else if((isOutside == 1 || "1".equals(isOutside) || isOutside==2)&& (project.getClassInfo().getIsOutside()!= null && project.getClassInfo().getIsOutside().equals(0))){
                sender.send(MessageTypeContent.TRAIN_PROJECT_APPROVAL_DELETE,
                        MessageHeaderContent.ID, project.getId(),
                        MessageHeaderContent.START_TIME, project.getClassInfo().getArriveDate().toString(),
                        MessageHeaderContent.END_TIME, project.getClassInfo().getReturnDate().toString());
            }
        }
        project.setStatus(status);
        project.setAddress(address);
        project.setSurveyType(surveyType.orElse(null));
        project.setTarget(target.orElse(null));
        if (stas.equals("reserve")) {
            project.setStudentStatus(0);
        }
        project.setIsPartyCadre(isPartyCadre);//是否是党干部培训班
        //如果是审核状态，说明数据是学员端来的
        if(aduitStatus.isPresent() && Objects.equals(aduitStatus, Project.AUDIT_STATUS_TRUE)){
            projectApprovalDao.update(project.getId(), status, createMemberId);
        } else {
            //如果是管理端，审批人跟预定人是一个
            projectApprovalDao.insert(project.getId(), status, suggestion, createMemberId, createMemberId);
            project.setReservationMember(createMemberId.orElse(null));
            project.setReservationTime(System.currentTimeMillis());
        }
        dao.update(project);
        Optional<ClassInfo> c = classInfoDao.execute(x -> {
            return x.selectFrom(CLASS_INFO).where(CLASS_INFO.PROJECT_ID.eq(id), CLASS_INFO.DELETE_FLAG.eq(Project.DELETE_FLASE)).fetchOptionalInto(ClassInfo.class);
        });
        String classId = null;
        if (status == 3) {
            if (c.isPresent()) {
                ClassInfo classInfo = c.get();
                classId = classInfo.getId();
                classInfo.setArriveDate(arriveDate);
                classInfo.setReturnDate(returnDate);
                classInfo.setIsOutside(isOutside);
                classInfo.setImplementationYear(reachYear);
                classInfo.setImplementationMonth(reachMonth);
                classInfo.setSurveyType(surveyType.orElse(null));
                classInfo.setTarget(target.orElse(null));
                resourceStatus.ifPresent(classInfo::setResourceStatus);
                Integer classStatus = ClassInfo.STATUS_FALSE;
                Integer classSort = ClassInfo.SORT_ING;
                long value = returnDate + 86400000;
                if(System.currentTimeMillis() >= arriveDate && System.currentTimeMillis() < value) {
                    classStatus = ClassInfo.STATUS_ING;
                    classSort = ClassInfo.SORT_FALSE;
                }else if(arriveDate.equals(returnDate)){
                        if(System.currentTimeMillis()>returnDate && returnDate> (System.currentTimeMillis()-86400000) ){
                            classStatus = ClassInfo.STATUS_ING;
                            classSort = ClassInfo.SORT_FALSE;
                    }
                }else if(System.currentTimeMillis() >= value) {
                    classStatus = ClassInfo.STATUS_TRUE;
                    classSort = ClassInfo.SORT_TRUE;
                }
                classInfo.setStatus(classStatus);
                classInfo.setSort(classSort);
                classInfo.setProjectSource(findSource);
                classInfoDao.update(classInfo);
                sender.send(MessageTypeContent.TRAIN_CLASS_UPDATE, MessageHeaderContent.ID, classInfo.getId());
                sender.send(MessageTypeContent.UPDATE_ARRIVEDATE, MessageHeaderContent.CLASSID, classInfo.getId(),
                        MessageHeaderContent.MEMBERID, createMemberId.get());
                Optional<ClassInfo> optional = classInfoDao.fetchOne(CLASS_INFO.DELETE_FLAG.eq(ClassInfo.DELETE_FLASE)
                        .and(CLASS_INFO.NOTICE.eq(ClassInfo.CLASS_IS_NOTICE))
                        .and(CLASS_INFO.ID.eq(classInfo.getId())));
                if (optional.isPresent()) {
                    sender.send(MessageTypeContent.TRAIN_CLASS_UPDATE_FOR_ACTIVITY, MessageHeaderContent.ID, optional.get().getId());
                }
                Optional<ResearchQuestionary> researchQuestionary = questionnaireSurveyService.getData(classInfo.getId());
                if (researchQuestionary.isPresent()) {
                    String detailM = null;
                    ResearchQuestionary research = researchQuestionary.get();
                    String ad = sdf.format(new Date(arriveDate));
                    String rd = sdf.format(new Date(returnDate));
                    String arrDate = ad.replace('-', '.');
                    String retDate = rd.replaceAll("-", ".");
                    detailM = "亲爱的学员:  " +
                            "为了了解您对本次培训项目的意见，帮助公司持续优化提升培训质量，请您评价如下各项指标，并填写文字建议。谢谢您的大力支持！          " +
                            "培训项目：" + project.getName() + "    计划时间" + arrDate + "至" + retDate;
                    if (detailM != null) {
                        research.setQuestionaryDetail(detailM);
                        researchQuestionaryDao.update(research);
                    }
                }
            } else {
                if (returnDate != null && System.currentTimeMillis() > returnDate) {
                if(arriveDate.equals(returnDate)){
                    if(System.currentTimeMillis()>returnDate && returnDate> (System.currentTimeMillis()-86400000) ){
                        stu = ClassInfo.STATUS_ING;
                        sort = ClassInfo.SORT_FALSE;
                    }
                }else{
                    stu = ClassInfo.STATUS_TRUE;
                    sort = ClassInfo.SORT_TRUE;
                }
                } else {
                    if (arriveDate != null && System.currentTimeMillis() > arriveDate) {
                        stu = ClassInfo.STATUS_ING;
                        sort = ClassInfo.SORT_FALSE;
                    } else {
                        stu = ClassInfo.STATUS_FALSE;
                        sort = ClassInfo.SORT_ING;
                    }
                }

               ClassInfo classInfo =  classInfoService.insert(id,
                        Optional.empty(),
                        Optional.empty(),
                        Optional.ofNullable(arriveDate),
                        Optional.ofNullable(returnDate),
                        Optional.ofNullable(isOutside),
                        surveyType,
                        target,
                        Optional.empty(),
                        Optional.empty(),
                        Optional.empty(),
                        Optional.empty(),
                        Optional.ofNullable(reachYear),
                        Optional.ofNullable(reachMonth),
                        Optional.of(stu),
                        Optional.of(project.getCreateMember()),
                        resourceStatus,
                        0.00,
                        0.00,
                        Optional.ofNullable(findSource),
                        project.getOrganizationId(), sort, 0);
                 classId = classInfo.getId();

                sender.send(MessageTypeContent.PLA_IMPLEMENTATION_SYNCHRONOUS, MessageHeaderContent.ID, classId);
            }

               int num =  classRequiredService.findProject(project.getId(),classId);
            if(num==0){
                classRequiredService.updateProject(project.getId(),classId,createMemberId.get());
            }
            sender.send(MessageTypeContent.TRAIN_PROJECT_UPDATE, MessageHeaderContent.ID, project.getId());

        } else {
            if (c.isPresent()) {
                ClassInfo classInfo = c.get();
                classInfoDao.delete(classInfo.getId());
                deleteDataTrainCommonDao.insert(DeleteDataTrain.getDeleteData(SETTLEMENT_CONFIGURATION_VALUE.getName(), id,""));

                traineeService.deleteTraineesByClassId(classInfo.getId());
                sender.send(MessageTypeContent.TRAIN_CLASS_DELETE, MessageHeaderContent.ID, classInfo.getId());
                sender.send(MessageTypeContent.TRAIN_CLASS_DELETE_SMART_CAMPUS, MessageHeaderContent.ID, classInfo.getId());
            }
        }
        return project;
    }

    @Override
    public List<Project> findProjectByDate(Integer year, Integer month) {
        String top = year + "-" + String.format("%02d", month) + "-01";
        String bottom = month == 12 ? (year + 1) + "-01-01" : year + "-" + String.format("%02d", month + 1) + "-01";
        Long topLong = StringUtils.dateString2OptionalLong(top);
        Long bottomLong = StringUtils.dateString2OptionalLong(bottom);
        return dao.execute(x -> {
            Result<Record> record = x.select(Fields.start()
                    .add(PROJECT.NAME, PROJECT.AMOUNT, PROJECT.DAYS)
                    .add(CLASS_INFO.ARRIVE_DATE).add(CLASS_INFO.IS_OUTSIDE)
                    .end()).from(PROJECT)
                    .innerJoin(CLASS_INFO).on(PROJECT.ID.eq(CLASS_INFO.PROJECT_ID))
                    .where(PROJECT.DELETE_FLAG.eq(Project.DELETE_FLASE).and(CLASS_INFO.DELETE_FLAG.eq(Project.DELETE_FLASE))
                            .and(PROJECT.STATUS.eq(Project.STATUS_AGREE).or(PROJECT.STATUS.eq(Project.STATUS_APPROVAL)))
                            .and(CLASS_INFO.RETURN_DATE.greaterOrEqual(topLong).and(CLASS_INFO.RETURN_DATE.lessThan(bottomLong))
                                    .or(CLASS_INFO.ARRIVE_DATE.greaterOrEqual(topLong).and(CLASS_INFO.ARRIVE_DATE.lessThan(bottomLong)))
                                    .or(CLASS_INFO.ARRIVE_DATE.lessThan(topLong).and(CLASS_INFO.RETURN_DATE.greaterOrEqual(bottomLong)))))
                    .fetch();
            List<Project> projectList = record.into(Project.class);
            List<ClassInfo> classInfoList = record.into(ClassInfo.class);
            for (int i = 0; i < projectList.size(); i++) {
                projectList.get(i).setClassInfo(classInfoList.get(i));
            }
            return projectList;
        });
    }

    @Override
    public Optional<Project> findByCode(String code) {
        return dao.execute(x -> {
            return x.selectFrom(PROJECT).where(PROJECT.CODE.eq(code), PROJECT.DELETE_FLAG.eq(Project.DELETE_FLASE)).fetchOptionalInto(Project.class);
        });
    }

    /**
     * 用户是否为需求方
     *
     * @param memberId
     * @return
     */
    @Override
    public Integer findContract(String memberId) {
        List<Project> projectList = dao.execute(x -> {
            return x.select(Fields.start().add(PROJECT.ID).end()).from(PROJECT).where(PROJECT.DELETE_FLAG.eq(Project.DELETE_FLASE).and(PROJECT.CONTACT_MEMBER_ID.eq(memberId)))
                    .fetchInto(Project.class);
//            return x.selectFrom(PROJECT).where(PROJECT.CONTACT_MEMBER_ID.eq(memberId).and(PROJECT.DELETE_FLAG.ne(Project.DELETE_TRUE)))
//            .fetchInto(Project.class);
        });
        if (projectList != null && projectList.size() > 0) {
            return 1;
        }
        return 0;
    }

    @Override
    public int delete(String id) {
        // TODO Auto-generated method stub
        Project t = get(id);
        if (t != null) {
            Integer stu = t.getStatus();
            Optional<ClassInfo> classInfo = classInfoDao.execute(x -> {
                return x.selectFrom(CLASS_INFO).where(CLASS_INFO.PROJECT_ID.eq(t.getId()), CLASS_INFO.DELETE_FLAG.eq(Project.DELETE_FLASE)).fetchOptionalInto(ClassInfo.class);
            });
            if (classInfo.isPresent()) {
                ClassInfo c = classInfo.get();
                if (c.getNotice() != null && c.getNotice() == 1) {
                    return 4;
                } else {
                    if (stu != null && !stu.equals(3)) {
                        //删除培训班时需要将可用资源额度还原
                        if(c.getIsOutside()!=null&&c.getIsOutside()==0){
                            logger.error("#OCCUPY#删除计划释放资源，ID：" + id);
                            occupyService.correctOccupyByProjectDelete(id);
                        }
                        traineeService.deleteTraineesByClassId(c.getId());
                        c.setDeleteFlag(Project.DELETE_TRUE);
                        c.setGroupId(null);
                        classInfoDao.update(c);
                        sender.send(MessageTypeContent.TRAIN_CLASS_DELETE, MessageHeaderContent.ID, c.getId());
                        sender.send(MessageTypeContent.TRAIN_CLASS_DELETE_SMART_CAMPUS, MessageHeaderContent.ID, c.getId());
                        classRequiredCommonDao.delete(CLASS_REQUIRED.CLASS_ID.eq(c.getId()));
                        t.setDeleteFlag(Project.DELETE_TRUE);
                        dao.update(t);
                        sender.send(MessageTypeContent.TRAIN_PROJECT_DELETE, MessageHeaderContent.ID, t.getId());
                        return 0;
                    } else {
                        if (c.getArriveDate() != null && c.getArriveDate() > System.currentTimeMillis()) {
                            //删除培训班时需要将可用资源额度还原
                            if(c.getIsOutside()!=null&&c.getIsOutside()==0){
                                logger.error("#OCCUPY#删除计划释放资源，ID：" + id);
                                occupyService.correctOccupyByProjectDelete(id);
                            }
                            traineeService.deleteTraineesByClassId(c.getId());
                            c.setDeleteFlag(Project.DELETE_TRUE);
                            c.setGroupId(null);
                            classInfoDao.update(c);
                            sender.send(MessageTypeContent.TRAIN_CLASS_DELETE, MessageHeaderContent.ID, c.getId());
                            sender.send(MessageTypeContent.TRAIN_CLASS_DELETE_SMART_CAMPUS, MessageHeaderContent.ID, c.getId());
                            classRequiredCommonDao.delete(CLASS_REQUIRED.CLASS_ID.eq(c.getId()));
                            t.setDeleteFlag(Project.DELETE_TRUE);
                            dao.update(t);
                            sender.send(MessageTypeContent.TRAIN_PROJECT_DELETE, MessageHeaderContent.ID, t.getId());
                            return 0;
                        } else if (c.getArriveDate() != null && c.getReturnDate() != null && c.getArriveDate() < System.currentTimeMillis()
                                && System.currentTimeMillis() < c.getReturnDate()) {
                            return 1;
                        } else {
                            return 2;
                        }
                    }
                }
            } else {
                t.setDeleteFlag(Project.DELETE_TRUE);
                dao.update(t);
                sender.send(MessageTypeContent.TRAIN_PROJECT_DELETE, MessageHeaderContent.ID, t.getId());
                return 0;
            }
        } else {
            return 3;
        }
    }

    @Override
    public List<Project> findDownProject(Optional<Long> arriveDate, String memberId, List<String> organizationIds) {
        // TODO Auto-generated method stub
        if (arriveDate.isPresent()) {
            long l = arriveDate.get();
            Date date = new Date(l);
            LocalDate localDate = date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();


            List<Project> project2 = new ArrayList<>();

            List<String> claIds = new ArrayList<>();
            List<String> proIds = new ArrayList<>();
            List<String> teaIds = new ArrayList<>();
            List<String> orgIds = new ArrayList<>();
            List<String> memIds = new ArrayList<>();
            List<Project> list = dao.execute(d -> {
                Table<Record1<String>> basic = (d.select(PROJECT.ID)
                        .from(PROJECT)
                        .leftJoin(CLASS_INFO).on(CLASS_INFO.PROJECT_ID.eq(PROJECT.ID))
                        .where(PROJECT.DELETE_FLAG.eq(Project.DELETE_FLASE))
                        .and(CLASS_INFO.IMPLEMENTATION_YEAR.eq(localDate.getYear())
                                                           .and(CLASS_INFO.IMPLEMENTATION_MONTH.eq(localDate.getMonthValue()))
                                                           .or(PROJECT.STATUS.eq(STATUS_APPROVAL)))
                        .and(CLASS_INFO.DELETE_FLAG.eq(Project.DELETE_FLASE))
                        .and(PROJECT.ORGANIZATION_ID.in(organizationIds))
                        .groupBy(PROJECT.ID)
                        .orderBy(PROJECT.CREATE_TIME.asc())
                ).asTable("b");

                return d.select(Fields.start()
                        .add(PROJECT.CODE)
                        .add(PROJECT.ID)
                        .add(PROJECT.NAME)
                        .add(PROJECT.CONTACT_EMAIL)
                        .add(PROJECT.CONTACT_PHONE)
                        .add(PROJECT.OBJECT)
                        .add(PROJECT.AMOUNT)
                        .add(PROJECT.DAYS)
                        .add(PROJECT.CREATE_TIME.as("projectCreateTime"))
                        .add(PROJECT.ORGANIZATION_ID)
                        .add(PROJECT.CONTACT_MEMBER_ID,PROJECT.RESERVATION_TIME,PROJECT.RESERVATION_MEMBER)
                        .add(CLASS_INFO.ARRIVE_DATE)
                        .add(CLASS_INFO.RETURN_DATE)
                        .add(CLASS_INFO.IS_OUTSIDE)
                        .add(CLASS_INFO.ID, CLASS_INFO.CLASS_INFO_TYPE, CLASS_INFO.CLASS_TEACHER)
                        .add(PROJECT_APPROVAL.CREATE_TIME, PROJECT_APPROVAL.SUGGESTION,PROJECT.IS_PARTY_CADRE)
                        .end())
                        .from(PROJECT.innerJoin(basic).on(basic.field(PROJECT.ID).eq(PROJECT.ID)))
                        .leftJoin(CLASS_INFO)
                        .on(PROJECT.ID.eq(CLASS_INFO.PROJECT_ID))
                        .leftJoin(PROJECT_APPROVAL)
                        .on(PROJECT_APPROVAL.PROJECT_ID.eq(PROJECT.ID))
                        .groupBy(PROJECT.ID)
                        .fetch(r -> {
                            boolean flag = true;
                            for (Project pro : project2) {
                                if (pro.getId().equals(r.getValue(PROJECT.ID))) {
                                    flag = false;
                                    break;
                                }
                            }
                            if (flag) {
                                Project p = r.into(Project.class);
                                p.setCreateTime(r.getValue("projectCreateTime", Long.class));
                                ClassInfo c = r.into(ClassInfo.class);
                                p.setSuggestion(r.getValue(PROJECT_APPROVAL.SUGGESTION));
                                proIds.add(r.getValue(PROJECT.ID));
                                claIds.add(r.getValue(CLASS_INFO.ID));
                                if (r.getValue(CLASS_INFO.CLASS_TEACHER) != null) {
                                    teaIds.add(r.getValue(CLASS_INFO.CLASS_TEACHER));
                                }
                                orgIds.add(r.getValue(PROJECT.ORGANIZATION_ID));
                                memIds.add(r.getValue(PROJECT.CONTACT_MEMBER_ID));
                                memIds.add(r.getValue(PROJECT.RESERVATION_MEMBER));
                                p.setClassInfo(c);
                                project2.add(p);
                            }
                            return null;
                        });
            });

            Map<String, Member> memberMap = memberDao.execute(x -> x.select(MEMBER.ID, MEMBER.FULL_NAME).from(MEMBER).where(MEMBER.ID.in(memIds)))
                    .fetch(r -> r.into(Member.class)).stream()
                    .collect(Collectors.toMap(Member::getId, m -> m));

            Map<String, Member> teaMap = memberDao.execute(x -> x.select(MEMBER.ID, MEMBER.FULL_NAME, MEMBER.PHONE_NUMBER).from(MEMBER).where(MEMBER.ID.in(teaIds)))
                    .fetch(r -> {
                        Member member = new Member();
                        member.setId(r.getValue(MEMBER.ID));
                        member.setFullName(r.getValue(MEMBER.FULL_NAME));
                        member.setPhoneNumber(SM4Utils.decryptDataCBC(r.getValue(MEMBER.PHONE_NUMBER)));
                        return member;
                    }).stream()
                    .collect(Collectors.toMap(Member::getId, m -> m));

            Map<String, Organization> orgMap = orgDao.execute(x -> x.select(ORGANIZATION.ID, ORGANIZATION.NAME).from(ORGANIZATION).where(ORGANIZATION.ID.in(orgIds)))
                    .fetch(r -> r.into(Organization.class)).stream()
                    .collect(Collectors.toMap(Organization::getId, o -> o));

            Map<String, ConfigurationValue> cvMap = configurationValueDao.execute(x -> x.select(CONFIGURATION_VALUE.ID, CONFIGURATION_VALUE.NAME, PROJECT.ID)
                    .from(CONFIGURATION_VALUE)
                    .innerJoin(PROJECT).on(PROJECT.TYPE_ID.eq(CONFIGURATION_VALUE.ID))
                    .where(PROJECT.ID.in(proIds)
                            .and(CONFIGURATION_VALUE.DELETE_FLAG.eq(0))
                            .and(CONFIGURATION_VALUE.TYPE_ID.eq(2))))
                    .fetch(b -> {
                        ConfigurationValue taskReviewer = b.into(ConfigurationValue.class);
                        taskReviewer.setId(b.getValue(CONFIGURATION_VALUE.ID));
                        taskReviewer.setName(b.getValue(CONFIGURATION_VALUE.NAME));
                        taskReviewer.setProId(b.getValue(PROJECT.ID));
                        return taskReviewer;
                    }).stream()
                    .collect(Collectors.toMap(ConfigurationValue::getProId, o -> o));

            Map<String, ConfigurationValue> cvMap2 = configurationValueDao.execute(x -> x.select(CONFIGURATION_VALUE.ID, CONFIGURATION_VALUE.NAME, PROJECT.ID)
                    .from(CONFIGURATION_VALUE)
                    .innerJoin(PROJECT).on(PROJECT.COST.eq(CONFIGURATION_VALUE.ID))
                    .where(PROJECT.ID.in(proIds)
                            .and(CONFIGURATION_VALUE.DELETE_FLAG.eq(0))
                            .and(CONFIGURATION_VALUE.TYPE_ID.eq(1))))
                    .fetch(b -> {
                        ConfigurationValue taskReviewer = b.into(ConfigurationValue.class);
                        taskReviewer.setId(b.getValue(CONFIGURATION_VALUE.ID));
                        taskReviewer.setName(b.getValue(CONFIGURATION_VALUE.NAME));
                        taskReviewer.setProId(b.getValue(PROJECT.ID));
                        return taskReviewer;
                    }).stream()
                    .collect(Collectors.toMap(ConfigurationValue::getProId, o -> o));

            Map<String, List<ConfigurationValue>> restaurantsConfig = configurationValueDao.execute(x -> x.select(Fields.start().add(CONFIGURATION_VALUE).add(CLASS_RESOURCE.CLASS_ID).end())
                    .from(CONFIGURATION_VALUE)
                    .innerJoin(CLASS_RESOURCE).on(CLASS_RESOURCE.DINING_ROOM.eq(CONFIGURATION_VALUE.ID))
                    .where(CLASS_RESOURCE.CLASS_ID.in(claIds))
                    .and(CONFIGURATION_VALUE.DELETE_FLAG.eq(0))
                    .and(CONFIGURATION_VALUE.TYPE_ID.eq(14))
            ).fetch(b -> {
                ConfigurationValue taskReviewer = b.into(ConfigurationValue.class);
                taskReviewer.setId(b.getValue(CONFIGURATION_VALUE.ID));
                taskReviewer.setName(b.getValue(CONFIGURATION_VALUE.NAME));
                taskReviewer.setClaId(b.getValue(CLASS_RESOURCE.CLASS_ID));
                return taskReviewer;
            }).stream().collect(Collectors.groupingBy(ConfigurationValue::getClaId));

            Map<String, List<ConfigurationValue>> guestroomsConfig = configurationValueDao.execute(x -> x.select(Fields.start().add(CONFIGURATION_VALUE).add(CLASS_RESOURCE.CLASS_ID).end())
                    .from(CONFIGURATION_VALUE)
                    .innerJoin(CLASS_RESOURCE).on(CLASS_RESOURCE.REST_ROOM.eq(CONFIGURATION_VALUE.ID))
                    .where(CLASS_RESOURCE.CLASS_ID.in(claIds))
                    .and(CONFIGURATION_VALUE.DELETE_FLAG.eq(0))
                    .and(CONFIGURATION_VALUE.TYPE_ID.eq(15))
            ).fetch(b -> {
                ConfigurationValue taskReviewer = b.into(ConfigurationValue.class);
                taskReviewer.setId(b.getValue(CONFIGURATION_VALUE.ID));
                taskReviewer.setName(b.getValue(CONFIGURATION_VALUE.NAME));
                taskReviewer.setClaId(b.getValue(CLASS_RESOURCE.CLASS_ID));
                return taskReviewer;
            }).stream().collect(Collectors.groupingBy(ConfigurationValue::getClaId));

            Map<String, List<ClassroomConfiguration>> classroomsConfig = classroomConfigurationDao.execute(x -> x.select(Fields.start().add(CLASSROOM_CONFIGURATION).add(CLASS_RESOURCE.CLASS_ID).end())
                    .from(CLASSROOM_CONFIGURATION)
                    .innerJoin(CLASS_RESOURCE).on(CLASS_RESOURCE.CLASSROOM.eq(CLASSROOM_CONFIGURATION.ID))
                    .where(CLASS_RESOURCE.CLASS_ID.in(claIds))
                    .and(CLASSROOM_CONFIGURATION.DELETE_FLAG.eq(0))
            ).fetch(b -> {
                ClassroomConfiguration taskReviewer = b.into(ClassroomConfiguration.class);
                taskReviewer.setId(b.getValue(CLASSROOM_CONFIGURATION.ID));
                taskReviewer.setClassroom(b.getValue(CLASSROOM_CONFIGURATION.CLASSROOM));
                taskReviewer.setClaId(b.getValue(CLASS_RESOURCE.CLASS_ID));
                return taskReviewer;
            }).stream().collect(Collectors.groupingBy(ClassroomConfiguration::getClaId));

            List<ProjectApproval> approvals = projectApprovalDao.findByProjectIds(proIds);
            Map<String, ProjectApproval> approvalMap;
            if(!CollectionUtils.isEmpty(approvals)){
                approvalMap = approvals.stream().collect(Collectors.toMap(ProjectApproval::getProjectId, Function.identity(), (p, q) -> q));
            } else {
                approvalMap = new HashMap<>();
            }

            project2.forEach(r -> {
                String pId = r.getId();
                String cId = r.getClassInfo().getId();
                String tId = r.getClassInfo().getClassTeacher();
                String mId = r.getContactMemberId();
                String oId = r.getOrganizationId();
                String typId = r.getTypeId();
                String cost = r.getCost();
                String cmId = null;
                if (tId != null && !teaMap.isEmpty() && teaMap.get(tId) != null) {
                    ClassInfo c = r.getClassInfo();
                    c.setTeacher(teaMap.get(tId).getFullName());
                    c.setTeacherPhone(teaMap.get(tId).getPhoneNumber());
                }
                if (cId != null) {
                    if (restaurantsConfig.get(cId) != null) {
                        List<String> list2 = restaurantsConfig.get(cId).stream().map(ConfigurationValue::getName).collect(Collectors.toList());
                        r.setRestaurantsConfig(String.join(",", list2));
                    }
                    if (guestroomsConfig.get(cId) != null) {
                        List<String> list2 = guestroomsConfig.get(cId).stream().map(ConfigurationValue::getName).collect(Collectors.toList());
                        r.setGuestroomsConfig(String.join(",", list2));
                    }
                    if (classroomsConfig.get(cId) != null) {
                        List<String> list2 = classroomsConfig.get(cId).stream().map(ClassroomConfiguration::getClassroom).collect(Collectors.toList());
                        r.setClassroomConfig(String.join(",", list2));
                    }
                }

                if (memberMap.get(mId) != null) {
                    r.setContactMemberName(memberMap.get(mId).getFullName());
                    r.setReservationMemberName(Objects.nonNull(r.getReservationMember()) ? memberMap.get(r.getReservationMember()).getFullName() : "");
                }
                if (orgMap.get(oId) != null) {
                    r.setOrganizationName(orgMap.get(oId).getName());
                }
                if (cvMap.get(pId) != null) {
                    r.setProjectType(cvMap.get(pId).getName());
                }
                if (cvMap2.get(pId) != null) {
                    r.setProjectLevel(cvMap2.get(pId).getName());
                }
		    /*if (clmMap.get(cmId) != null) {
		    	r.setClassRoomName(clmMap.get(cmId).getClassroom());
		    } else {
		    	r.setClassRoomName("");
		    }*/
                if(Objects.nonNull(approvalMap) && approvalMap.size() > 0 && Objects.nonNull(approvalMap.get(pId))){
                    r.setProjectApproval(approvalMap.get(pId));
                }
            });
            return project2;
        } else {
            return null;
        }
    }

    public static long dateToLong(java.util.Date first) {
        return first.getTime();
    }

    @Override
    public List<Project> findDownYM(Integer year, Integer month) {
        // TODO Auto-generated method stub
        com.zxy.product.train.jooq.tables.Member member2 = MEMBER.as("member2");
        List<Project> project2 = new ArrayList<>();
        Field<String> memberFullName = member2.FULL_NAME.as("memberFullName");
        List<Project> project = dao.execute(x -> x.selectDistinct(Fields.start()
                .add(PROJECT.ID)
                .add(PROJECT.CODE)
                .add(PROJECT.NAME)
                .add(PROJECT.CONTACT_PHONE)
                .add(PROJECT.OBJECT)
                .add(PROJECT.AMOUNT)
                .add(PROJECT.DAYS)
                .add(CLASS_INFO.ID)
                .add(CLASS_INFO.ARRIVE_DATE)
                .add(CLASS_INFO.RETURN_DATE)
                .add(CLASS_RESOURCE.ID)
                .add(CLASS_RESOURCE.CLASSROOM)
                .add(CLASS_RESOURCE.DINING_ROOM)
                .add(CLASS_RESOURCE.REST_ROOM)
                .add(CONFIGURATION_VALUE.NAME)
                .add(memberFullName)
                .add(MEMBER.FULL_NAME)
                .add(MEMBER.PHONE_NUMBER)
                .add(ORGANIZATION.NAME)
                .add(PROJECT_APPROVAL.SUGGESTION)
                .add(PROJECT_APPROVAL.CREATE_TIME)
                .add(PROJECT_APPROVAL.CREATE_TIME.max())
                .end()))
                .from(PROJECT)
                .leftJoin(CLASS_INFO)
                .on(PROJECT.ID.eq(CLASS_INFO.PROJECT_ID))
                .leftJoin(member2).on(PROJECT.CONTACT_MEMBER_ID.eq(member2.ID))
                .leftJoin(MEMBER)
                .on(CLASS_INFO.CLASS_TEACHER.eq(MEMBER.ID))
                .leftJoin(CLASS_RESOURCE)
                .on(CLASS_RESOURCE.CLASS_ID.eq(CLASS_INFO.ID))
                .leftJoin(ORGANIZATION)
                .on(PROJECT.ORGANIZATION_ID.eq(ORGANIZATION.ID))
                .leftJoin(PROJECT_APPROVAL)
                .on(PROJECT_APPROVAL.PROJECT_ID.eq(PROJECT.ID))
                .leftJoin(CONFIGURATION_VALUE)
                .on(CLASS_INFO.CLASS_INFO_TYPE.eq(CONFIGURATION_VALUE.ID).and(CONFIGURATION_VALUE.TYPE_ID.eq(5)))
                .where(PROJECT.YEAR.eq(year)
                        .and(PROJECT.MONTH.eq(month))
                        .and(PROJECT.DELETE_FLAG.eq(0)))
                .groupBy(PROJECT.ID, CLASS_INFO.ID, CLASS_RESOURCE.ID,
                        PROJECT.CODE, PROJECT.NAME, PROJECT.CONTACT_PHONE, PROJECT.OBJECT,
                        PROJECT.AMOUNT, PROJECT.DAYS, CLASS_INFO.ARRIVE_DATE, CLASS_INFO.RETURN_DATE
                        , CLASS_RESOURCE.CLASSROOM, CLASS_RESOURCE.DINING_ROOM, CLASS_RESOURCE.REST_ROOM
                        , memberFullName, MEMBER.FULL_NAME, PROJECT_APPROVAL.CREATE_TIME, MEMBER.PHONE_NUMBER, ORGANIZATION.NAME, PROJECT_APPROVAL.SUGGESTION, CONFIGURATION_VALUE.NAME)
                .orderBy(PROJECT_APPROVAL.CREATE_TIME.desc())
                .fetch(r -> {
                    boolean flag = true;
                    for (Project pro : project2) {
                        if (pro.getId().equals(r.getValue(PROJECT.ID))) {
                            flag = false;
                            break;
                        }
                    }
                    if (flag) {
                        Project p = r.into(Project.class);
                        p.setContactMemberName(r.getValue(memberFullName));
                        p.setOrganizationName(r.getValue(ORGANIZATION.NAME));
                        p.setSuggestion(r.getValue(PROJECT_APPROVAL.SUGGESTION));
                        p.setClassType(r.getValue(CONFIGURATION_VALUE.NAME));
                        ClassInfo c = r.into(ClassInfo.class);
                        c.setTeacher(r.getValue(MEMBER.FULL_NAME));
                        c.setTeacherPhone(SM4Utils.decryptDataCBC(r.getValue(MEMBER.PHONE_NUMBER)));
                        ClassResource classResource = r.into(ClassResource.class);
                        p.setClassInfo(c);
                        p.setClassResource(classResource);
                        project2.add(p);
                    }
                    return null;
                });
        return project2;
    }

    @Override
    public int checkCode(String code, Optional<String> id) {
        // TODO Auto-generated method stub
        return dao.execute(d -> {
            return d.selectCount().from(PROJECT)
                    .where(PROJECT.DELETE_FLAG.eq(Project.DELETE_FLASE))
                    .and(PROJECT.CODE.eq(code))
                    .and(id.map(PROJECT.ID::ne).orElse(DSL.trueCondition()))
                    .fetchOne(0, Integer.class);
        });
    }

    @Override
    public void save(List<Project> projects) {
        // TODO Auto-generated method stub
        dao.insert(projects);
    }

    @Override
    public List<Project> updateMember(String memberId) {
        // TODO Auto-generated method stub
        List<Project> projectList = dao.fetch(PROJECT.CONTACT_MEMBER_ID.eq(memberId));
        return projectList;
    }

    public Integer updateClassQuotaByClassIdAndAmount(String projectId, Integer beforeAmount, Integer nowAmount, Optional<String> createMember) {
        ClassQuota classQuota = classQuotaService.findByProjectId(projectId);
        if (beforeAmount > nowAmount) {
            if (classQuota != null && classQuota.getType() == 2) {
                List<Trainee> list = traineeService.findFormalTraineeAndCount(classQuota.getClassId());
                List<ClassQuotaDetail> newList = new ArrayList<>();
                if (list != null && !list.isEmpty() && list.size() > 0) {
                    List<ClassQuotaDetail> delList = classQuotaDetailService.findByClassId(classQuota.getClassId());
                    ClassQuotaDetail quota = null;
                    for (Trainee trainee : list) {
                        quota = new ClassQuotaDetail();
                        if (trainee.getNum() == 2) {
                            quota.forInsert();
                            quota.setClassId(classQuota.getClassId());
                            if (createMember.isPresent()) {
                                quota.setCreateMember(createMember.get());
                            }
                            quota.setOrganizationId(trainee.getOrganizationId());
                            quota.setQuantity(trainee.getMemberNum());
                            newList.add(quota);
                        }
                    }
                    return classQuotaDetailService.update(classQuota.getClassId(), newList, delList, Optional.empty());
                }
            }
        }
        return 0;
    }

    @Override
    public Project updateOne(String id, Optional<String> name, Optional<String> code, Optional<String> contactEmail,
                             Optional<String> contactMemberId, Optional<String> organizationId, Optional<String> contactPhone,
                             Optional<Integer> year, Optional<Integer> month, Optional<Integer> amount, Optional<String> object,
                             Optional<Integer> days, Optional<String> address, Optional<String> typeId, Optional<Integer> isOutSide,
                             Optional<String> surveyType, Optional<Long> arriveDate, Optional<Long> returnDate, Optional<String> target,
                             Optional<Integer> status, Optional<String> cost, Optional<Integer> beforeAmount,
                             Optional<String> createMember) {
        // TODO Auto-generated method stub
        Project project = get(id);
        String oldOrganizationId = project.getOrganizationId();
        Integer originalAmount = project.getAmount();
        name.ifPresent(project::setName);
        organizationId.ifPresent(project::setOrganizationId);
        contactMemberId.ifPresent(project::setContactMemberId);
        address.ifPresent(project::setAddress);
        typeId.ifPresent(project::setTypeId);
        year.ifPresent(project::setYear);
        cost.ifPresent(project::setCost);
        month.ifPresent(project::setMonth);
        amount.ifPresent(project::setAmount);
        object.ifPresent(project::setObject);
        days.ifPresent(project::setDays);
        code.ifPresent(project::setCode);
        contactEmail.ifPresent(project::setContactEmail);
        status.ifPresent(project::setStatus);
        contactPhone.ifPresent(project::setContactPhone);
        dao.update(project);
        if (beforeAmount.isPresent()) {
            if (beforeAmount.get() > amount.get()) {
                updateClassQuotaByClassIdAndAmount(id, beforeAmount.get(), amount.get(), createMember);
            }
        }
        if (name.isPresent()) {
            Optional<ClassInfo> classInfo = classInfoDao.fetchOne(CLASS_INFO.PROJECT_ID.eq(project.getId())
                    .and(CLASS_INFO.DELETE_FLAG.eq(ClassInfo.DELETE_FLASE))
                    .and(CLASS_INFO.NOTICE.eq(ClassInfo.CLASS_IS_NOTICE)));
            sender.send(MessageTypeContent.TRAIN_UPDATE_PROJECT_NAME,
                    MessageHeaderContent.ID, project.getId(),
                    MessageHeaderContent.NAME, name.get());
            if (classInfo.isPresent()) {
                sender.send(MessageTypeContent.TRAIN_CLASS_UPDATE_FOR_ACTIVITY, MessageHeaderContent.ID, classInfo.get().getId()
                        , MessageHeaderContent.NAME, name.get());
            }
        }
        //如果计划审批已经通过并且计划人数做了修改，需要修改相应的可用资源额度
        if ((project.getStatus() == Project.STATUS_AGREE || project.getStatus() == Project.STATUS_APPROVAL) && amount.isPresent() && amount.get() != originalAmount) {
            occupyService.correctOccupyByProjectUpdate(id, originalAmount, amount.get());
        }
        if (organizationId.isPresent()) {
            if (!organizationId.get().equals(oldOrganizationId)) {
                Integer findSource = organizationService.findSource(project.getOrganizationId());
                classInfoService.updateOrganizationIdByProjectId(id, organizationId.get(), findSource);
            }
        }
        return project;
    }

    @Override
    public Project updateNameAndCode(String id, String name, Optional<String> code) {
        Project project = get(id);
        project.setName(name);
        code.ifPresent(project::setCode);
        dao.update(project);
        Optional<ClassInfo> classInfo = classInfoDao.fetchOne(CLASS_INFO.PROJECT_ID.eq(project.getId())
                .and(CLASS_INFO.DELETE_FLAG.eq(ClassInfo.DELETE_FLASE))
                .and(CLASS_INFO.NOTICE.eq(ClassInfo.CLASS_IS_NOTICE)));
        sender.send(MessageTypeContent.TRAIN_UPDATE_PROJECT_NAME,
                MessageHeaderContent.ID, project.getId(),
                MessageHeaderContent.NAME, name);
        if (classInfo.isPresent()) {
            sender.send(MessageTypeContent.TRAIN_CLASS_UPDATE_FOR_ACTIVITY, MessageHeaderContent.ID, classInfo.get().getId()
                    , MessageHeaderContent.NAME, name);
        }
        return project;
    }

    /**
     * add by acong 2018/01/19
     */
    @Override
    public String[] findMemberIdsByProjectIds(String[] projectIds) {
        return dao.execute(x -> x.select().from(PROJECT).where(PROJECT.ID.in(projectIds)).fetchArray(PROJECT.CONTACT_MEMBER_ID));
    }

    /**
     * add by lulu 2018/01/25
     */
    @Override
    public String[] findTepIdsByProjectIds(String[] projectIds) {
        return dao.execute(x -> x.select().from(PROJECT).where(PROJECT.ID.in(projectIds)).fetchArray(PROJECT.CONTACT_PHONE));
    }

    @Override
    public Project getProjectNameAndOrgNameAndImplementationMonth(String classId) {
        return dao.execute(r->
                r.select(
                        CLASS_INFO.IMPLEMENTATION_MONTH,
                        ORGANIZATION.NAME,
                        PROJECT.NAME)
                        .from(PROJECT)
                        .leftJoin(CLASS_INFO).on(PROJECT.ID.eq(CLASS_INFO.PROJECT_ID))
                        .leftJoin(ORGANIZATION).on(PROJECT.ORGANIZATION_ID.eq(ORGANIZATION.ID))
                        .where(CLASS_INFO.ID.eq(classId))
                        .fetchOne(o ->{
                            Project project=new Project();
                            project.setName(o.getValue(PROJECT.NAME));
                            project.setImplementationMonth(o.getValue(CLASS_INFO.IMPLEMENTATION_MONTH));
                            project.setOrgName(o.getValue(ORGANIZATION.NAME));
                            return  project;
                        }));
    }
}
