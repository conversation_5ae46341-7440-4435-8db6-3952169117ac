package com.zxy.product.train.service.support;

import com.zxy.common.dao.Fields;
import com.zxy.common.dao.support.CommonDao;
import com.zxy.product.train.api.ClassOfflineCourseService;
import com.zxy.product.train.api.SettlementService;
import com.zxy.product.train.api.TraineeService;
import com.zxy.product.train.entity.Settlement;
import com.zxy.product.train.entity.SettlementMemberQuantity;
import org.jooq.Record;
import org.jooq.SelectConditionStep;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.zxy.product.train.jooq.tables.ClassInfo.CLASS_INFO;
import static com.zxy.product.train.jooq.tables.Project.PROJECT;
import static com.zxy.product.train.jooq.tables.Settlement.SETTLEMENT;
import static com.zxy.product.train.jooq.tables.SettlementMemberQuantity.SETTLEMENT_MEMBER_QUANTITY;

/**
 * Created by chun on 2017/3/7.
 */
@Service
public class SettlementServiceSupport implements SettlementService {
    private CommonDao<Settlement> settlementCommonDao;
    private CommonDao<SettlementMemberQuantity> settlementMemberQuantityCommonDao;
    private TraineeService traineeService;
    private ClassOfflineCourseService classOfflineCourseService;
    @Autowired
    public void setClassOfflineCourseService(ClassOfflineCourseService classOfflineCourseService) {
        this.classOfflineCourseService = classOfflineCourseService;
    }

    @Autowired
    public void setTraineeService(TraineeService traineeService) {
        this.traineeService = traineeService;
    }

    @Autowired
    public void setSettlementCommonDao(CommonDao<Settlement> settlementCommonDao) {
        this.settlementCommonDao = settlementCommonDao;
    }

    @Autowired
    public void setSettlementMemberQuantityCommonDao(CommonDao<SettlementMemberQuantity> settlementMemberQuantityCommonDao) {
        this.settlementMemberQuantityCommonDao = settlementMemberQuantityCommonDao;
    }
    @Override
    public Settlement get(String id) {
        int courseNumber = classOfflineCourseService.findByCourseNumber(id);
        Integer count = traineeService.countFormalTraineeByClassId(id);
        SelectConditionStep<Record> step = settlementCommonDao.execute(x -> x.selectDistinct(Fields.start().
                add(PROJECT.AMOUNT, PROJECT.DAYS, CLASS_INFO.ID,
                        PROJECT.MONTH, SETTLEMENT.ID, SETTLEMENT.ATTEND_DAYS,
                        CLASS_INFO.RETURN_DATE, CLASS_INFO.ARRIVE_DATE ,SETTLEMENT.DAY_NUMBER, SETTLEMENT.TRAIN_DAY_NUM,
                        SETTLEMENT.EXPLAIN, SETTLEMENT.PEOPLE_DAY, SETTLEMENT.CREATE_MOUTH,SETTLEMENT.SETTLEMENT_QUANTITY,
                        SETTLEMENT.PEOPLE_NUMBER).end()).from(PROJECT)
                .innerJoin(CLASS_INFO).on(CLASS_INFO.PROJECT_ID.eq(PROJECT.ID))
                .leftJoin(SETTLEMENT).on(SETTLEMENT.CLASS_ID.eq(CLASS_INFO.ID))).where(CLASS_INFO.ID.eq(id));
        Settlement list = step.fetchOne(r -> {
            Double trainPeople = 0.0;
            Settlement settlement = r.into(Settlement.class);
            Integer settDay  = r.getValue(PROJECT.DAYS);
            settlement.setPeopleDayEntity(count);
            settlement.setClassId(r.getValue(CLASS_INFO.ID));
            Double number = r.getValue(SETTLEMENT.DAY_NUMBER);
            settlement.setPeopleNumber(r.getValue(SETTLEMENT.PEOPLE_NUMBER));
            Integer people = r.getValue(PROJECT.AMOUNT);
            String settlementId = r.getValue(SETTLEMENT.ID);
            int flag = settlementId!=null ? 1:0;
            settlement.setFlag(flag);
            long arriveDate =   r.getValue(CLASS_INFO.RETURN_DATE);
            SimpleDateFormat sdf=new SimpleDateFormat("yyyy-MM");
            String createMonth = sdf.format(arriveDate);
            if (number == null) {
                double douDay = courseNumber;
                settlement.setDayNumber(douDay+2);
                settlement.setPeopleNumber(count);
                double peopleDa = (douDay+2) * count;
                settlement.setAttendDays(douDay);
                settlement.setPeopleDay(peopleDa);
                settlement.setCreateMouth(createMonth);
                settlement.setDayNumberEntity(douDay+1);
                trainPeople = Double.valueOf((douDay+1) * count);

            }else{
                Double num = r.getValue(SETTLEMENT.TRAIN_DAY_NUM)==null?0.0:r.getValue(SETTLEMENT.TRAIN_DAY_NUM);
                settlement.setDayNumberEntity(num);
                trainPeople = Double.valueOf(num * count);
                settlement.setCreateMouth(r.getValue(SETTLEMENT.CREATE_MOUTH));
            }
            settlement.setPeopleNumberEntity(trainPeople);
            settlement.setArriveDate(r.getValue(CLASS_INFO.ARRIVE_DATE));
            settlement.setReturnDate(r.getValue(CLASS_INFO.RETURN_DATE));
            settlement.setSettlementQuantity(r.getValue(SETTLEMENT.SETTLEMENT_QUANTITY));
            return settlement;
        });
        return list;
    }

    @Override
    public Settlement insert(Optional<String> explain, Optional<Integer> peopleNumber, Optional<Double> dayNumber,
                             Optional<Double> peopleDay,Optional<String> createMouth,Optional<String> classId,
                             Optional<Double> trainDayNum, Optional<Double> attendDays,Optional<Integer> settlementQuantity) {
        Settlement settlement = new Settlement();
        settlement.forInsert();
        explain.ifPresent(settlement::setExplain);
        peopleNumber.ifPresent(settlement::setPeopleNumber);
        dayNumber.ifPresent(settlement::setDayNumber);
        peopleDay.ifPresent(settlement::setPeopleDay);
        trainDayNum.ifPresent(settlement::setTrainDayNum);
        classId.ifPresent(settlement::setClassId);
        attendDays.ifPresent(settlement::setAttendDays);
        settlementQuantity.ifPresent(settlement::setSettlementQuantity);
        settlement.setIsReceipt(Settlement.NO_RECEIPT);
        String createMonth = this.Transformation();
        settlement.setCreateMouth(createMonth);
        if(createMouth.isPresent()){
            createMouth.ifPresent(settlement::setCreateMouth);
        }
        return settlementCommonDao.insert(settlement);
    }

    @Override
    public Settlement update(String id, Optional<String> explain, Optional<Integer> peopleNumber,
                             Optional<Double> dayNumber, Optional<Double> peopleDay,
                             Optional<String> createMouth, String classId,
                             Optional<Double> trainDayNum, Optional<Double> attendDays,
                             Optional<Integer> settlementQuantity) {
        Settlement settlement = settlementCommonDao.get(id);
        String flag = "";
        if(explain.isPresent()){
            explain.ifPresent(settlement::setExplain);
        }else{
            settlement.setExplain(flag);
        }
        trainDayNum.ifPresent(settlement::setTrainDayNum);
        peopleNumber.ifPresent(settlement::setPeopleNumber);
        dayNumber.ifPresent(settlement::setDayNumber);
        peopleDay.ifPresent(settlement::setPeopleDay);
        attendDays.ifPresent(settlement::setAttendDays);
        settlementQuantity.ifPresent(settlement::setSettlementQuantity);
        settlement.setClassId(classId);

        if(createMouth.isPresent()){
            createMouth.ifPresent(settlement::setCreateMouth);
        }
        return settlementCommonDao.update(settlement);
    }
    public String Transformation(){
        Date d = new Date();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String dateNowStr = sdf.format(d);
        String[] dates = dateNowStr.split("-");
        String createMonth = dates[0]+"-"+dates[1];
        return createMonth;
    }
    @Override
    public Integer findSettlementCount(String classId) {
        SelectConditionStep<Record> step = settlementCommonDao.execute(x -> x.select(Fields.start().add(SETTLEMENT).end())
                .from(SETTLEMENT)
                .where(SETTLEMENT.CLASS_ID.eq(classId)));
        Integer count = settlementCommonDao.execute(x->x.fetchCount(step));
        return count;
    }

    @Override
    public Settlement update(String id, String createMouth) {
        Settlement settlement = settlementCommonDao.get(id);
        settlement.setCreateMouth(createMouth);
        return settlementCommonDao.update(settlement);
    }

    @Override
    public void insertMemberQuantity(String classId, List<SettlementMemberQuantity> settlementMemberQuantities) {
        //若存在，则先删除
        List<SettlementMemberQuantity> exitSettlementMemberQuantity =
                settlementMemberQuantityCommonDao.fetch(SETTLEMENT_MEMBER_QUANTITY.CLASS_ID.eq(classId));
        if(!ObjectUtils.isEmpty(settlementMemberQuantities)){
            settlementMemberQuantityCommonDao.delete(exitSettlementMemberQuantity.stream().map(r -> r.getId()).collect(Collectors.toList()));
        }
        settlementMemberQuantities.forEach(settlementMemberQuantity -> {
            settlementMemberQuantity.setClassId(classId);
            settlementMemberQuantity.forInsert();
        });
       settlementMemberQuantityCommonDao.insert(settlementMemberQuantities);
    }

    @Override
    public List<SettlementMemberQuantity> findMemberQuantity(String classId) {
        return settlementMemberQuantityCommonDao.execute(e ->
                e.selectDistinct(Fields.start().add(SETTLEMENT_MEMBER_QUANTITY).end())
                        .from(SETTLEMENT_MEMBER_QUANTITY)
                        .where(SETTLEMENT_MEMBER_QUANTITY.CLASS_ID.eq(classId))
                        .fetchInto(SettlementMemberQuantity.class)
        );
    }


    @Override
    public List<SettlementMemberQuantity> findMemberByClassIds(List<String> classIds) {
        return settlementMemberQuantityCommonDao.execute(e ->
                e.selectDistinct(Fields.start().add(SETTLEMENT_MEMBER_QUANTITY).end())
                        .from(SETTLEMENT_MEMBER_QUANTITY)
                        .where(SETTLEMENT_MEMBER_QUANTITY.CLASS_ID.in(classIds))
                        .orderBy(SETTLEMENT_MEMBER_QUANTITY.DATE.asc())
                        .fetchInto(SettlementMemberQuantity.class)
        );
    }
}
