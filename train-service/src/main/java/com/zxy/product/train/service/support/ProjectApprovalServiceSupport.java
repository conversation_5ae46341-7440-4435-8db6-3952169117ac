package com.zxy.product.train.service.support;

import com.zxy.common.dao.support.CommonDao;
import com.zxy.product.train.api.ProjectApprovalService;
import com.zxy.product.train.entity.Project;
import com.zxy.product.train.entity.ProjectApproval;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

import static com.zxy.product.train.jooq.Tables.MEMBER;
import static com.zxy.product.train.jooq.Tables.PROJECT_APPROVAL;

@Service
public class ProjectApprovalServiceSupport implements ProjectApprovalService{

	private CommonDao<ProjectApproval> projectApprovalDao;

	@Autowired
	public void setProjectApprovalDao(CommonDao<ProjectApproval> projectApprovalDao) {
		this.projectApprovalDao = projectApprovalDao;
	}

	@Override
	public ProjectApproval insert(String projectId, Integer status, Optional<String> suggestion,
			Optional<String> approvalMemberId, Optional<String> createMemberId) {
		// TODO Auto-generated method stub
				ProjectApproval projectApproval = new ProjectApproval();
				projectApproval.forInsert();
				projectApproval.setProjectId(projectId);
				projectApproval.setStatus(status);
				suggestion.ifPresent(projectApproval::setSuggestion);
				approvalMemberId.ifPresent(projectApproval::setApprovalMember);
				createMemberId.ifPresent(projectApproval::setCreateMember);
				projectApproval.setCreateTime(System.currentTimeMillis());
				projectApproval.setDeleteFlag(Project.DELETE_FLASE);
				//设置审批时间
				if(Objects.equals(status, ProjectApproval.STATUS_AGREE) || Objects.equals(status, ProjectApproval.STATUS_REFUSE)){
					projectApproval.setApprovalTime(System.currentTimeMillis());
				}
				projectApprovalDao.insert(projectApproval);
				return projectApproval;
	}
	@Override
	public void update(String projectId, Integer status, Optional<String> approvalMemberId) {
		List<ProjectApproval> approvals = find(projectId);
		if(CollectionUtils.isEmpty(approvals)){
			ProjectApproval projectApproval = approvals.get(0);
			projectApproval.setStatus(status);
			projectApproval.setApprovalMember(approvalMemberId.orElse(null));
			//设置预定时间
			if(Objects.equals(status, ProjectApproval.STATUS_AGREE) || Objects.equals(status, ProjectApproval.STATUS_REFUSE)){
				projectApproval.setApprovalTime(System.currentTimeMillis());
			}
			projectApprovalDao.update(projectApproval);
		}
	}


	@Override
	public List<ProjectApproval> find(String id) {
		return projectApprovalDao.execute(x -> {
			return x.selectFrom(PROJECT_APPROVAL).where(PROJECT_APPROVAL.PROJECT_ID.eq(id)
					.and(PROJECT_APPROVAL.DELETE_FLAG.eq(Project.DELETE_FLASE)))
					.orderBy(PROJECT_APPROVAL.CREATE_TIME.desc())
					.fetchInto(ProjectApproval.class);
			});
		}


	@Override
	public List<ProjectApproval> findByProjectIds(List<String> projectIds) {
		com.zxy.product.train.jooq.tables.Member member = MEMBER.as("createMember");
		return projectApprovalDao.execute(x -> {
			return x.select(PROJECT_APPROVAL.PROJECT_ID,PROJECT_APPROVAL.CREATE_TIME,PROJECT_APPROVAL.CREATE_MEMBER,PROJECT_APPROVAL.APPROVAL_MEMBER,PROJECT_APPROVAL.APPROVAL_TIME,MEMBER.FULL_NAME,member.FULL_NAME.as("createFullName"))
					.from(PROJECT_APPROVAL)
					.leftJoin(MEMBER).on(PROJECT_APPROVAL.APPROVAL_MEMBER.eq(MEMBER.ID))
					.leftJoin(member).on(PROJECT_APPROVAL.CREATE_MEMBER.eq(member.ID))
					.where(PROJECT_APPROVAL.PROJECT_ID.in(projectIds)
							.and(PROJECT_APPROVAL.DELETE_FLAG.eq(Project.DELETE_FLASE)))
					.fetch(r->{
						ProjectApproval into = r.into(ProjectApproval.class);
						//这块是先审核，再预定，所以创建人就是审核人；审核通过人是预定人
						into.setApprovalFullName(r.getValue(member.FULL_NAME.as("createFullName")));
						into.setCreataFullName(r.getValue(MEMBER.FULL_NAME));
						return into;
					});
		});
	}



}
