package com.zxy.product.train.service.support;

import com.zxy.common.dao.Fields;
import com.zxy.common.dao.support.CommonDao;
import com.zxy.product.train.api.TraineeGroupService;
import com.zxy.product.train.entity.Member;
import com.zxy.product.train.entity.Trainee;
import com.zxy.product.train.entity.TraineeGroup;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import static com.zxy.product.train.entity.Trainee.AUDIT_AGREE;
import static com.zxy.product.train.entity.Trainee.TRAINEE_TYPE_FORMAL;
import static com.zxy.product.train.entity.Trainee.TRAINEE_TYPE_INFORMAL;
import static com.zxy.product.train.entity.TraineeGroup.DELETE_FLASE;
import static com.zxy.product.train.jooq.Tables.MEMBER;
import static com.zxy.product.train.jooq.Tables.TRAINEE;
import static com.zxy.product.train.jooq.Tables.TRAINEE_GROUP;

/**
 * Created by 田聪 on 2017/3/6.
 */
@Service
public class TraineeGroupServiceSupport implements TraineeGroupService {
	private CommonDao<TraineeGroup> dao;
	private CommonDao<Trainee> traineeDao;

	@Autowired
	public void setDao(CommonDao<TraineeGroup> dao) {
		this.dao = dao;
	}

	@Autowired
	public void setTraineeDao(CommonDao<Trainee> traineeDao) {
		this.traineeDao = traineeDao;
	}

	@Override
	public List<TraineeGroup> find(String classId) {
		List<TraineeGroup> list = dao.execute(x -> x
				.selectDistinct(Fields.start()
						.add(TRAINEE_GROUP.ID, TRAINEE_GROUP.NAME, TRAINEE_GROUP.SORT, TRAINEE_GROUP.CLASS_ID,
								TRAINEE_GROUP.DELETE_FLAG, TRAINEE_GROUP.CREATE_MEMBER_ID, TRAINEE_GROUP.CREATE_TIME,
								TRAINEE.ID.count())
						.end())
				.from(TRAINEE_GROUP).leftJoin(TRAINEE)
				.on(TRAINEE_GROUP.ID.eq(TRAINEE.TRAINEE_GROUP_ID).and(TRAINEE.AUDIT_STATUS.eq(Trainee.AUDIT_AGREE))
						.and(TRAINEE.TYPE.eq(Trainee.TRAINEE_TYPE_FORMAL))
						.and(TRAINEE.DELETE_FLAG.eq(Trainee.DELETE_FLASE)))
				.where(TRAINEE_GROUP.DELETE_FLAG.eq(DELETE_FLASE)).and(TRAINEE_GROUP.CLASS_ID.eq(classId))
				.groupBy(TRAINEE_GROUP.ID, TRAINEE_GROUP.NAME, TRAINEE_GROUP.SORT, TRAINEE_GROUP.CLASS_ID)
				.orderBy(TRAINEE_GROUP.SORT)).fetch(r -> {
					TraineeGroup tg = new TraineeGroup();
					tg.setId(r.getValue(TRAINEE_GROUP.ID));
					tg.setName(r.getValue(TRAINEE_GROUP.NAME));
					tg.setSort(r.getValue(TRAINEE_GROUP.SORT));
					tg.setClassId(r.getValue(TRAINEE_GROUP.CLASS_ID));
					tg.setCreateMemberId(r.getValue(TRAINEE_GROUP.CREATE_MEMBER_ID));
					tg.setDeleteFlag(r.getValue(TRAINEE_GROUP.DELETE_FLAG));
					tg.setCreateTime(r.getValue(TRAINEE_GROUP.CREATE_TIME));
					tg.setTraineeNumber(r.getValue(TRAINEE.ID.count()));
					return tg;
				});
		return list;
	}


	@Override
	public List<TraineeGroup> findByIds(String classId, List<String> ids) {
		return dao.execute(x -> x
				.selectDistinct(Fields.start()
						.add(TRAINEE_GROUP.ID, TRAINEE_GROUP.NAME)
						.end())
				.from(TRAINEE_GROUP)
				.where(TRAINEE_GROUP.DELETE_FLAG.eq(DELETE_FLASE)).and(TRAINEE_GROUP.CLASS_ID.eq(classId).and(TRAINEE_GROUP.ID.in(ids)))
		).fetchInto(TraineeGroup.class);
	}

	@Override
	public int save(String classId, List<TraineeGroup> newGroups, List<TraineeGroup> delGroups, String userId) {
		// 查询已存在分组
		List<TraineeGroup> tgs = find(classId);
		StringBuffer sb = new StringBuffer();
		tgs.stream().forEach(x -> {
			sb.append(x.getId());
		});
		// 新增集合
		List<TraineeGroup> addList = newGroups.stream().filter(x -> {
			return !sb.toString().contains(x.getId());
		}).map(y -> {
			y.forInsert();
			y.setCreateMemberId(userId);
			return y;
		}).collect(Collectors.toList());
		dao.insert(addList);
		// 更新集合
		List<TraineeGroup> updateList = newGroups.stream().filter(x -> {
			return sb.toString().contains(x.getId());
		}).collect(Collectors.toList());
		dao.update(updateList);
		// 删除集合
		List<String> ids = delGroups.stream().map(x -> {
			return x.getId();
		}).collect(Collectors.toList());
		dao.delete(ids);
		// 把删除的分组内的学员分组字段置空
		traineeDao.execute(
				x -> x.update(TRAINEE).set(TRAINEE.TRAINEE_GROUP_ID, "0").where(TRAINEE.TRAINEE_GROUP_ID.in(ids)))
				.execute();
		return 1;
	}

	@Override
	public List<TraineeGroup> traineesForMessage(String classId) {
		// 创建分组的集合
		List<TraineeGroup> groupList = new ArrayList<TraineeGroup>();
		TraineeGroup tg = new TraineeGroup();
		tg.setClassId(classId);
		List<Trainee> trainees = new ArrayList<Trainee>();
		tg.setTraineeList(trainees);
		tg.setName("待分组");
		tg.setId("0");
		groupList.add(tg);
		// 获取所有分组
		dao.execute(x -> x
				.selectDistinct(Fields.start().add(TRAINEE_GROUP.ID, TRAINEE_GROUP.NAME, TRAINEE_GROUP.SORT).end())
				.from(TRAINEE_GROUP).where(TRAINEE_GROUP.CLASS_ID.eq(classId))
				.and(TRAINEE_GROUP.DELETE_FLAG.eq(DELETE_FLASE))).orderBy(TRAINEE_GROUP.SORT).fetch(r -> {
					TraineeGroup traineeGroup = new TraineeGroup();
					traineeGroup.setId(r.getValue(TRAINEE_GROUP.ID));
					traineeGroup.setName(r.getValue(TRAINEE_GROUP.NAME));
					List<Trainee> traineeList = new ArrayList<Trainee>();
					traineeGroup.setTraineeList(traineeList);
					groupList.add(traineeGroup);
					return null;
				});
		// 给每个分组添加学员
		traineeDao
				.execute(x -> x
						.selectDistinct(Fields.start()
								.add(TRAINEE.ID, MEMBER.FULL_NAME, MEMBER.ID, TRAINEE.PHONE_NUMBER, TRAINEE.TRAINEE_GROUP_ID,
										TRAINEE.SORT)
								.end())
						.from(TRAINEE).leftJoin(MEMBER).on(TRAINEE.MEMBER_ID.eq(MEMBER.ID))
						.where(TRAINEE.DELETE_FLAG.eq(DELETE_FLASE)).and(TRAINEE.AUDIT_STATUS.eq(AUDIT_AGREE))
						.and(TRAINEE.TYPE.eq(TRAINEE_TYPE_FORMAL)).and(TRAINEE.CLASS_ID.eq(classId)))
				.orderBy(TRAINEE.SORT).fetch(r -> {
					String groupId = r.getValue(TRAINEE.TRAINEE_GROUP_ID);
					for (TraineeGroup traineeGroup : groupList) {
						if (traineeGroup.getId().equals(groupId)) {
							Trainee t = new Trainee();
							t.setId(r.getValue(TRAINEE.ID));
							Member m = new Member();
							m.setFullName(r.getValue(MEMBER.FULL_NAME));
							m.setId(r.getValue(MEMBER.ID));
							t.setMember(m);
							t.setPhoneNumber(r.getValue(TRAINEE.PHONE_NUMBER));
							traineeGroup.getTraineeList().add(t);
						}
					}
					return null;
				});
		return groupList;
	}

	@Override
	public List<Trainee> getInformalTraineesForMessage(String classId) {
		return traineeDao.execute(x -> x
								 .selectDistinct(Fields.start()
													   .add(TRAINEE.ID, MEMBER.FULL_NAME, MEMBER.ID, TRAINEE.PHONE_NUMBER, TRAINEE.TRAINEE_GROUP_ID,
															TRAINEE.SORT)
													   .end())
								 .from(TRAINEE)
								 .leftJoin(MEMBER).on(TRAINEE.MEMBER_ID.eq(MEMBER.ID))
								 .where(TRAINEE.DELETE_FLAG.eq(DELETE_FLASE))
								 .and(TRAINEE.TYPE.eq(TRAINEE_TYPE_INFORMAL))
								 .and(TRAINEE.CLASS_ID.eq(classId)))
						 .orderBy(TRAINEE.SORT_NEW).fetch(r -> {
					Trainee t = new Trainee();
					t.setId(r.getValue(TRAINEE.ID));
					Member m = new Member();
					m.setFullName(r.getValue(MEMBER.FULL_NAME));
					m.setId(r.getValue(MEMBER.ID));
					t.setMember(m);
					t.setPhoneNumber(r.getValue(TRAINEE.PHONE_NUMBER));
					return t;
				}).stream().collect(Collectors.toList());
	}

}
