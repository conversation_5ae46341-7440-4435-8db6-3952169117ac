package com.zxy.product.train.async.listener;

import static com.zxy.product.train.jooq.Tables.PROJECT;
import static com.zxy.product.train.jooq.Tables.PROJECT_OCCUPY;
import static com.zxy.product.train.jooq.Tables.CLASS_INFO;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.zxy.common.base.message.Message;
import com.zxy.common.dao.support.CommonDao;
import com.zxy.common.message.consumer.AbstractMessageListener;
import com.zxy.product.train.api.LimitConfigurationService;
import com.zxy.product.train.content.MessageHeaderContent;
import com.zxy.product.train.content.MessageTypeContent;
import com.zxy.product.train.entity.LimitConfiguration;
import com.zxy.product.train.entity.Project;
import com.zxy.product.train.entity.ProjectOccupy;
import com.zxy.product.train.util.StringUtils;

/**
 * 释放或占用资源
 * <AUTHOR>
 * @date
 */
@Service
public class OccupySourceListener  extends AbstractMessageListener{

    private static Logger logger = LoggerFactory.getLogger(ChangeTraineeAuditStatusListener.class);
    private CommonDao<ProjectOccupy> occupyDao;
    private LimitConfigurationService limitService;
    private CommonDao<Project> projectDao;

    // 消息去重缓存，存储 projectId_messageType -> 最后处理时间
    private static final ConcurrentHashMap<String, Long> messageProcessCache = new ConcurrentHashMap<>();

    // 缓存清理时间间隔（毫秒）
    private static final long CACHE_CLEANUP_INTERVAL = 300000; // 5分钟

    // 重复消息检测时间窗口（毫秒）
    private static final long DUPLICATE_MESSAGE_WINDOW = 30000; // 30秒

    @Autowired
    public void setProjectDao(CommonDao<Project> projectDao) {
        this.projectDao = projectDao;
    }

    @Autowired
    public void setOccupyDao(CommonDao<ProjectOccupy> occupyDao) {
        this.occupyDao = occupyDao;
    }

    @Autowired
    public void setLimitService(LimitConfigurationService limitService) {
        this.limitService = limitService;
    }


    @Override
    public int[] getTypes() {
        return new int[] {
                MessageTypeContent.TRAIN_PROJECT_APPROVAL_UPDATE,
                MessageTypeContent.TRAIN_PROJECT_APPROVAL_DELETE
        };
    }

    @Override
    protected void onMessage(Message message) {
        logger.info("train....修改计划占用资源train/OccupySourceListener:" + message.getType());
        String startTime = message.getHeader(MessageHeaderContent.START_TIME);
        String endTime = message.getHeader(MessageHeaderContent.END_TIME);
        int type = message.getType();
        Long start = Long.valueOf(startTime);
        Long end = Long.valueOf(endTime);
        String projectId = message.getHeader(MessageHeaderContent.ID);

        // 消息去重检查
        if (isDuplicateMessage(projectId, type)) {
            logger.info("跳过重复消息处理: projectId={}, type={}", projectId, type);
            return;
        }

        Optional<Project> project = projectDao.fetchOne(PROJECT.ID.eq(projectId));
        if (!project.isPresent()) {
            logger.warn("项目不存在: projectId={}", projectId);
            return;
        }

        boolean flag = false;
        if(end - start > 86400000l){
            flag = true;
        }

        int num = project.get().getDays();
        if(flag){
            num = num - 1;
        }

        switch (type) {
            case MessageTypeContent.TRAIN_PROJECT_APPROVAL_UPDATE:
                handleProjectUpdate(projectId, start, end, project.get().getAmount(), num);
                break;
            case MessageTypeContent.TRAIN_PROJECT_APPROVAL_DELETE:
                handleProjectDelete(projectId, start, end, project.get().getAmount(), num);
                break;
            default:
                logger.warn("未知的消息类型: type={}", type);
                break;
        }
    }

    /**
     * 处理项目更新的差量逻辑
     */
    private void handleProjectUpdate(String projectId, Long start, Long end, Integer amount, Integer days) {
        logger.info("处理项目更新: projectId={}, amount={}, days={}", projectId, amount, days);

        // 重新计算指定日期范围内的资源占用
        recalculateOccupyForDateRange(start, days);

        logger.info("项目更新处理完成: projectId={}", projectId);
    }

    /**
     * 处理项目删除的差量逻辑
     */
    private void handleProjectDelete(String projectId, Long start, Long end, Integer amount, Integer days) {
        logger.info("处理项目删除: projectId={}, amount={}, days={}", projectId, amount, days);

        // 重新计算指定日期范围内的资源占用
        recalculateOccupyForDateRange(start, days);

        logger.info("项目删除处理完成: projectId={}", projectId);
    }

    /**
     * 重新计算指定日期范围内的资源占用情况
     */
    private void recalculateOccupyForDateRange(Long startDate, Integer days) {
        BigDecimal start = new BigDecimal(startDate);
        for (int i = 0; i < days; i++) {
            Long currentDate = start.add(new BigDecimal(86400000).multiply(new BigDecimal(i))).longValue();
            recalculateOccupyForDate(currentDate);
        }
    }

    /**
     * 重新计算指定日期的资源占用情况
     */
    private void recalculateOccupyForDate(Long targetDate) {
        String dateStr = StringUtils.long2Date(targetDate, "yyyy-MM-dd");
        String[] dateArray = dateStr.split("-");
        int year = Integer.parseInt(dateArray[0]);
        int month = Integer.parseInt(dateArray[1]);
        int day = Integer.parseInt(dateArray[2]);

        try {
            // 查询所有在该日期有效的项目
            List<Project> activeProjects = findActiveProjectsForDate(targetDate);

            // 计算总占用量
            int totalOccupied = activeProjects.stream()
                .mapToInt(Project::getAmount)
                .sum();

            // 获取默认额度
            Optional<LimitConfiguration> limit = limitService.findByMonthWithTran(year, month);
            if (!limit.isPresent()) {
                logger.error("未找到月份额度配置: year={}, month={}", year, month);
                return;
            }

            int defaultLimit = limit.get().getLimit();
            int availableAmount = defaultLimit - totalOccupied;

            // 更新或创建资源记录
            Optional<ProjectOccupy> occupy = findOccupyByDate(year, month, day);
            if (occupy.isPresent()) {
                ProjectOccupy po = occupy.get();
                po.setDefault(defaultLimit);
                po.setAvailable(availableAmount);
                occupyDao.update(po);
                logger.debug("更新资源记录: date={}, 默认额度={}, 可用额度={}, 占用项目数={}",
                           dateStr, defaultLimit, availableAmount, activeProjects.size());
            } else {
                // 创建新记录
                ProjectOccupy po = new ProjectOccupy();
                po.forInsert();
                po.setYear(year);
                po.setMonth(month);
                po.setDay(day);
                po.setDate(targetDate);
                po.setDefault(defaultLimit);
                po.setAvailable(availableAmount);
                occupyDao.insert(po);
                logger.debug("创建资源记录: date={}, 默认额度={}, 可用额度={}, 占用项目数={}",
                           dateStr, defaultLimit, availableAmount, activeProjects.size());
            }
        } catch (Exception e) {
            logger.error("重新计算日期资源占用失败: date={}, error={}", dateStr, e.getMessage(), e);
        }
    }

    /**
     * 查询在指定日期有效的所有项目
     */
    private List<Project> findActiveProjectsForDate(Long targetDate) {
        try {
            // 查询所有状态为已通过(status=3)且时间范围包含目标日期的项目
            return projectDao.execute(dsl -> {
                return dsl.select(PROJECT.fields())
                    .from(PROJECT)
                    .join(CLASS_INFO).on(PROJECT.ID.eq(CLASS_INFO.PROJECT_ID))
                    .where(PROJECT.STATUS.eq(3)  // 已通过状态
                        .and(CLASS_INFO.ARRIVE_DATE.le(targetDate))
                        .and(CLASS_INFO.RETURN_DATE.gt(targetDate)))
                    .fetchInto(Project.class);
            });
        } catch (Exception e) {
            logger.error("查询活跃项目失败: targetDate={}, error={}", targetDate, e.getMessage(), e);
            return Collections.emptyList(); // 返回空列表
        }
    }

    /**
     * 消息去重检查
     */
    private boolean isDuplicateMessage(String projectId, int messageType) {
        String key = projectId + "_" + messageType;
        Long lastProcessTime = messageProcessCache.get(key);
        long currentTime = System.currentTimeMillis();

        if (lastProcessTime != null && (currentTime - lastProcessTime) < DUPLICATE_MESSAGE_WINDOW) {
            // 在时间窗口内的重复消息，跳过处理
            return true;
        }

        // 记录处理时间
        messageProcessCache.put(key, currentTime);

        // 定期清理过期缓存
        cleanupExpiredCache(currentTime);

        return false;
    }

    /**
     * 清理过期的缓存条目
     */
    private void cleanupExpiredCache(long currentTime) {
        if (messageProcessCache.size() > 1000) { // 当缓存条目过多时才清理
            messageProcessCache.entrySet().removeIf(entry ->
                (currentTime - entry.getValue()) > CACHE_CLEANUP_INTERVAL);
        }
    }

    private Optional<ProjectOccupy> findOccupyByDate(Integer year, Integer month, Integer day) {
        return occupyDao.fetchOne(
                PROJECT_OCCUPY.YEAR.eq(year).and(PROJECT_OCCUPY.MONTH.eq(month)).and(PROJECT_OCCUPY.DAY.eq(day)));
    }

}