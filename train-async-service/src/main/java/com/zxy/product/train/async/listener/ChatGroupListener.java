package com.zxy.product.train.async.listener;

import com.alibaba.dubbo.common.utils.StringUtils;
import com.alibaba.fastjson.JSON;
import com.zxy.common.base.message.Message;
import com.zxy.common.dao.Fields;
import com.zxy.common.dao.support.CommonDao;
import com.zxy.common.encrypt.SM4.SM4Utils;
import com.zxy.common.message.consumer.AbstractMessageListener;
import com.zxy.product.train.api.TrainChatGroupInfoService;
import com.zxy.product.train.api.TrainChatGroupService;
import com.zxy.product.train.content.MessageHeaderContent;
import com.zxy.product.train.content.MessageTypeContent;
import com.zxy.product.train.dto.GroupMemberDto;
import com.zxy.product.train.entity.Member;
import com.zxy.product.train.entity.TrainChatGroup;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.EnvironmentAware;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import sun.misc.BASE64Decoder;
import sun.misc.BASE64Encoder;

import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.spec.SecretKeySpec;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.zxy.product.train.jooq.Tables.MEMBER;

@Service
public class ChatGroupListener  extends AbstractMessageListener implements  EnvironmentAware{

    protected static final Logger LOGGER = LoggerFactory.getLogger(ChatGroupListener.class);
    private TrainChatGroupService trainChatGroupService;
    private TrainChatGroupInfoService infoService;

    private CommonDao<Member> memberDao;

    @Value("${im.aes.key:a5e8a4f11d3c7b75082dcfe7fc01b3f5}")
    private String imAesKey;

    @Autowired
    public void setTrainChatGroupService(TrainChatGroupService trainChatGroupService) {
        this.trainChatGroupService = trainChatGroupService;
    }

    @Autowired
    public void setInfoService(TrainChatGroupInfoService infoService) {
        this.infoService = infoService;
    }

    @Autowired
    public void setMemberDao(CommonDao<Member> memberDao) {
        this.memberDao = memberDao;
    }

    @Override
    protected void onMessage(Message message) {
        String memberId = message.getHeader(MessageHeaderContent.MEMBERID);
        String classId = message.getHeader(MessageHeaderContent.CLASSID);

        switch (message.getType()) {
            case MessageTypeContent.CHAT_GROUP_MEMBER_ADD:
                addChatGroup(memberId, classId);
                break;
            case MessageTypeContent.CHAT_GROUP_MEMBER_DELETE:
                deleteChatGroupMember(memberId, classId);
                break;
            case MessageTypeContent.CHAT_GROUP_MEMBER_UPDATE:

                break;
            case MessageTypeContent.CHAT_GROUP_CREAT:
                //班级名称
                String className = message.getHeader(MessageHeaderContent.NAME);
                //班主任名称
                String memberName = message.getHeader(MessageHeaderContent.MEMBER_NAME);
                autoCreateGroup(classId, memberId, className,memberName);
                break;
            default:
                break;
        }
    }




    private String setParams(String className, String userId, String userName, List<TrainChatGroup> chatGroupList){
        Map<String, Object> params = new HashMap<>();
        params.put("groupType", 1001);
        params.put("groupName", className);
        params.put("creatorUserId", getAesEncryptParam(Optional.of(userId)));
        params.put("creatorNickname", getAesEncryptParam(Optional.of(userName)));
        List<GroupMemberDto> groupMemberDtos = new ArrayList<>();
        chatGroupList.forEach(item->{
            GroupMemberDto dto = new GroupMemberDto();
            dto.setDisplayName(getAesEncryptParam(Optional.of(item.getFullName())));
            dto.setMemberId(getAesEncryptParam(Optional.of(item.getMemberId())));
            groupMemberDtos.add(dto);
        });
        params.put("groupMembers", groupMemberDtos);
        return JSON.toJSONString(params);
    }
    private void processMemberIds(List<String> memberIds, String chatGroupName, String classId, String chatId) {
            if (!CollectionUtils.isEmpty(memberIds)) {
                List<TrainChatGroup> trainChatGroups = new ArrayList<>();
                memberIds.forEach(id -> {
                    TrainChatGroup trainChatGroup = new TrainChatGroup();
                    trainChatGroup.forInsert();
                    trainChatGroup.setClassId(classId);
                    trainChatGroup.setMemberId(id);
                    trainChatGroup.setChatGroupName(chatGroupName);
                    trainChatGroup.setChatId(chatId);
                    trainChatGroup.setDeleteFlag(TrainChatGroup.DELETE_FALSE);
                    trainChatGroups.add(trainChatGroup);
                });
                trainChatGroupService.save(trainChatGroups);
            }
    }

    //自动建组
    private void autoCreateGroup(String classId, String memberId, String className,String memberName) {
        //查询当前培训班下所有人
        List<TrainChatGroup> chatGroupList = trainChatGroupService.getChatGroupList(classId);
        if(CollectionUtils.isEmpty(chatGroupList)){
            LOGGER.info("此班级没有数据 classId ={}" ,chatGroupList);
            return;
        }
        String params = setParams(className, memberId, memberName, chatGroupList);
        //调第三方接口，创建
        Map<String, String> chatGroup = trainChatGroupService.createChatGroup(params);
        if(Objects.nonNull(chatGroup)){
            String groupId =  chatGroup.get("groupId");
            String conversionId =  chatGroup.get("conversionId");
            List<String> memberIds = chatGroupList.stream().map(TrainChatGroup::getMemberId).collect(Collectors.toList());
            //添加群成员
            processMemberIds(memberIds, className, classId, groupId);
            //添加群信息
            infoService.save(classId, className, groupId, memberId, conversionId);
        }
    }

    private void addChatGroup(String memberId, String classId) {

        String chatGroupId = infoService.getChatGroupId(classId);

        if (StringUtils.isNotEmpty(chatGroupId)) {

            TrainChatGroup chatGroupMember = trainChatGroupService.getChatGroupMember(classId, memberId);

            Member m = getMemberInfo(memberId);

            // 如果通过class和memberId查询到人员信息，说明当前人已经存在群聊列表中
            if (Objects.nonNull(chatGroupMember)) {
                if (chatGroupMember.getDeleteFlag().equals(TrainChatGroup.DELETE_TRUE)){
                    trainChatGroupService.updateGroupMember(classId, memberId,TrainChatGroup.DELETE_FALSE);
                    String phoneNumber = getAesEncryptParam(Optional.of(m.getPhoneNumber()));
                    String fullName = getAesEncryptParam(Optional.of(m.getFullName()));
                    LOGGER.error("IM群聊加密phoneNum: {},原始phoneNum: {}, 加密fullName :{}, 原始 fullName :{}", phoneNumber, m.getPhoneNumber(),fullName,m.getFullName());
                    trainChatGroupService.addChatGroupMember(phoneNumber,fullName, classId, chatGroupId);
                }
            }else{
                TrainChatGroup tcg = new TrainChatGroup();
                tcg.forInsert();
                tcg.setClassId(classId);
                tcg.setChatId(chatGroupId);
                tcg.setMemberId(memberId);
                tcg.setDeleteFlag(TrainChatGroup.DELETE_FALSE);

                trainChatGroupService.save(Collections.singletonList(tcg));
                String phoneNumber = getAesEncryptParam(Optional.of(m.getPhoneNumber()));
                String fullName = getAesEncryptParam(Optional.of(m.getFullName()));
                LOGGER.error("IM群聊加密phoneNum: {},原始phoneNum: {}, 加密fullName :{}, 原始 fullName :{}", phoneNumber, m.getPhoneNumber(),fullName,m.getFullName());
                trainChatGroupService.addChatGroupMember(phoneNumber, fullName, classId, chatGroupId);
            }
        }
    }

    private Member getMemberInfo(String memberId) {
        return memberDao.execute(e -> e.select(Fields.start().add(MEMBER.FULL_NAME, MEMBER.PHONE_NUMBER).end())
                                       .from(MEMBER).where(MEMBER.ID.eq(memberId))).fetchOne(r -> {
            Member m = new Member();
            m.setFullName(r.get(MEMBER.FULL_NAME));
            m.setPhoneNumber(SM4Utils.decryptDataCBC(r.get(MEMBER.PHONE_NUMBER)));
            return m;
        });

    }

    private void deleteChatGroupMember(String memberId, String classId) {
        TrainChatGroup chatGroupMember = trainChatGroupService.getChatGroupMember(classId, memberId);
        if (Objects.nonNull(chatGroupMember)){
            Member memberInfo = getMemberInfo(memberId);
            trainChatGroupService.updateGroupMember(classId, memberId, TrainChatGroup.DELETE_TRUE);
            String phoneNumber = getAesEncryptParam(Optional.of(memberInfo.getPhoneNumber()));
            LOGGER.error("IM群聊加密phoneNum: {},原始phoneNum: {}", phoneNumber, memberInfo.getPhoneNumber());
            trainChatGroupService.deleteChatGroupMember(classId, phoneNumber, chatGroupMember.getChatId());
        }
    }

    private String getAesEncryptParam(Optional<String> param) {
        if (param.isPresent()) {
            try {
                return aesEncrypt(param.get(), imAesKey);
            } catch (Exception e) {
                e.printStackTrace();
                return null;
            }
        }
        return null;
    }

    public String aesEncrypt(String content, String encryptKey) throws Exception {
        String result = (new BASE64Encoder()).encode(aesEncryptToBytes(content, encryptKey));
        return result.replace("\n", "");
    }

    private byte[] aesEncryptToBytes(String content, String encryptKey) throws Exception {
        KeyGenerator kgen = KeyGenerator.getInstance("AES");
        kgen.init(128);
        Cipher cipher = Cipher.getInstance("AES/ECB/PKCS5Padding");
        cipher.init(1, new SecretKeySpec(encryptKey.getBytes(), "AES"));
        return cipher.doFinal(content.getBytes("utf-8"));
    }

    private String getDecryptParam(Optional<String> param) {
        if (param.isPresent()) {
            try {
                return Decrypt(param.get(), imAesKey);
            } catch (Exception e) {
                e.printStackTrace();
                return null;
            }
        }
        return null;
    }

    public String Decrypt(String data, String key) throws Exception {
        try {
            byte[] encryptBytes = (new BASE64Decoder()).decodeBuffer(data);
            Cipher cipher = Cipher.getInstance("AES/ECB/PKCS5Padding");
            cipher.init(2, new SecretKeySpec(key.getBytes(), "AES"));
            byte[] decryptBytes = cipher.doFinal(encryptBytes);
            return new String(decryptBytes);
        } catch (Exception var5) {
            throw new Exception("sSrc=" + data + ",key=" + key);
        }
    }

    @Override
    public int[] getTypes() {
        return new int[]{MessageTypeContent.CHAT_GROUP_MEMBER_ADD, MessageTypeContent.CHAT_GROUP_MEMBER_DELETE, MessageTypeContent.CHAT_GROUP_MEMBER_UPDATE,
                MessageTypeContent.CHAT_GROUP_CREAT};
    }

    @Override
    public void setEnvironment(Environment environment) {
        imAesKey = environment.getProperty("im.aes.key", "a5e8a4f11d3c7b75082dcfe7fc01b3f5");
    }
}
