package com.zxy.product.train.async.listener;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.zxy.common.base.message.Message;
import com.zxy.common.dao.Fields;
import com.zxy.common.dao.support.CommonDao;
import com.zxy.common.message.consumer.AbstractMessageListener;
import com.zxy.common.message.provider.MessageSender;
import com.zxy.product.course.util.HttpClientUtil;
import com.zxy.product.system.util.DateUtil;
import com.zxy.product.train.content.MessageHeaderContent;
import com.zxy.product.train.content.MessageTypeContent;
import com.zxy.product.train.entity.*;
import org.jooq.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.EnvironmentAware;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

import static com.zxy.product.train.jooq.Tables.*;
import static com.zxy.product.train.jooq.tables.ClassResource.CLASS_RESOURCE;
import static com.zxy.product.train.jooq.tables.ClassroomConfiguration.CLASSROOM_CONFIGURATION;
import static com.zxy.product.train.jooq.tables.ConfigurationValue.CONFIGURATION_VALUE;

/**
 * <AUTHOR>
 * 培训班新增删除修改时，状态同步给智慧校园
 */
@Service
public class SmartCampusClassListener extends AbstractMessageListener implements EnvironmentAware {

    private static Logger logger = LoggerFactory.getLogger(SmartCampusClassListener.class);

    private String domain;
    private static final String SMART_CAMPUS_CLASS_ADD = "api/bmpvoucher/TrainingClass/add";
    private static final String SMART_CAMPUS_CLASS_UPDATE = "api/bmpvoucher/TrainingClass/put";
    private static final String SMART_CAMPUS_CLASS_DELETE = "api/bmpvoucher/TrainingClass/delByClassId";
    private static final int INSERT = 0;
    private static final int UPDATE = 1;


    private CommonDao<ClassInfo> classInfoCommonDao;
    private CommonDao<Trainee> traineeCommonDao;
    private MessageSender sender;
    private CommonDao<ConfigurationValue> configurationValueDao;



    @Autowired
    public void setClassInfoCommonDao(CommonDao<ClassInfo> classInfoCommonDao) {
        this.classInfoCommonDao = classInfoCommonDao;
    }

    @Autowired
    public void setTraineeCommonDao(CommonDao<Trainee> traineeCommonDao) {
        this.traineeCommonDao = traineeCommonDao;
    }

    @Autowired
    public void setSender(MessageSender sender) {
        this.sender = sender;
    }

    @Autowired
    public void setConfigurationValueDao(CommonDao<ConfigurationValue> configurationValueDao) {
        this.configurationValueDao = configurationValueDao;
    }

    @Override
    public int[] getTypes() {

        return new int[]{MessageTypeContent.TRAIN_CLASS_INSERT, MessageTypeContent.TRAIN_CLASS_DELETE_SMART_CAMPUS, MessageTypeContent.TRAIN_CLASS_UPDATE};
    }

    @Override
    protected void onMessage(Message message) {
        String classId = message.getHeader(MessageHeaderContent.ID);

        switch (message.getType()) {
            case MessageTypeContent.TRAIN_CLASS_INSERT:
                insertOrUpdate(classId, INSERT);
                break;
            case MessageTypeContent.TRAIN_CLASS_UPDATE:
                insertOrUpdate(classId, UPDATE);
                break;
            case MessageTypeContent.TRAIN_CLASS_DELETE_SMART_CAMPUS:
                delete(classId);
                break;
            default:
                break;
        }
    }

    private void insertOrUpdate(String classId, Integer type) {
        ClassInfo classInfo = getClassInfo(classId);

        JSONObject jsonObject = buildParam(classId, classInfo);

        switch (type) {
            case INSERT:
                logger.error("新增班级数据同步智慧校园 ，url：{} , param：{}", domain + SMART_CAMPUS_CLASS_ADD, JSON.toJSONString(jsonObject,SerializerFeature.WriteMapNullValue));
                String addResponse = HttpClientUtil.httpPost(domain + SMART_CAMPUS_CLASS_ADD, null, JSON.toJSONString(jsonObject,SerializerFeature.WriteMapNullValue));
                logger.error("新增班级数据同步智慧校园 ，response :{}", addResponse);
                break;
            case UPDATE:
                logger.error("修改班级数据同步智慧校园 ，url：{} , param：{}", domain + SMART_CAMPUS_CLASS_UPDATE, JSON.toJSONString(jsonObject,SerializerFeature.WriteMapNullValue));
                String updateResponse = HttpClientUtil.httpPost(domain + SMART_CAMPUS_CLASS_UPDATE, null, JSON.toJSONString(jsonObject,SerializerFeature.WriteMapNullValue));
                logger.error("修改班级数据同步智慧校园 ，response :{}", updateResponse);

                List<Trainee> trainees = traineeCommonDao.fetch(TRAINEE.CLASS_ID.eq(classId));
                if (!CollectionUtils.isEmpty(trainees)){
                    trainees.stream().forEach(x -> {
                        sender.send(MessageTypeContent.TRAIN_CLASS_STUDENT_UPDATE,
                                MessageHeaderContent.CLASSID, classId,
                                MessageHeaderContent.MEMBERID, x.getMemberId());
                    });
                }
                break;
            default:
                break;
        }
    }

    private JSONObject buildParam(String classId, ClassInfo classInfo) {
        String arriveDate = DateUtil.dateLongToString(classInfo.getArriveDate(), DateUtil.YYYY_MM_DD_HH_MM_SS);
        String returnDate = DateUtil.dateLongToString(classInfo.getReturnDate(), DateUtil.YYYY_MM_DD_HH_MM_SS);
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("classId", classId);
        jsonObject.put("className", classInfo.getClassName());
        jsonObject.put("deptName", classInfo.getOrganization());
        jsonObject.put("arriveTime", arriveDate);
        jsonObject.put("leaveTime", returnDate);
        jsonObject.put("trainingOpenTime", arriveDate);
        jsonObject.put("trainingEndTime", returnDate);
        jsonObject.put("classType", classInfo.getClassInfoType());
        jsonObject.put("classRoom", classInfo.getClassRoom());
        jsonObject.put("planCount", classInfo.getAmount());
        jsonObject.put("diningRoom", classInfo.getDiningRoom());
        jsonObject.put("projectStatus", classInfo.getProjectStatus());

        jsonObject.put("placeForTraining", classInfo.getAddress());
        jsonObject.put("teachers", classInfo.getExtendData());

        return jsonObject;
    }

    private ClassInfo getClassInfo(String classId) {
        ClassInfo classInfo = classInfoCommonDao.execute(
                e -> e.select(Fields.start()
                                    .add(CLASS_INFO.ARRIVE_DATE)
                                    .add(CLASS_INFO.RETURN_DATE)
                                    .add(PROJECT.NAME)
                                    .add(PROJECT.AMOUNT)
                                    .add(ORGANIZATION.NAME.as("orgName"))
                                    .add(PROJECT.ID)
                                    .add(PROJECT.STATUS)
                                    .end())
                      .from(CLASS_INFO)
                      .leftJoin(PROJECT).on(CLASS_INFO.PROJECT_ID.eq(PROJECT.ID))
                      .leftJoin(ORGANIZATION).on(PROJECT.ORGANIZATION_ID.eq(ORGANIZATION.ID))
                      .where(CLASS_INFO.ID.eq(classId))).fetchOne(r -> {
            ClassInfo ci = new ClassInfo();
            ci.setClassName(r.getValue(PROJECT.NAME));
            ci.setAmount(r.getValue(PROJECT.AMOUNT));
            ci.setProjectId(r.getValue(PROJECT.ID));
            ci.setReturnDate(r.getValue(CLASS_INFO.RETURN_DATE));
            ci.setArriveDate(r.getValue(CLASS_INFO.ARRIVE_DATE));
            ci.setOrganization(r.getValue("orgName",String.class));
            ci.setClassInfoType(r.getValue(CONFIGURATION_VALUE.NAME));
            ci.setProjectStatus(r.getValue(PROJECT.STATUS));
            return ci;
        });

        Map<String, String> diningRoomMap = queryRoomInfo(Collections.singletonList(classId),
                                                                             CONFIGURATION_VALUE,
                                                                             CONFIGURATION_VALUE.ID,
                                                                             CLASS_RESOURCE.DINING_ROOM,
                                                                             CONFIGURATION_VALUE.NAME,
                                                                             CONFIGURATION_VALUE.SORT,
                                                                             CONFIGURATION_VALUE.TYPE_ID.eq(14));
        // 教室字段查询
        Map<String, String> classRoomMap = queryRoomInfo(Collections.singletonList(classId),
                                                                            CLASSROOM_CONFIGURATION,
                                                                            CLASSROOM_CONFIGURATION.ID,
                                                                            CLASS_RESOURCE.CLASSROOM,
                                                                            CLASSROOM_CONFIGURATION.CLASSROOM,
                                                                            CLASSROOM_CONFIGURATION.SORT,
                                                                            null);

        String address = queryAddressInfo(classInfo.getProjectId()); // 培训地点

        List<Map<String,Object>> teacherInfo = queryTeacherInfo(classId);// 班主任

        classInfo.setClassRoom(classRoomMap.get(classId));
        classInfo.setDiningRoom(diningRoomMap.get(classId));

        classInfo.setAddress(address);
        classInfo.setExtendData(teacherInfo);
        return classInfo;
    }

    private List<Map<String, Object>> queryTeacherInfo(String classId) {
        return classInfoCommonDao.execute(dsl -> dsl
                .select(CLASSSTAFF_CLASS.SORT, MEMBER.FULL_NAME)
                .from(CLASSSTAFF_CLASS)
                .leftJoin(MEMBER).on(CLASSSTAFF_CLASS.MEMBER_ID.eq(MEMBER.ID))
                .where(CLASSSTAFF_CLASS.CLASS_ID.eq(classId))
                .fetch(record -> {
                    Map<String, Object> res = new HashMap<>();
                    res.put("name", record.getValue(MEMBER.FULL_NAME));
                    res.put("sort", record.getValue(CLASSSTAFF_CLASS.SORT));
                    return res;
                })
        );
    }

    private String queryAddressInfo(String projectId) {
        return classInfoCommonDao.execute(dslContext -> dslContext
                .select(CONFIGURATION_VALUE.NAME)
                .from(PROJECT)
                .leftJoin(CONFIGURATION_VALUE).on(PROJECT.ADDRESS.eq(CONFIGURATION_VALUE.ID))
                .where(PROJECT.ID.eq(projectId))
                .limit(1)
                .fetchOne(Record1::value1));
    }

    private void delete(String classId) {

        ClassInfo classInfo = classInfoCommonDao.fetchOne(CLASS_INFO.ID.eq(classId)).orElse(null);
        if (classInfo == null) {
            return;
        }

        JSONObject jsonObject = new JSONObject();
        jsonObject.put("classId", classId);

        String response = HttpClientUtil.httpPost(domain + SMART_CAMPUS_CLASS_DELETE, null, jsonObject.toJSONString());
        logger.error("删除班级数据同步智慧校园 ，response :{}，param :{}", response,jsonObject.toJSONString());
    }

    private <T> Map<String, String> queryRoomInfo(List<String> ids,
                                                  Table<?> fromTable,
                                                  Field<T> joinField,
                                                  Field<T> classResourceFiled,
                                                  Field<String> resultField,
                                                  Field<?> orderField,
                                                  Condition additionalCOndition) {

        SelectConditionStep<Record> query = configurationValueDao.execute(
                x -> x.select(Fields.start().add(CLASS_RESOURCE.CLASS_ID,
                                                 resultField).end())
                      .from(fromTable)
                      .innerJoin(CLASS_RESOURCE).on(joinField.eq(classResourceFiled))
                      .where(CLASS_RESOURCE.CLASS_ID.in(ids))
                      .and(fromTable.field("f_delete_flag", Integer.class).eq(0))

        );

        if (additionalCOndition != null){
            query.and(additionalCOndition);
        }
        return query.orderBy(orderField.asc()).fetch().stream().collect(
                Collectors.groupingBy(x -> x.getValue(CLASS_RESOURCE.CLASS_ID),
                                      Collectors.mapping(
                                              x -> x.getValue(resultField),
                                              Collectors.joining("、")
                                      )));
    }

    @Override
    public void setEnvironment(Environment environment) {
        domain = environment.getProperty("smart.campus.domain", "http://223.71.66.114:1667");
    }
}
