spring.application.name=train-web-server
#logging.level.root=ERROR

dubbo.application.name=train-web-server
dubbo.application.version=1

#dubbo.registry.address=zookeeper://mw9.zhixueyun.com:10501
#dubbo.registry.address=zookeeper://**************:30002
#dubbo.registry.address=zookeeper://127.0.0.1:2181
dubbo.registry.address=zookeeper://127.0.0.1:2181
dubbo.registry.username=zk_user
dubbo.registry.password=dreamtech
dubbo.registry.client=curator


spring.rabbitmq.host=*************
spring.rabbitmq.port=30419
spring.rabbitmq.username=guest
spring.rabbitmq.password=guest
spring.rabbitmq.virtual-host=/
spring.rabbitmq.default-exchange=amq.direct

graphite.server=**************
graphite.port=30004

# fastdfs
spring.fastdfs.connect-timeout = 30
spring.fastdfs.network-timeout = 60
spring.fastdfs.charset = utf-8
spring.fastdfs.tracker-servers = *************:10401
spring.fastdfs.tracker-http-port = 10402
spring.fastdfs.anti-steal-token = false
spring.fastdfs.secret-key = 123456

# redis
#spring.redis.cluster = true
#spring.redis.cluster.nodes= ***********:7000
##spring.redis.cluster.nodes= ***************:30006
#spring.redis.timeout=10000
spring.redis.cluster = false
#spring.redis.cluster.nodes = **************:30006
spring.redis.cluster.nodes = *************:6379
spring.redis.password = TA6sMiuSRqtTrHB7Amdg
spring.redis.connection-timeout = 2000
spring.redis.max-attempts = 3
spring.redis.timeout=10000

# jedis pool
spring.jedis.max-total=8
spring.jedis.max-idle=8
spring.jedis.block-when-exhausted=true
spring.jedis.max-wait-millis=-1

server.context-path=/api/v1/train
server.port=8085
server.tomcat.max-http-post-size=********

# upload config
spring.http.multipart.max-file-size=100Mb
spring.http.multipart.max-request-size=300Mb
spring.http.multipart.file-size-threshold=0

zyx.desensitize.accounts=admin

planning.implementation.related.ids=*********,*********,96599,96602
planning.implementation.related.names=å¨çº¿å­¦ä¹ é¨,æå­¦æ¯æé¨,æå­¦é¨,åæ ¡æè²é¨

executive.training.class.ids=a3a11901-b1a0-4f9c-b9ff-32e2adc1255b,791f1ba3-94b0-4b22-a676-fbe02e50c99b,7c649a6a-8c79-4e8c-b03b-456fb52de16e,b1e0b992-582a-4a96-a335-ea139e145c6f,c657ef61-0576-4e00-863a-d6e3dfc1b77d