package com.zxy.product.train.web.controller;

import com.zxy.common.cache.Cache;
import com.zxy.common.cache.CacheService;
import com.zxy.common.restful.RequestContext;
import com.zxy.common.restful.annotation.Param;
import com.zxy.common.restful.audit.Audit;
import com.zxy.common.restful.json.JSON;
import com.zxy.common.restful.multipart.AttachmentResolver;
import com.zxy.common.restful.security.Subject;
import com.zxy.product.human.api.FileService;
import com.zxy.product.human.entity.Member;
import com.zxy.product.system.api.operation.MessageSendService;
import com.zxy.product.system.content.MessageConstant;
import com.zxy.product.train.api.ClassInfoService;
import com.zxy.product.train.api.ProjectService;
import com.zxy.product.train.entity.ClassInfo;
import com.zxy.product.train.entity.Project;
import com.zxy.product.train.util.StringUtils;
import com.zxy.product.train.web.controller.xlsx.BaseImportController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.Optional;

/**
 * <AUTHOR>
 * @date 17/02/09
 */
@Controller
@RequestMapping("/project-approval")
public class ProjectApprovalController extends BaseImportController{
	private ProjectService projectService;
	private MessageSendService messageSendService;
	private Cache cache;
	private Cache offlineCourseCache;
	private CacheService cacheService;
	private Cache infoCache;
	private Cache statisfactionCache;
	private Cache themeCache;
	private static final String CLASS_BASIC_INFO_FOR_SIGN_UP = "class-basic-info-for-sign-up";
	private static final String DEGREE_OF_SATISFACTION = "satisfaction-degree-question";
	private static final String THEME_CACHE_KEY = "offline-theme-list-key";

	private ClassInfoService classService;

	@Autowired
	public void setMessageSendService(MessageSendService messageSendService) {
		this.messageSendService = messageSendService;
	}

	@Override
	@Autowired
	public void setAttachmentResolver(AttachmentResolver attachmentResolver) {
		this.attachmentResolver = attachmentResolver;
	}

	@Override
	@Autowired
	public void setFileService(FileService fileService) {
		this.fileService = fileService;
	}

	@Autowired
	public void setProjectService(ProjectService projectService) {
		this.projectService = projectService;
	}

	@Autowired
	public void setCacheService(CacheService cacheService) {
		this.cacheService = cacheService;
		this.cache = cacheService.create("train");
		this.offlineCourseCache = cacheService.create("offline-course");
		this.statisfactionCache = cacheService.create(DEGREE_OF_SATISFACTION);
		this.infoCache = cacheService.create("info");
		this.themeCache = cacheService.create(THEME_CACHE_KEY);
	}

	@Autowired
	public void setClassService(ClassInfoService classService) {
		this.classService = classService;
	}

	/**
	 * 根据ID预定/审核
	 *
	 * @param name
	 *            班级名称
	 * @param contactMemberId
	 *            需求单位联系人
	 * @param organizationId
	 *            需求单位
	 * @param contactPhone
	 *            联系电话
	 * @param days
	 *            培训天数
	 * @param amount
	 *            计划人数
	 * @param arriveDate
	 *            报到日
	 * @param returnDate
	 *            返程日
	 * @param isOutside
	 *            是否外部举办
	 * @param level
	 *            培训级别
	 * @param address
	 *            学习地点
	 * @param studentType
	 *            人员类型
	 * @param simpleType
	 *            补贴类型
	 * @param status
	 *            审核状态
	 * @param suggestion
	 *            审核意见
	 * @return
	 */
	@RequestMapping(method = RequestMethod.PUT, value="/{id}")
	@Param(name = "id", type = String.class, required = true)
	@Param(name = "arriveDate", type = String.class, required = true)
	@Param(name = "returnDate", type = String.class, required = true)
	@Param(name = "isOutside", type = Integer.class, required = true)
	@Param(name = "level", type = String.class)
	@Param(name = "address", type = String.class, required = true)
	@Param(name = "status", type = Integer.class, required = true)
	@Param(name = "suggestion", type = String.class)
	@Param(name = "message", type = String.class)
	@Param(name = "target", type = String.class)
	@Param(name = "surveyType", type = String.class)
	@Param(name = "stu", type = String.class)
	@Param(name = "resourceStatus", type = Integer.class)
	@Param(name = "aduitStatus", type = Integer.class)
	@Param(name="isPartyCadre",type=Integer.class,required = true)
	@JSON("id,name,contactMemberId,organizationId,contactPhone"
			+ ",days,amount,arriveDate,returnDate,isOutside,level,address" + "studentType,simpleType,status,suggestion,cost")
	@Audit(module = "活动管理", subModule = "培训管理－培训计划", action = Audit.Action.UPDATE, fisrtAction = "审核",desc = "操作审核于培训计划{0}", ids = {"id"}, jsons = {"name"}, keys = {"project"})
	public Project approval(RequestContext requestContext, Subject<Member> subject) {
		/*com.zxy.product.human.entity.Organization organization = memberService
				.getCompanyOrganizationWithLevel2ByMemberId(subject.getCurrentUserId());*/
		Optional<String> suggestion = requestContext.getOptional("suggestion", String.class);
		String classSuggestion = suggestion.orElse("无");
		ClassInfo info = classService.findClassIdByProjectId(requestContext.get("id", String.class));
		if (info != null ) {
			cache.clear(CLASS_BASIC_INFO_FOR_SIGN_UP + "#" + info.getId());
			//报道日更新课程变更，需要清除线下课程和满意度的缓存
//            themeCache.clear(THEME_CACHE_KEY + info.getId());
			statisfactionCache.clear(DEGREE_OF_SATISFACTION + info.getId());
			infoCache.clear(ClassInfo.CLASS_DETAIL_PAGE_INFO_KEY + info.getId());
			themeCache.clear(THEME_CACHE_KEY + info.getId());
		}
//		Integer num = info.getIsOutside();
		Integer isOutside = requestContext.get("isOutside", Integer.class);
//		if(!isOutside.equals(num)){
//			if(isOutside.equals(0)){
//
//			}
//		}
		Project p = projectService.approval(requestContext.get("id", String.class),
				StringUtils.dateString2OptionalLong(requestContext.get("arriveDate").toString()),
				StringUtils.dateString2OptionalLong(requestContext.get("returnDate").toString()),
				isOutside,
				requestContext.get("address", String.class),
				requestContext.get("status", Integer.class), requestContext.getOptional("suggestion", String.class),
				requestContext.getOptional("surveyType", String.class),
				requestContext.getOptional("target", String.class),
				requestContext.get("stu", String.class),
				Optional.of(subject.getCurrentUserId()),
				requestContext.getOptional("resourceStatus", Integer.class),
				requestContext.getInteger("isPartyCadre"),
				requestContext.getOptionalInteger("aduitStatus")
		);

		Integer stu = p.getStatus();
		String className = p.getName();
		String[] params1 = { className };
		String[] params2 = { className, classSuggestion };
		String[] mids = { p.getContactMemberId() };
		if (stu == 3 && stu != null) {
			messageSendService.send(subject.getCurrentUserId(),mids,MessageConstant.CLASS_RESERVE_AUDIT_PASS,Optional.ofNullable(p.getId()),
					Optional.empty(),Optional.of(params1));

		} else {
			messageSendService.send(subject.getCurrentUserId(),mids,MessageConstant.CLASS_RESERVE_AUDIT_REFUSE,Optional.ofNullable(p.getId()),
					Optional.empty(),Optional.of(params2));
		}

        /*if (p != null && me.isPresent()) {
            messageSendService.send(subject.getCurrentUserId(), mids, com.zxy.product.train.content.MessageConstant.CLASS_SMS_AUDIT,
                    Optional.of(p.getId()), Optional.empty(), Optional.of(params2));
        }*/
		return p;
	}



}
