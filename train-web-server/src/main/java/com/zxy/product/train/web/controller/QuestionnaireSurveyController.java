package com.zxy.product.train.web.controller;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.zxy.common.base.helper.PagedResult;
import com.zxy.common.cache.Cache;
import com.zxy.common.cache.CacheService;
import com.zxy.common.message.provider.MessageSender;
import com.zxy.common.office.excel.export.Writer;
import com.zxy.common.office.excel.export.support.ExcelWriter;
import com.zxy.common.restful.RequestContext;
import com.zxy.common.restful.annotation.Param;
import com.zxy.common.restful.audit.Audit;
import com.zxy.common.restful.json.JSON;
import com.zxy.common.restful.security.Permitted;
import com.zxy.common.restful.security.Subject;
import com.zxy.product.exam.api.ExamRecordService;
import com.zxy.product.exam.api.ExamService;
import com.zxy.product.exam.entity.Exam;
import com.zxy.product.system.api.permission.GrantService;
import com.zxy.product.train.api.ClassEvaluateService;
import com.zxy.product.train.api.ClassInfoService;
import com.zxy.product.train.api.MemberService;
import com.zxy.product.train.api.QuestionnaireSurveyService;
import com.zxy.product.train.content.MessageHeaderContent;
import com.zxy.product.train.content.MessageTypeContent;
import com.zxy.product.train.entity.ClassEvaluate;
import com.zxy.product.train.entity.ClassInfo;
import com.zxy.product.train.entity.Member;
import com.zxy.product.train.entity.PoiModel;
import com.zxy.product.train.entity.Question;
import com.zxy.product.train.entity.QuestionAttr;
import com.zxy.product.train.entity.ResearchAnswerRecord;
import com.zxy.product.train.entity.ResearchQuestionary;
import com.zxy.product.train.entity.ResearchRecord;
import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR> 统计与评估 查看问卷控制层
 */
@Controller
@RequestMapping("/questionnaire-survey")
public class QuestionnaireSurveyController {

  private QuestionnaireSurveyService questionnaireSurveyService;

  private MemberService memberService;

  private GrantService grantService;
  private ClassEvaluateService classEvaluateService;
  private ClassInfoService classInfoService;

  private static final String DEGREE_OF_SATISFACTION = "satisfaction-degree-question";


  private Cache scheduleCache;
  private Cache answerCache;

  private MessageSender sender;

  private ExamService examService;

  private ExamRecordService examRecordService;

  @Autowired
  public void setExamRecordService(ExamRecordService examRecordService) {
      this.examRecordService = examRecordService;
  }

  @Autowired
  public void setExamService(ExamService examService) {
      this.examService = examService;
  }

  @Autowired
  public void setGrantService(GrantService grantService) {
    this.grantService = grantService;
  }

  @Autowired
  public void setClassInfoService(ClassInfoService classInfoService) {
    this.classInfoService = classInfoService;
  }

  @Autowired
  public void setMemberService(MemberService memberService) {
    this.memberService = memberService;
  }

  @Autowired
  public void setClassEvaluateService(ClassEvaluateService classEvaluateService) {
    this.classEvaluateService = classEvaluateService;
  }

  @Autowired
  public void setQuestionnaireSurveyService(QuestionnaireSurveyService questionnaireSurveyService) {
    this.questionnaireSurveyService = questionnaireSurveyService;
  }

  @Autowired
  public void setCacheService(CacheService cacheService) {
    this.scheduleCache = cacheService.create(DEGREE_OF_SATISFACTION);
    this.answerCache = cacheService.create(ResearchAnswerRecord.DEGREE_OF_SATISACTION_ANSWER);
  }

  @Autowired
  public void setSender(MessageSender sender) {
    this.sender = sender;
  }

  /**
   * 班级问卷考试列表
   *
   * @param requestContext
   * @return
   */
  @RequestMapping(value = "/class-evaluate", method = RequestMethod.GET)
  @Param(name = "classId", type = String.class, required = true)
  @JSON("id,examCreateTime,examAllowExamTimes,examedTimes,isOverByPassExam,classId,type,resourceId,createTime,createMember,resourceName,startTime,endTime,deleteFlag,evaluateStatus,score,showAnswerRule")
  @JSON("exam.(startTime,endTime)")
  @JSON("researchQuestionary.(startTime,endTime)")
  public List<ClassEvaluate> findEvaluateRecorde(RequestContext requestContext,
      Subject<Member> subject) {
    List<ClassEvaluate> list = questionnaireSurveyService
        .findEvaluateRecorde(requestContext.get("classId", String.class),
            subject.getCurrentUserId());
    for (ClassEvaluate classEvaluate : list) {
        if (classEvaluate != null && classEvaluate.getType() == 1 && classEvaluate.getResourceId() != null) {
            String examId = classEvaluate.getResourceId();
            Exam exam = examService.getSimpleData(examId);
            Integer examedTimes = examRecordService.calculateExamTimes(examId, subject.getCurrentUserId());
            classEvaluate.setExamCreateTime(exam.getCreateTime());
            classEvaluate.setExamAllowExamTimes(exam.getAllowExamTimes());
            classEvaluate.setExamedTimes(examedTimes);
            classEvaluate.setIsOverByPassExam(exam.getIsOverByPassExam());
        }
    }
    return list;
  }

  /**
   * 班级评估问卷
   *
   * @param requestContext
   * @return
   */
  @RequestMapping(value = "/class-evaluate-type", method = RequestMethod.GET)
  @Param(name = "classId", type = String.class, required = true)
  @JSON("*")
  public List<ResearchQuestionary> findEvaluateResearch(RequestContext requestContext,
      Subject<Member> subject) {
    return questionnaireSurveyService
        .findEvaluateResearch(requestContext.get("classId", String.class),
            subject.getCurrentUserId(), null);
  }


  /**
   * 班级评估问卷
   *
   * @param requestContext
   * @return
   */
  @RequestMapping(value = "/class-evaluate-type-new", method = RequestMethod.GET)
  @Param(name = "classId", type = String.class, required = true)
  @JSON("*")
  public List<ResearchQuestionary> findNewEvaluateResearch(RequestContext requestContext,
      Subject<Member> subject) {
    Integer[] types = {5, 6, 8};
    return questionnaireSurveyService
        .findEvaluateResearch(requestContext.get("classId", String.class),
            subject.getCurrentUserId(), types);
  }

  /**
   * 班级学员满意度问卷列表
   *
   * @param requestContext
   * @return
   */
  @RequestMapping(value = "/find", method = RequestMethod.GET)
  @Permitted
  @Param(name = "classId", type = String.class, required = true)
  @JSON("id,classId,type,resourceId,createTime,createMember,resourceName,startTime,endTime,deleteFlag,response,isPartyCadre,isEnsemble,view")
  public List<ClassEvaluate> findOne(RequestContext requestContext) {
    return questionnaireSurveyService.findOne(requestContext.get("classId", String.class));
  }


  /**
   * 班级评估问卷学员参与列表页
   *
   * @param requestContext
   * @return
   */
  @RequestMapping(value = "/research-answer-record", method = RequestMethod.GET)
  @Param(name = "page", type = Integer.class, required = true)
  @Param(name = "pageSize", type = Integer.class, required = true)
  @Param(name = "resourceId", type = String.class, required = true)
  @Param(name = "name", type = String.class)
  @Param(name = "classId", type = String.class)
  @Param(name = "fullName", type = String.class)
  @Param(name = "startTime", type = Long.class)
  @JSON("recordCount")
  @JSON("items.(id,rId,sTime,suTime,researchRecordId,score,sumScore)")
  @JSON("items.member.(id,name,fullName)")
  @JSON("items.organization.(name)")
  @JSON("items.researchRecord.(researchQuestionaryId)")
  public PagedResult<ResearchAnswerRecord> findResearchAnswerRecord(RequestContext requestContext) {
    return questionnaireSurveyService.findResearchAnswerRecord(
        requestContext.get("page", Integer.class),
        requestContext.get("pageSize", Integer.class),
        requestContext.getOptional("classId", String.class),
        requestContext.getOptional("resourceId", String.class),
        requestContext.getOptional("name", String.class),
        requestContext.getOptional("fullName", String.class),
        requestContext.getOptional("startTime", Long.class)
    );
  }

  /**
   * 班级调研学员参与列表页
   *
   * @param requestContext
   * @return
   */
  @RequestMapping(value = "/research-record", method = RequestMethod.GET)
  @Param(name = "page", type = Integer.class, required = true)
  @Param(name = "pageSize", type = Integer.class, required = true)
  @Param(name = "resourceId", type = String.class, required = true)
  @Param(name = "name", type = String.class)
  @Param(name = "classId", type = String.class)
  @Param(name = "fullName", type = String.class)
  @Param(name = "startTime", type = Long.class)
  @JSON("recordCount")
  @JSON("items.(id,sTime,submitTime,status,researchQuestionaryId)")
  @JSON("items.member.(id,name,fullName)")
  @JSON("items.organization.(name)")
  public PagedResult<ResearchRecord> findResearchRecord(RequestContext requestContext) {
    return questionnaireSurveyService.findResearchRecord(
        requestContext.get("page", Integer.class),
        requestContext.get("pageSize", Integer.class),
        requestContext.getOptional("classId", String.class),
        requestContext.getOptional("resourceId", String.class),
        requestContext.getOptional("name", String.class),
        requestContext.getOptional("fullName", String.class),
        requestContext.getOptional("startTime", Long.class)
    );
  }

  /**
   * 调研评估详情页
   *
   * @param context
   * @return
   */
  @RequestMapping(value = "/research-questionary", method = RequestMethod.GET)
  @Param(name = "resourceId", type = String.class, required = true)
  @JSON("id,name,type,status,num")
  public ResearchQuestionary getResearchQuestionary(RequestContext context) {
    return questionnaireSurveyService.getResearchQuestionary(context.getString("resourceId"));
  }

  /**
   * 评估列表导出
   *
   * @param context
   * @throws IOException
   */
  @RequestMapping(value = "/research-answer-record-download", method = RequestMethod.GET)
  @Param(name = "page", type = Integer.class, required = true)
  @Param(name = "pageSize", type = Integer.class, required = true)
  @Param(name = "resourceId", type = String.class, required = true)
  @Param(name = "name", type = String.class)
  @Param(name = "classId", type = String.class)
  @Param(name = "fullName", type = String.class)
  @Param(name = "startTime", type = Long.class)
  public void researchAnswerRecordDownload(RequestContext context) throws IOException {

    HttpServletResponse response = context.getResponse();
    response.setContentType("application/octet-stream;charset=utf-8");
    response.setHeader("Content-Disposition",
        "attachment;filename=" + new String("评估详情".getBytes("gb2312"), "ISO-8859-1") + ".xlsx");
    List<ResearchAnswerRecord> list = this.findResearchAnswerRecord(context).getItems();
    Writer writer = new ExcelWriter();
    writer.sheet("评估详情列表", list)
        .field("员工编号", ResearchAnswerRecord -> ResearchAnswerRecord.getMember().getName())
        .field("姓名", ResearchAnswerRecord -> ResearchAnswerRecord.getMember().getFullName())
        .field("所属部门", ResearchAnswerRecord -> ResearchAnswerRecord.getOrganization().getName())
        .field("开始时间", ResearchAnswerRecord -> dataFormat(
            Optional.ofNullable(ResearchAnswerRecord.getsTime())))
        .field("提交时间", ResearchAnswerRecord -> dataFormat(
            Optional.ofNullable(ResearchAnswerRecord.getSuTime())))
    ;
    writer.write(response.getOutputStream());
  }

  /**
   * 调研列表导出
   *
   * @param context
   * @param subject
   * @throws IOException
   */
  @RequestMapping(value = "/research-record-download", method = RequestMethod.GET)
  @Param(name = "page", type = Integer.class, required = true)
  @Param(name = "pageSize", type = Integer.class, required = true)
  @Param(name = "resourceId", type = String.class, required = true)
  @Param(name = "name", type = String.class)
  @Param(name = "classId", type = String.class)
  @Param(name = "fullName", type = String.class)
  @Param(name = "startTime", type = Long.class)
  public void researchRecordDownload(RequestContext context, Subject<Member> subject)
      throws IOException {

    HttpServletResponse response = context.getResponse();
    response.setContentType("application/octet-stream;charset=utf-8");
    response.setHeader("Content-Disposition",
        "attachment;filename=" + new String("调研详情".getBytes("gb2312"), "ISO-8859-1") + ".xlsx");
    List<ResearchRecord> list = this.findResearchRecord(context).getItems();
    Writer writer = new ExcelWriter();
    writer.sheet("调研详情列表", list)
        .field("员工编号", ResearchRecord -> ResearchRecord.getMember().getName())
        .field("姓名", ResearchRecord -> ResearchRecord.getMember().getFullName())
        .field("所属部门", ResearchRecord -> ResearchRecord.getOrganization().getName())
        .field("开始时间", ResearchRecord -> dataFormat(Optional.ofNullable(ResearchRecord.getsTime())))
        .field("提交时间",
            ResearchRecord -> dataFormat(Optional.ofNullable(ResearchRecord.getSubmitTime())))
    ;
    writer.write(response.getOutputStream());
  }

  public String dataFormat(Optional<Long> time) {
    //时间戳转化为Sting或Date
    if (time.isPresent()) {
      SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
      String d = format.format(new Date(time.get()));
      return d;
    }
    return "";
  }

  /**
   * 学员满意度列表
   *
   * @param requestContext
   * @return
   */
  @RequestMapping(value = "/research", method = RequestMethod.GET)
  @Param(name = "page", type = Integer.class, required = true)
  @Param(name = "pageSize", type = Integer.class, required = true)
  @Param(name = "resourceId", type = String.class, required = true)
  @Param(name = "name", type = String.class)
  @Param(name = "fullName", type = String.class)
  @Param(name = "classId", type = String.class)
  @Param(name = "type", type = Integer.class)
  @Param(name = "status", type = Integer.class) //参与状态：0：未参与 1：已参与
  @JSON("recordCount")
  @JSON("items.(id,rId,sTime,suTime,researchRecordId,score,sumScore,status)")
  @JSON("items.member.(id,name,fullName)")
  @JSON("items.organization.(name)")
  @JSON("items.researchRecord.(researchQuestionaryId)")
  @Permitted(perms = {"train/class-questionnaire",""})
  public PagedResult<ResearchAnswerRecord> findResear(RequestContext requestContext) {
    Optional<Integer> type = requestContext.getOptional("type", Integer.class);
    Optional<Integer> status = requestContext.getOptional("status", Integer.class);
    if (type.isPresent() && type.get() == 7) {
      return questionnaireSurveyService.findLeaderQuestion(
          requestContext.get("page", Integer.class),
          requestContext.get("pageSize", Integer.class),
          requestContext.get("resourceId", String.class),
          requestContext.getOptional("name", String.class),
          requestContext.getOptional("fullName", String.class),
          requestContext.getOptional("classId", String.class),
          type
      );
    } else {
      return questionnaireSurveyService.findResearch(
          requestContext.get("page", Integer.class),
          requestContext.get("pageSize", Integer.class),
          requestContext.get("resourceId", String.class),
          requestContext.getOptional("name", String.class),
          requestContext.getOptional("fullName", String.class),
          requestContext.getOptional("classId", String.class),
          type,
          status
      );
    }
  }

  /**
   * 统计与评估下培训评估详情列表
   *
   * @param requestContext
   * @return
   */
  @RequestMapping(value = "/researchT", method = RequestMethod.GET)
  @Permitted
  @Param(name = "page", type = Integer.class, required = true)
  @Param(name = "pageSize", type = Integer.class, required = true)
  @Param(name = "resourceId", type = String.class, required = true)
  @Param(name = "name", type = String.class)
  @Param(name = "fullName", type = String.class)
  @Param(name = "classId", type = String.class)
  @JSON("recordCount")
  @JSON("items.(id,rId,sTime,suTime,researchRecordId,score,sumScore,status)")
  @JSON("items.member.(id,name,fullName)")
  @JSON("items.organization.(name)")
  @JSON("items.researchRecord.(researchQuestionaryId)")
  public PagedResult<ResearchAnswerRecord> findResearchT(RequestContext requestContext) {
    return questionnaireSurveyService.findResearchT(
        requestContext.get("page", Integer.class),
        requestContext.get("pageSize", Integer.class),
        requestContext.get("resourceId", String.class),
        requestContext.getOptional("name", String.class),
        requestContext.getOptional("fullName", String.class),
        requestContext.getOptional("classId", String.class)
    );
  }

  /**
   * 客观题统计列表
   *
   * @param requestContext
   * @param subject
   * @return
   */
  @RequestMapping(value = "/count-a", method = RequestMethod.GET)
  @Param(name = "classId", type = String.class, required = true)
  @JSON("questionContent,manA,manB,manC,manD,manE,manZ,manAF,manBF,manCF,manDF,manEF,manZF")
  public List<QuestionAttr> findCountA(RequestContext requestContext, Subject<Member> subject) {
//        List<QuestionAttr> findManYiA = questionnaireSurveyService.findManYiA(requestContext.get("classId", String.class));
    String string = requestContext.get("classId", String.class);
    List<QuestionAttr> attr = questionnaireSurveyService
        .findSatisfactionQuestionnairStatisticsA(string);
    sender
        .send(MessageTypeContent.SATISFACTION_QUESTIONARE_STATISTICS, MessageHeaderContent.CLASSID,
            string);
    return attr;
  }

  /**
   * 客观题课程统计列表
   *
   * @param requestContext
   * @param subject
   * @return
   */
  @RequestMapping(value = "/count-b", method = RequestMethod.GET)
  @Param(name = "classId", type = String.class, required = true)
  @JSON("questionContent,manA,manB,manC,manD,manE,manF,manZ,manAF,manBF,manCF,manDF,manEF,manZF,manFF,manCMF")
  public List<QuestionAttr> findCountB(RequestContext requestContext, Subject<Member> subject) {
//        List<QuestionAttr> attr = questionnaireSurveyService.findManYiB(requestContext.get("classId", String.class));
//        sender.send(MessageTypeContent.SATISFACTION_QUESTIONARE_STATISTICS, MessageHeaderContent.CLASSID,requestContext.get("classId", String.class));
    List<QuestionAttr> attr = questionnaireSurveyService
        .findSatisfactionQuestionnairStatisticsB(requestContext.get("classId", String.class));
    return attr;
  }

  /**
   * 客观题课程统计列表导出
   *
   * @param requestContext
   * @param subject
   * @return
   */
  @RequestMapping(value = "/count-b-dw", method = RequestMethod.GET)
  @Param(name = "classId", type = String.class, required = true)
  @JSON("questionContent,manA,manB,manC,manD,manE,manF,manZ,manAF,manBF,manCF,manDF,manEF,manZF,manFF,manCMF")
  public List<QuestionAttr> findCountBDW(RequestContext requestContext, Subject<Member> subject) {
    List<QuestionAttr> attr = questionnaireSurveyService
        .findManYiBDW(requestContext.get("classId", String.class));
    return attr;
  }

  /**
   * 主观题统计列表
   *
   * @param requestContext
   * @param subject
   * @return
   */
  @SuppressWarnings("null")
  @RequestMapping(value = "/count-c", method = RequestMethod.GET)
  @Param(name = "classId", type = String.class, required = true)
  @JSON("id,content")
  @JSON("questionAttrs.(questionContent,mName,oName,phone,answer)")
  public List<Question> findCountC(RequestContext requestContext, Subject<Member> subject) {
    //主观题
    List<Question> zhuguanList = questionnaireSurveyService
        .attr(requestContext.get("classId", String.class));
    List<QuestionAttr> attrList = questionnaireSurveyService
        .findManYiC(requestContext.get("classId", String.class));
    if (attrList != null || attrList.size() > 0) {
      Map<String, List<QuestionAttr>> attrMap = attrList.stream()
          .collect(Collectors.groupingBy(QuestionAttr::getQuestionContent));
      zhuguanList.stream().forEach(x -> {
        x.setQuestionAttrs(attrMap != null ? attrMap.get(x.getContent()) : null);
      });
    }
    return zhuguanList;
  }

  /**
   * 主观题统计列表
   *
   * @param requestContext
   * @param subject
   * @return
   */
  @RequestMapping(value = "/count-attr", method = RequestMethod.GET)
  @Param(name = "classId", type = String.class, required = true)
  @JSON("*")
  public List<QuestionAttr> findCountCQ(RequestContext requestContext, Subject<Member> subject) {
    return questionnaireSurveyService.findManYiCDW(requestContext.get("classId", String.class));
  }

  /**
   * 客观题跨班统计列表
   *
   * @return
   */
  @RequestMapping(value = "/count-zong", method = RequestMethod.GET)
  @Param(name = "classId", type = String.class, required = true)
  @JSON("questionContent,manA,manB,manC,manD,manE,manZ,manAF,manBF,manCF,manDF,manEF,manZF")
  public List<QuestionAttr> findCountZ(RequestContext requestContext, Subject<Member> subject) {

    List<String> organizationIds = grantService
        .findGrantedOrganization(subject.getCurrentUserId(), "train/project", Optional.empty(),
            Optional.empty(),
            Optional.empty(), Optional.empty(),
            requestContext.getOptional("organizationId", String.class),
            Optional.empty(), Optional.empty()).stream().map(a -> a.getId())
        .collect(Collectors.toList());

		/*String[] a = {"6d2bd13d-2ada-4a98-9517-6ccd23d0100c", "ffb70e44-80ce-4162-90a0-899e9975af59", "10000001", "10000002", "65eb7f97-38af-4e1a-b4e6-1c2088f4413b", "f6da4e79-64ab-4951-bb99-e2b23c3181e9", "38f59f56-5a74-4ece-9a2f-3cddb402d98f", "464553c8-ec77-46fb-b367-26a5690aeaf8", "fa3e50ba-85f4-4afb-b747-5e5768ab0029", "1ca04aad-fde8-4c0c-895a-181397044035", "a0beec76-6b7e-460c-9e6b-24855558ad5e", "28128b07-70bf-4960-84f2-938ffad21a8b",
                "492f5dbc-06ca-4f4a-ae76-be6936aef186", "b23decba-e330-49b3-92c5-1997cc6f70b5", "2d6db67b-d12f-4a1c-823b-b8329cbfa9a7", "92613a15-6429-43a1-8041-10faf1bf5b75", "ee16de03-624b-4393-a308-7dd30eed1534", "dcba4972-0a6a-4525-9237-f89f6232f097", "1", "4fe0cc3d-0346-4a5d-bfea-2f8ad19712de", "5b644c5d-cdb9-483a-9c96-a60d19c5dfaa", "e7e495ea-0af1-46f3-b585-b01ceeda0be6", "5c94a829-b94c-4a1b-84f7-09bbb2241e34",
                "feae62e0-3572-441a-b662-e1e11804feac", "658d773d-34fd-45ec-af76-d0def06cfb03", "38db2316-cb63-4aa6-a91f-d7e3197555e3", "610657cd-a513-472c-ac14-a02536291bd7", "2c444e21-4793-43fc-b5cc-37113f5b21e9", "541d0a95-ced2-4001-a575-28be3d43882b", "6d15d052-0403-4067-8537-64792152c1d4", "745255b5-8f10-4154-9260-5ba4ba164bdd", "81ae7ece-f17f-458e-a0f0-37ec97d28c26", "a0e91c6c-7796-43d2-8127-ab0ddc019f37", "6631d987-2d69-4f6d-97d6-4f04445cc971", "efa7eb21-ec20-4f62-bddd-743b0f91f250", "ee7e19bf-163e-4071-bf17-768bf8626e28", "2b394448-8ccf-4335-aacf-edcc6f7f6d54", "b42c21d3-e51c-414d-a42f-6ec42f92ac8a",
                "ac59a35d-2cdf-482a-8796-2139e745ed17", "436e6a67-e717-4b43-9b2f-cd64794aa1c0","d3a8bb2c-9420-40b6-a7d9-330809e66c69", "64639bef-de22-4cc7-9eb5-25534e69dc9c", "7b7c548f-1ff1-45d0-941e-1c5b733d2a4f", "82a86816-f3e9-4f30-9ed2-d1e95b670e81", "42940594", "3769839", "523359", "523370", "523348", "523337", "523326", "523315", "523271", "523304",
                "523293", "523282", "523196", "523260", "523249", "523238", "523152", "523185", "523174", "523163", "523130", "523119", "523141",
                "523108", "523097", "523086", "523075", "523062", "188", "199", "210", "177", "166", "129", "173899432", "173062645", "171190464", "167982884"};
List<String> organizationIds = Arrays.asList(a);*/

    return questionnaireSurveyService
        .findManYiKua(requestContext.get("classId", String.class), organizationIds);
  }

  /**
   * 响应中心客观题跨班统计列表
   *
   * @return
   */
  @RequestMapping(value = "/count-response-zong", method = RequestMethod.GET)
  @Param(name = "classId", type = String.class, required = true)
  @JSON("questionContent,manA,manB,manC,manD,manE,manZ,manAF,manBF,manCF,manDF,manEF,manZF")
  public List<QuestionAttr> findCountResponseZ(RequestContext requestContext,
      Subject<Member> subject) {

    return questionnaireSurveyService
        .findManResponseZYiKua(requestContext.get("classId", String.class));
  }

  /**
   * 导出客观题列表
   *
   * @param context
   * @param subject
   * @throws IOException
   */
  @RequestMapping(value = "/download/count-a", method = RequestMethod.GET)
  @Param(name = "classId", type = String.class)
  @Param(name = "flag", type = Integer.class)
  @Audit(module = "活动管理", subModule = "培训管理－班级管理", action = Audit.Action.INSERT, fisrtAction = "班级维护", secondAction = "班务管理—导出调研问卷统计", desc = "操作导出调研问卷统计于班级{0}", ids = {
      "classId"}, jsons = {"name"}, keys = {"class-info"})
  @JSON("questionContent,manA,manB,manC,manD,manE,manF,manZ,manAF,manBF,manCF,manDF,manEF,manZF,manFF,manCMF")
  public void downloadA(RequestContext context, Subject<Member> subject) throws IOException {

    String pName = "";
    DecimalFormat df = new DecimalFormat("#.00");
    ClassInfo findClassAndProjectByClassId = classInfoService
        .findClassAndProjectByClassId(context.get("classId", String.class));
    pName = "《" + findClassAndProjectByClassId.getClassName() + "》培训满意度问卷汇总统计表";
    HttpServletResponse response = context.getResponse();
    XSSFWorkbook workbook = new XSSFWorkbook();
    OutputStream out = response.getOutputStream();
    Integer flag = context.get("flag", Integer.class);
    response.setContentType("application/octet-stream;charset=utf-8");
    response.setHeader("Content-Disposition",
        "attachment;filename=" + new String(pName.getBytes("gb2312"), "ISO-8859-1") + ".xlsx");

    Float one = 0.00f;
    Float two = 0.00f;
    Float fenZ = 0.00f;
    Float fenK = 0.00f;
    String responseOne = "0.00";//学员问卷反馈率：
    String population = "0.00";//总体满意率
    String curriculum = "0.00";//课程满意率

    List<ClassEvaluate> findOne = this.findOne(context);
    if (findOne != null && findOne.size() > 0) {
      for (int i = 0; i < findOne.size(); i++) {
        if (findOne.get(i).getType() == 4) {
          Float response2 = findOne.get(i).getResponse();
          if (response2 > 0) {
            responseOne = df.format(response2);
          } else {
            responseOne = "0.00";
          }
          break;
        }
      }
    }

    List<QuestionAttr> list = this.findCountA(context, subject);
    if (list != null && list.size() > 0) {
      for (int i = 0; i < list.size(); i++) {
        fenZ += Float.valueOf(list.get(i).getManZF());
      }
      if (fenZ == 0.0) {
        population = "0.00";
      } else {
        one = (fenZ / (float) list.size()) * 100;
        one = (float) (Math.round(one)) / 100;
        population = df.format(one);
      }
    }

    List<QuestionAttr> list1 = this.findCountBDW(context, subject);
    if (list != null && list1.size() > 0) {
      for (int i = 0; i < list1.size(); i++) {
        fenK += Float.valueOf(list1.get(i).getManCMF());
      }
      if (fenZ == 0.0) {
        curriculum = "0.00";
      } else {
        two = (fenK / (float) list1.size()) * 100;
        two = (float) (Math.round(two)) / 100;
        curriculum = df.format(two);
      }
    }
    list1
        .sort((label1, label2) -> label2.getCourseLecturer().compareTo(label1.getCourseLecturer()));
    //客观题第一部分
    List<Map<String, String>> listDW = Lists.newArrayList();
    for (int i = 0; i < list.size(); i++) {
      HashMap<String, String> map = com.google.common.collect.Maps.newHashMap();
      Integer n = i + 1;
      map.put("序号", n.toString());
      map.put("内容", list.get(i).getQuestionContent());
      map.put("满意", list.get(i).getManA().toString());
      map.put("基本满意", list.get(i).getManB().toString());
      map.put("一般", list.get(i).getManC().toString());
      map.put("不满意", list.get(i).getManD().toString());
      map.put("很不满意", list.get(i).getManE().toString());
      map.put("合计（个）", list.get(i).getManZ().toString());
      map.put("满意率（%）", list.get(i).getManZF() + "%");
      map.put("满意（%）", list.get(i).getManAF() + "%");
      map.put("基本满意（%）", list.get(i).getManBF() + "%");
      map.put("一般（%）", list.get(i).getManCF() + "%");
      map.put("不满意（%）", list.get(i).getManDF() + "%");
      map.put("很不满意（%）", list.get(i).getManEF() + "%");
      listDW.add(map);
    }
    //客观题第二部分
    List<Map<String, String>> listDW1 = Lists.newArrayList();
    for (int i = 0; i < list1.size(); i++) {
      HashMap<String, String> map = com.google.common.collect.Maps.newHashMap();
      map.put("讲师", list1.get(i).getCourseLecturer());
      map.put("课程", list1.get(i).getCourseName());
      map.put("内容", list1.get(i).getCourseContent());
      map.put("满意", list1.get(i).getManA().toString());
      map.put("基本满意", list1.get(i).getManB().toString());
      map.put("一般", list1.get(i).getManC().toString());
      map.put("不满意", list1.get(i).getManD().toString());
      map.put("很不满意", list1.get(i).getManE().toString());
      map.put("合计（个）", list1.get(i).getManZ().toString());
      map.put("推荐次数（次）", list1.get(i).getManF().toString());
      map.put("课程师资满意率（%）", list1.get(i).getManCMF() + "%");
      map.put("满意率（%）", list1.get(i).getManZF() + "%");
      map.put("满意（%）", list1.get(i).getManAF() + "%");
      map.put("基本满意（%）", list1.get(i).getManBF() + "%");
      map.put("一般（%）", list1.get(i).getManCF() + "%");
      map.put("不满意（%）", list1.get(i).getManDF() + "%");
      map.put("很不满意（%）", list1.get(i).getManEF() + "%");
      map.put("推荐率（%）", list1.get(i).getManFF() + "%");
      listDW1.add(map);
    }
    //主观题
    List<QuestionAttr> list2 = this.findCountCQ(context, subject);
    List<Map<String, String>> listDW2 = Lists.newArrayList();
    if (flag != null && flag.equals(2)) {
      //需求方导出
      for (int i = 0; i < list2.size(); i++) {
        HashMap<String, String> map = com.google.common.collect.Maps.newHashMap();
        Integer n = i + 1;
        map.put("序号", n.toString());
        map.put("问题", list2.get(i).getQuestionContent());
        map.put("意见与建议", list2.get(i).getAnswer());
        map.put("学员", "-");
        map.put("手机号", "-");
        map.put("单位", "-");
        listDW2.add(map);
      }
    } else {
      for (int i = 0; i < list2.size(); i++) {
        HashMap<String, String> map = com.google.common.collect.Maps.newHashMap();
        Integer n = i + 1;
        map.put("序号", n.toString());
        map.put("问题", list2.get(i).getQuestionContent());
        map.put("意见与建议", list2.get(i).getAnswer());
        String getmName = list2.get(i).getmName() != null ? list2.get(i).getmName() : "-";
        String getPhone = list2.get(i).getPhone() != null ? list2.get(i).getPhone() : "-";
        String getoName = list2.get(i).getoName() != null ? list2.get(i).getoName() : "-";
        map.put("学员", getmName);
        map.put("手机号", getPhone);
        map.put("单位", getoName);
        listDW2.add(map);
      }
    }

    Map<String/*此处的key为每个sheet的名称，一个excel中可能有多个sheet页*/, List<Map<String/*此处key对应每一列的标题*/, String>>/*该list为每个sheet页的数据*/> map = Maps
        .newHashMap();
    map.put("客观题", listDW);
    map.put("客观题（二）", listDW1);
    map.put("主观题", listDW2);
    this.createExcel(responseOne, population, curriculum, response, map,
        new int[]{0, 1}/*此处数组为需要合并的列，可能有的需求是只需要某些列里面相同内容合并*/, workbook);
    workbook.write(out);
    out.flush();
    out.close();
//      Writer writer = new ExcelWriter();
//      writer.sheet("满意度问卷客观统计列表(维度一)", list)
//      		.indexColumn(Optional.empty())
//              .field("内容", QuestionAttr -> QuestionAttr.getQuestionContent() )
//              .field("满意", QuestionAttr -> QuestionAttr.getManA())
//              .field("基本满意", QuestionAttr -> QuestionAttr.getManB())
//              .field("一般", QuestionAttr -> QuestionAttr.getManC())
//              .field("不满意", QuestionAttr -> QuestionAttr.getManD())
//              .field("很不满意", QuestionAttr -> QuestionAttr.getManE())
//              .field("合计（个）", QuestionAttr -> QuestionAttr.getManZ())
//              .field("满意率（%）", QuestionAttr -> QuestionAttr.getManZF() + "%")
//              .field("满意（%）", QuestionAttr -> QuestionAttr.getManAF() + "%")
//              .field("基本满意（%）", QuestionAttr -> QuestionAttr.getManBF() + "%")
//              .field("一般（%）", QuestionAttr -> QuestionAttr.getManCF() + "%")
//              .field("不满意（%）", QuestionAttr -> QuestionAttr.getManDF() + "%")
//              .field("很不满意（%）", QuestionAttr -> QuestionAttr.getManEF() + "%")
//      ;

//        writer.sheet("满意度问卷客观统计列表(维度二)", list1)
//		        .field("讲师", QuestionAttr -> QuestionAttr.getCourseLecturer())
//		        .field("课程", QuestionAttr -> QuestionAttr.getCourseName())
//		        .field("内容", QuestionAttr -> QuestionAttr.getCourseContent())
//		        .field("满意", QuestionAttr -> QuestionAttr.getManA())
//		        .field("基本满意", QuestionAttr -> QuestionAttr.getManB())
//		        .field("一般", QuestionAttr -> QuestionAttr.getManC())
//		        .field("不满意", QuestionAttr -> QuestionAttr.getManD())
//		        .field("很不满意", QuestionAttr -> QuestionAttr.getManE())
//		        .field("合计（个）", QuestionAttr -> QuestionAttr.getManZ())
//		        .field("推荐次数（次）", QuestionAttr -> QuestionAttr.getManF())
//		        .field("课程师资满意率（%）", QuestionAttr -> QuestionAttr.getManCMF() + "%")
//		        .field("满意率（%）", QuestionAttr -> QuestionAttr.getManZF() + "%")
//		        .field("满意（%）", QuestionAttr -> QuestionAttr.getManAF() + "%")
//		        .field("基本满意（%）", QuestionAttr -> QuestionAttr.getManBF() + "%")
//		        .field("一般（%）", QuestionAttr -> QuestionAttr.getManCF() + "%")
//		        .field("不满意（%）", QuestionAttr -> QuestionAttr.getManDF() + "%")
//		        .field("很不满意（%）", QuestionAttr -> QuestionAttr.getManEF() + "%")
//		        .field("推荐率（%）", QuestionAttr -> QuestionAttr.getManFF() + "%")
//                ;
//        List<QuestionAttr> list2 = this.findCountCQ(context,subject);
//        System.out.println("\n例子3 -用Map来计算对象出现的次数");
//        Map<String,Integer> map2 = new HashMap<String,Integer>();
//        if (list2 != null  && list2.size() > 0) {
//        	for (int i = 0; i < list2.size(); i++) {
//            	Integer count = (Integer) map2.get(list2.get(i).getQuestionContent());
//            	map2.put(list2.get(i).getQuestionContent(), (count == null) ? 1 : count + 1);
//    		}
//        }
//        for (String in : map2.keySet()) {
//             //map.keySet()返回的是所有key的值
//             Integer str = map2.get(in);//得到每个key多对用value的值
//             System.out.println(in + "——" + str);
//         }
//        if(flag!=null&&flag.equals(2)){
//            writer.sheet("满意度问卷客观统计列表(维度三)", list2)
//            		.indexColumn(Optional.empty())
//                    .field("问题", QuestionAttr -> QuestionAttr.getQuestionContent())
//                    .field("意见与建议", QuestionAttr -> QuestionAttr.getAnswer())
//                    .field("学员", QuestionAttr -> "-")
//                    .field("手机号", QuestionAttr -> "-")
//                    .field("单位", QuestionAttr -> "-")
//            ;
//        }else{
//            String a = context.getString("classId");
//            String b = subject.getCurrentUserId();
//            writer.sheet("满意度问卷客观统计列表(维度三)", list2)
//            		.indexColumn(Optional.empty())
//                    .field("问题", QuestionAttr -> QuestionAttr.getQuestionContent())
//                    .field("意见与建议", QuestionAttr -> QuestionAttr.getAnswer())
//                    .field("学员", QuestionAttr -> QuestionAttr.getmName())
//                    .field("手机号", QuestionAttr -> QuestionAttr.getPhone())
//                    .field("单位", QuestionAttr -> QuestionAttr.getoName())
//            ;
//        }
//        writer.write(response.getOutputStream());

  }

  /**
   * 导出主观题列表
   *
   * @param context
   * @param subject
   * @throws IOException
   */
  @RequestMapping(value = "/download/count-c", method = RequestMethod.GET)
  @Param(name = "classId", type = String.class)
  @Audit(module = "活动管理", subModule = "培训管理－班级管理", action = Audit.Action.INSERT, fisrtAction = "班级维护", secondAction = "班务管理—导出调研问卷统计", desc = "操作导出调研问卷统计于班级{0}", ids = {
      "classId"}, jsons = {"name"}, keys = {"class-info"})
  public void downloadC(RequestContext context, Subject<Member> subject) throws IOException {
    HttpServletResponse response = context.getResponse();
    response.setContentType("application/octet-stream;charset=utf-8");
    response.setHeader("Content-Disposition",
        "attachment;filename=" + new String("满意度问卷客观统计列表(主观题)".getBytes("gb2312"), "ISO-8859-1")
            + ".xlsx");
    Writer writer = new ExcelWriter();
    List<QuestionAttr> list = this.findCountCQ(context, subject);
    String a = context.getString("classId");
    String b = subject.getCurrentUserId();
    writer.sheet("满意度问卷客观统计列表(维度三)", list)
        .indexColumn(Optional.empty())
        .field("问题", QuestionAttr -> QuestionAttr.getQuestionContent())
        .field("意见与建议", QuestionAttr -> QuestionAttr.getAnswer())
        .field("学员", QuestionAttr -> QuestionAttr.getmName())
        .field("手机号", QuestionAttr -> QuestionAttr.getPhone())
        .field("单位", QuestionAttr -> QuestionAttr.getoName())
    ;
    writer.write(response.getOutputStream());
  }

  /**
   * 导出主观题列表(需求方)
   *
   * @param context
   * @param subject
   * @throws IOException
   */
  @RequestMapping(value = "/download/count-cs", method = RequestMethod.GET)
  @Param(name = "classId", type = String.class)
  @Audit(module = "活动管理", subModule = "培训管理－班级管理", action = Audit.Action.INSERT, fisrtAction = "班级维护", secondAction = "班务管理—导出调研问卷统计", desc = "操作导出调研问卷统计于班级{0}", ids = {
      "classId"}, jsons = {"name"}, keys = {"class-info"})
  public void downloadC2(RequestContext context, Subject<Member> subject) throws IOException {
    HttpServletResponse response = context.getResponse();
    response.setContentType("application/octet-stream;charset=utf-8");
    response.setHeader("Content-Disposition",
        "attachment;filename=" + new String("满意度问卷客观统计列表(主观题)".getBytes("gb2312"), "ISO-8859-1")
            + ".xlsx");
    List<QuestionAttr> list = this.findCountCQ(context, subject);
    List<QuestionAttr> listq = this.findCountA(context, subject);
    Writer writer = new ExcelWriter();
    writer.sheet("满意度问卷客观统计列表(维度一)", listq)
        .indexColumn(Optional.empty())
        .field("内容", QuestionAttr -> QuestionAttr.getQuestionContent())
        .field("满意", QuestionAttr -> QuestionAttr.getManA())
        .field("基本满意", QuestionAttr -> QuestionAttr.getManB())
        .field("一般", QuestionAttr -> QuestionAttr.getManC())
        .field("不满意", QuestionAttr -> QuestionAttr.getManD())
        .field("很不满意", QuestionAttr -> QuestionAttr.getManE())
        .field("合计（个）", QuestionAttr -> QuestionAttr.getManZ())
        .field("满意率（%）", QuestionAttr -> QuestionAttr.getManZF() + "%")
        .field("满意（%）", QuestionAttr -> QuestionAttr.getManAF() + "%")
        .field("基本满意（%）", QuestionAttr -> QuestionAttr.getManBF() + "%")
        .field("一般（%）", QuestionAttr -> QuestionAttr.getManCF() + "%")
        .field("不满意（%）", QuestionAttr -> QuestionAttr.getManDF() + "%")
        .field("很不满意（%）", QuestionAttr -> QuestionAttr.getManEF() + "%")
    ;
    List<QuestionAttr> list1 = this.findCountB(context, subject);
    writer.sheet("满意度问卷客观统计列表(维度二)", list1)
        .field("内容", QuestionAttr -> QuestionAttr.getQuestionContent())
        .field("满意", QuestionAttr -> QuestionAttr.getManA())
        .field("基本满意", QuestionAttr -> QuestionAttr.getManB())
        .field("一般", QuestionAttr -> QuestionAttr.getManC())
        .field("不满意", QuestionAttr -> QuestionAttr.getManD())
        .field("很不满意", QuestionAttr -> QuestionAttr.getManE())
        .field("合计（个）", QuestionAttr -> QuestionAttr.getManZ())
        .field("推荐次数（次）", QuestionAttr -> QuestionAttr.getManF())
        .field("课程师资满意率（%）", QuestionAttr -> QuestionAttr.getManCMF() + "%")
        .field("满意率（%）", QuestionAttr -> QuestionAttr.getManZF() + "%")
        .field("满意（%）", QuestionAttr -> QuestionAttr.getManAF() + "%")
        .field("基本满意（%）", QuestionAttr -> QuestionAttr.getManBF() + "%")
        .field("一般（%）", QuestionAttr -> QuestionAttr.getManCF() + "%")
        .field("不满意（%）", QuestionAttr -> QuestionAttr.getManDF() + "%")
        .field("很不满意（%）", QuestionAttr -> QuestionAttr.getManEF() + "%")
        .field("推荐率（%）", QuestionAttr -> QuestionAttr.getManFF() + "%")
    ;
    writer.sheet("满意度问卷客观统计列表(维度三)", list)
        .indexColumn(Optional.empty())
        .field("问题", QuestionAttr -> QuestionAttr.getQuestionContent())
        .field("意见与建议", QuestionAttr -> QuestionAttr.getAnswer())
        .field("学员", QuestionAttr -> "-")
        .field("手机号", QuestionAttr -> "-")
        .field("单位", QuestionAttr -> "-")
    ;
    writer.write(response.getOutputStream());
  }

  /**
   * 导出跨班统计列表
   *
   * @param context
   * @param subject
   * @throws IOException
   */
  @RequestMapping(value = "/download/count-z", method = RequestMethod.GET)
  @Param(name = "classId", type = String.class, required = true)
  @JSON("*")
  @Audit(module = "活动管理", subModule = "培训管理－班级管理", action = Audit.Action.INSERT, fisrtAction = "班级维护", secondAction = "班务管理—导出调研问卷统计", desc = "操作导出调研问卷统计于班级{0}", ids = {
      "classId"}, jsons = {"name"}, keys = {"class-info"})
  public void downloadZ(RequestContext context, Subject<Member> subject) throws IOException {

    HttpServletResponse response = context.getResponse();
    response.setContentType("application/octet-stream;charset=utf-8");
    response.setHeader("Content-Disposition",
        "attachment;filename=" + new String("满意度问卷客观统计列表(总)".getBytes("gb2312"), "ISO-8859-1")
            + ".xlsx");
    List<QuestionAttr> list = this.findCountZ(context, subject);
    Writer writer = new ExcelWriter();
    writer.sheet("满意度问卷客观统计列表(总)", list)
        .indexColumn(Optional.empty())
        .field("内容", QuestionAttr -> QuestionAttr.getQuestionContent())
        .field("满意", QuestionAttr -> QuestionAttr.getManA())
        .field("基本满意", QuestionAttr -> QuestionAttr.getManB())
        .field("一般", QuestionAttr -> QuestionAttr.getManC())
        .field("不满意", QuestionAttr -> QuestionAttr.getManD())
        .field("很不满意", QuestionAttr -> QuestionAttr.getManE())
        .field("合计（个）", QuestionAttr -> QuestionAttr.getManZ())
        .field("满意率（%）", QuestionAttr -> QuestionAttr.getManZF() + "%")
        .field("满意（%）", QuestionAttr -> QuestionAttr.getManAF() + "%")
        .field("基本满意（%）", QuestionAttr -> QuestionAttr.getManBF() + "%")
        .field("一般（%）", QuestionAttr -> QuestionAttr.getManCF() + "%")
        .field("不满意（%）", QuestionAttr -> QuestionAttr.getManDF() + "%")
        .field("很不满意（%）", QuestionAttr -> QuestionAttr.getManEF() + "%")
    ;
    writer.write(response.getOutputStream());
  }

  /**
   * 响应中心导出跨班统计列表
   *
   * @param context
   * @param subject
   * @throws IOException
   */
  @RequestMapping(value = "/download/count-response", method = RequestMethod.GET)
  @Param(name = "classId", type = String.class, required = true)
  @JSON("*")
  @Audit(module = "活动管理", subModule = "培训管理－班级管理", action = Audit.Action.INSERT, fisrtAction = "班级维护", secondAction = "班务管理—导出调研问卷统计", desc = "操作导出调研问卷统计于班级{0}", ids = {
      "classId"}, jsons = {"name"}, keys = {"class-info"})
  public void downloadZxiangying(RequestContext context, Subject<Member> subject)
      throws IOException {

    HttpServletResponse response = context.getResponse();
    response.setContentType("application/octet-stream;charset=utf-8");
    response.setHeader("Content-Disposition",
        "attachment;filename=" + new String("满意度问卷客观统计列表(总)".getBytes("gb2312"), "ISO-8859-1")
            + ".xlsx");
    List<QuestionAttr> list = this.findCountResponseZ(context, subject);
    Writer writer = new ExcelWriter();
    writer.sheet("满意度问卷客观统计列表(总)", list)
        .field("内容", QuestionAttr -> QuestionAttr.getQuestionContent())
        .field("满意", QuestionAttr -> QuestionAttr.getManA())
        .field("基本满意", QuestionAttr -> QuestionAttr.getManB())
        .field("一般", QuestionAttr -> QuestionAttr.getManC())
        .field("不满意", QuestionAttr -> QuestionAttr.getManD())
        .field("很不满意", QuestionAttr -> QuestionAttr.getManE())
        .field("合计（个）", QuestionAttr -> QuestionAttr.getManZ())
        .field("满意率（%）", QuestionAttr -> QuestionAttr.getManZF() + "%")
        .field("满意（%）", QuestionAttr -> QuestionAttr.getManAF() + "%")
        .field("基本满意（%）", QuestionAttr -> QuestionAttr.getManBF() + "%")
        .field("一般（%）", QuestionAttr -> QuestionAttr.getManCF() + "%")
        .field("不满意（%）", QuestionAttr -> QuestionAttr.getManDF() + "%")
        .field("很不满意（%）", QuestionAttr -> QuestionAttr.getManEF() + "%")
    ;
    writer.write(response.getOutputStream());
  }

  /**
   * 满意度问卷提交
   *
   * @param requestContext
   * @param subject
   * @return
   */
  @RequestMapping(value = "/submit", method = RequestMethod.POST)
  @Param(name = "researchAnswerRecords", type = String.class, required = true)
  @Param(name = "researchQuestionaryId", type = String.class, required = true)
  @Param(name = "classId", type = String.class)
  @JSON("id,researchRecordId,questionId,createTime,answer,idea")
  public List<ResearchAnswerRecord> insertResearchAnswerRecords(RequestContext requestContext,
      Subject<Member> subject) {
    String string = requestContext.getString("researchAnswerRecords");
    String researchQuestionaryId = requestContext.getString("researchQuestionaryId");
    String key = ResearchAnswerRecord.DEGREE_OF_SATISACTION_ANSWER + researchQuestionaryId + subject
        .getCurrentUserId();
    answerCache.set(key, string, 60 * 60);
    //    	questionnaireSurveyService.save(subject.getCurrentUserId(), requestContext.getString("classId"),requestContext.getString("researchQuestionaryId"));
    sender.send(MessageTypeContent.SAVE_SATISFACTION_QUESTIONARE_ANSWER,
        MessageHeaderContent.MEMBER_ID, subject.getCurrentUserId(),
        MessageHeaderContent.CLASSID, requestContext.getString("classId"),
        MessageHeaderContent.ID, researchQuestionaryId,
        MessageHeaderContent.ANSWE_STRING, string);
    return com.alibaba.fastjson.JSON.parseArray(string, ResearchAnswerRecord.class);
//    	return questionnaireSurveyService.insertResearchAnswerRecords(
//				subject.getCurrentUserId(),
//				requestContext.getString("researchQuestionaryId"),
//    		com.alibaba.fastjson.JSON.parseArray(
//    			string, ResearchAnswerRecord.class)
//    	);
  }

  /**
   * 能力习得问卷提交
   *
   * @param requestContext
   * @param subject
   * @return
   */
  @RequestMapping(value = "/submitn", method = RequestMethod.POST)
  @Param(name = "researchAnswerRecords", type = String.class, required = true)
  @Param(name = "researchQuestionaryId", type = String.class, required = true)
  @Param(name = "classId", type = String.class)
  @JSON("id,researchRecordId,questionId,createTime,answer,idea")
  public List<ResearchAnswerRecord> insertResearchAnswerRecordsn(RequestContext requestContext,
      Subject<Member> subject) {
    String string = requestContext.getString("researchAnswerRecords");
    String researchQuestionaryId = requestContext.getString("researchQuestionaryId");
    String key = ResearchAnswerRecord.DEGREE_OF_SATISACTION_ANSWER + researchQuestionaryId + subject
        .getCurrentUserId();
    answerCache.set(key, string, 60 * 60);
    //    	questionnaireSurveyService.save(subject.getCurrentUserId(), requestContext.getString("classId"),requestContext.getString("researchQuestionaryId"));

    sender.send(MessageTypeContent.SAVE_SATISFACTION_QUESTIONARE_ANSWERN,
        MessageHeaderContent.MEMBER_ID, subject.getCurrentUserId(),
        MessageHeaderContent.CLASSID, requestContext.getString("classId"),
        MessageHeaderContent.ID, researchQuestionaryId,
        MessageHeaderContent.ANSWE_STRING, string);
    return com.alibaba.fastjson.JSON.parseArray(string, ResearchAnswerRecord.class);
//    	return questionnaireSurveyService.insertResearchAnswerRecords(
//				subject.getCurrentUserId(),
//				requestContext.getString("researchQuestionaryId"),
//    		com.alibaba.fastjson.JSON.parseArray(
//    			string, ResearchAnswerRecord.class)
//    	);
  }

  /**
   * 四度评估问卷提交
   *
   * @param requestContext
   * @param subject
   * @return
   */
  @RequestMapping(value = "/submits", method = RequestMethod.POST)
  @Param(name = "researchAnswerRecords", type = String.class, required = true)
  @Param(name = "researchQuestionaryId", type = String.class, required = true)
  @Param(name = "classId", type = String.class)
  @JSON("id,researchRecordId,questionId,createTime,answer,idea")
  public List<ResearchAnswerRecord> insertResearchAnswerRecordss(RequestContext requestContext,
      Subject<Member> subject) {
    String string = requestContext.getString("researchAnswerRecords");
    String researchQuestionaryId = requestContext.getString("researchQuestionaryId");
    String key = ResearchAnswerRecord.DEGREE_OF_SATISACTION_ANSWER + researchQuestionaryId + subject
        .getCurrentUserId();
    answerCache.set(key, string, 60 * 60);
    //    	questionnaireSurveyService.save(subject.getCurrentUserId(), requestContext.getString("classId"),requestContext.getString("researchQuestionaryId"));
    	/*sender.send(MessageTypeContent.SAVE_SATISFACTION_QUESTIONARE_ANSWERS,
    	        MessageHeaderContent.MEMBER_ID, subject.getCurrentUserId(),
    	        MessageHeaderContent.CLASSID, requestContext.getString("classId"),
    	        MessageHeaderContent.ID, researchQuestionaryId,
    	        MessageHeaderContent.ANSWE_STRING, string);*/
    return com.alibaba.fastjson.JSON.parseArray(string, ResearchAnswerRecord.class);
//    	return questionnaireSurveyService.insertResearchAnswerRecords(
//				subject.getCurrentUserId(),
//				requestContext.getString("researchQuestionaryId"),
//    		com.alibaba.fastjson.JSON.parseArray(
//    			string, ResearchAnswerRecord.class)
//    	);
  }

  /**
   * 领导问卷提交
   *
   * @param requestContext
   * @param subject
   * @return
   */
  @RequestMapping(value = "/submitl", method = RequestMethod.POST)
  @Param(name = "researchAnswerRecords", type = String.class, required = true)
  @Param(name = "researchQuestionaryId", type = String.class, required = true)
  @Param(name = "classId", type = String.class)
  @JSON("id,researchRecordId,questionId,createTime,answer,idea")
  public List<ResearchAnswerRecord> insertResearchAnswerRecordsl(RequestContext requestContext,
      Subject<Member> subject) {
    String string = requestContext.getString("researchAnswerRecords");
    String researchQuestionaryId = requestContext.getString("researchQuestionaryId");
    String key = ResearchAnswerRecord.DEGREE_OF_SATISACTION_ANSWER + researchQuestionaryId + subject
        .getCurrentUserId();
    answerCache.set(key, string, 60 * 60);
    //    	questionnaireSurveyService.save(subject.getCurrentUserId(), requestContext.getString("classId"),requestContext.getString("researchQuestionaryId"));
    sender.send(MessageTypeContent.SAVE_SATISFACTION_QUESTIONARE_ANSWERL,
        MessageHeaderContent.MEMBER_ID, subject.getCurrentUserId(),
        MessageHeaderContent.CLASSID, requestContext.getString("classId"),
        MessageHeaderContent.ID, researchQuestionaryId,
        MessageHeaderContent.ANSWE_STRING, string);
    return com.alibaba.fastjson.JSON.parseArray(string, ResearchAnswerRecord.class);
//    	return questionnaireSurveyService.insertResearchAnswerRecords(
//				subject.getCurrentUserId(),
//				requestContext.getString("researchQuestionaryId"),
//    		com.alibaba.fastjson.JSON.parseArray(
//    			string, ResearchAnswerRecord.class)
//    	);
  }

  /**
   * 调研统计页
   *
   * @param requestContext
   * @param subject
   * @return
   */
  @RequestMapping(value = "/summary-detail", method = RequestMethod.GET)
  @Param(name = "researchQuestionaryId", type = String.class, required = true)
  @Param(name = "classId", type = String.class, required = true)
  @JSON("(id,name)")
  @JSON("dimensions.(id,name,description,order,researchQuestionaryId)")
  @JSON("dimensions.questions.(id,content,createTime,order,dimensionId,type,score)")
  @JSON("dimensions.questions.questionAttrs.(*)")
  @JSON("researchAnswerRecordMaps.(researchRecordId)")
  @JSON("researchAnswerRecordMaps.researchAnswerRecords.(*)")
  public ResearchQuestionary getResearchQuestionaryDetail(RequestContext requestContext,
      Subject<Member> subject) {
    String researchQuestionaryId = requestContext.getString("researchQuestionaryId");
    String classId = requestContext.getString("classId");
    Optional<ResearchQuestionary> rq = questionnaireSurveyService
        .getSimpleData(researchQuestionaryId);
    if (rq.isPresent()) {
      ResearchQuestionary researchQuestionary = rq.get();
      researchQuestionary.setDimensions(
          questionnaireSurveyService.findDimensionsByResearchId(researchQuestionaryId));
      researchQuestionary.setResearchAnswerRecordMaps(
          questionnaireSurveyService.findRecordsByResearchQuestionaryId(researchQuestionaryId));
      return researchQuestionary;
    } else {
      return null;
    }
  }

  /**
   * 根据班级ID查询学员满意度问卷
   *
   * @param requestContext
   * @param subject
   * @return
   */
  @RequestMapping(value = "/research-detail", method = RequestMethod.GET)
  @Param(name = "classId", type = String.class, required = true)
  @JSON("(id,startTime,endTime,status,questionaryDetail,researchRecordId)")
  public ResearchQuestionary getResearchDetail(RequestContext requestContext,
      Subject<Member> subject) {
    String classId = requestContext.getString("classId");
    Optional<ResearchQuestionary> researchQuestionary = questionnaireSurveyService.getData(classId);
    ResearchQuestionary r = null;
    if (researchQuestionary.isPresent()) {
      r = researchQuestionary.get();
    }
    return r;
  }

  /**
   * 根据班级ID查询能力习得问卷
   *
   * @param requestContext
   * @param subject
   * @return
   */
  @RequestMapping(value = "/research-detailn", method = RequestMethod.GET)
  @Param(name = "classId", type = String.class, required = true)
  @JSON("(id,startTime,endTime,status,questionaryDetail,researchRecordId)")
  public ResearchQuestionary getResearchDetailn(RequestContext requestContext,
      Subject<Member> subject) {
    String classId = requestContext.getString("classId");
    Optional<ResearchQuestionary> researchQuestionary = questionnaireSurveyService
        .getDatan(classId);
    ResearchQuestionary r = null;
    if (researchQuestionary.isPresent()) {
      r = researchQuestionary.get();
    }
    return r;
  }

  /**
   * 根据班级ID查询四度评估问卷
   *
   * @param requestContext
   * @param subject
   * @return
   */
  @RequestMapping(value = "/research-details", method = RequestMethod.GET)
  @Param(name = "classId", type = String.class, required = true)
  @JSON("*")
  public ResearchQuestionary getResearchDetails(RequestContext requestContext,
      Subject<Member> subject) {
    String classId = requestContext.getString("classId");
    return questionnaireSurveyService.getDatas(classId, subject.getCurrentUserId());
  }


  /**
   * 根据班级ID查询学员上级领导
   *
   * @param requestContext
   * @param subject
   * @return
   */
  @RequestMapping(value = "/research-detaill", method = RequestMethod.GET)
  @Param(name = "classId", type = String.class, required = true)
  @JSON("(id,startTime,endTime,status,questionaryDetail,researchRecordId)")
  public ResearchQuestionary getResearchDetaill(RequestContext requestContext,
      Subject<Member> subject) {
    String classId = requestContext.getString("classId");
    Optional<ResearchQuestionary> researchQuestionary = questionnaireSurveyService
        .getDatal(classId);
    ResearchQuestionary r = null;
    if (researchQuestionary.isPresent()) {
      r = researchQuestionary.get();
    }
    return r;
  }

  /**
   * 查询当前登录人的满意度问卷作答表
   *
   * @param requestContext
   * @param subject
   * @return
   */
  @RequestMapping(value = "/get-by-research", method = RequestMethod.GET)
  @Param(name = "researchId", type = String.class, required = true)
  @JSON("id,createTime,researchQuestionaryId,status,memberId")
  @JSON("researchQuestionary.(id,name,startTime,endTime,status,permitViewCount,questionaryDetail)")
  public ResearchRecord getByResearchIdAndMemberId(RequestContext requestContext,
      Subject<Member> subject) {
    ResearchRecord researchRecord = questionnaireSurveyService.getByResearchIdAndMemberId(
        requestContext.getString("researchId"),
        subject.getCurrentUserId()
    );
    if (researchRecord != null && (null == researchRecord.getStatus()
        || researchRecord.getStatus() == ResearchRecord.STATUS_UNFINISH)) {
      String result = answerCache.get(
          ResearchAnswerRecord.DEGREE_OF_SATISACTION_ANSWER + researchRecord
              .getResearchQuestionaryId() + subject.getCurrentUserId(), String.class);
      if (result != null && result != "") {
        researchRecord.setStatus(ResearchRecord.STATUS_FINISHED);
      }
    } else {
      answerCache.clear(ResearchAnswerRecord.DEGREE_OF_SATISACTION_ANSWER + researchRecord
          .getResearchQuestionaryId() + subject.getCurrentUserId());
    }
    return researchRecord;

  }

  /**
   * 查询当前登录人的满意度能力习得问卷作答
   *
   * @param requestContext
   * @param subject
   * @return
   */
  @RequestMapping(value = "/get-by-researchn", method = RequestMethod.GET)
  @Param(name = "researchId", type = String.class, required = true)
  @JSON("id,createTime,researchQuestionaryId,status,memberId")
  @JSON("researchQuestionary.(id,name,startTime,endTime,status,permitViewCount,questionaryDetail)")
  public ResearchRecord getByResearchIdAndMemberIdn(RequestContext requestContext,
      Subject<Member> subject) {
    ResearchRecord researchRecord = questionnaireSurveyService.getByResearchIdAndMemberId(
        requestContext.getString("researchId"),
        subject.getCurrentUserId()
    );
    if (researchRecord != null && (null == researchRecord.getStatus()
        || researchRecord.getStatus() == ResearchRecord.STATUS_UNFINISH)) {
      String result = answerCache.get(
          ResearchAnswerRecord.DEGREE_OF_SATISACTION_ANSWER + researchRecord
              .getResearchQuestionaryId() + subject.getCurrentUserId(), String.class);
      if (result != null && result != "") {
        researchRecord.setStatus(ResearchRecord.STATUS_FINISHED);
      }
    } else {
      answerCache.clear(ResearchAnswerRecord.DEGREE_OF_SATISACTION_ANSWER + researchRecord
          .getResearchQuestionaryId() + subject.getCurrentUserId());
    }
    return researchRecord;

  }

  /**
   * 查询当前登录人的四度评估问卷作答
   *
   * @param requestContext
   * @param subject
   * @return
   */
  @RequestMapping(value = "/get-by-researchs", method = RequestMethod.GET)
  @Param(name = "researchId", type = String.class, required = true)
  @JSON("id,createTime,researchQuestionaryId,status,memberId")
  @JSON("researchQuestionary.(id,name,startTime,endTime,status,permitViewCount,questionaryDetail)")
  public ResearchRecord getByResearchIdAndMemberIds(RequestContext requestContext,
      Subject<Member> subject) {
    ResearchRecord researchRecord = questionnaireSurveyService.getByResearchIdAndMemberId(
        requestContext.getString("researchId"),
        subject.getCurrentUserId()
    );
    if (researchRecord != null && (null == researchRecord.getStatus()
        || researchRecord.getStatus() == ResearchRecord.STATUS_UNFINISH)) {
      String result = answerCache.get(
          ResearchAnswerRecord.DEGREE_OF_SATISACTION_ANSWER + researchRecord
              .getResearchQuestionaryId() + subject.getCurrentUserId(), String.class);
      if (result != null && result != "") {
        researchRecord.setStatus(ResearchRecord.STATUS_FINISHED);
      }
    } else {
      answerCache.clear(ResearchAnswerRecord.DEGREE_OF_SATISACTION_ANSWER + researchRecord
          .getResearchQuestionaryId() + subject.getCurrentUserId());
    }
    return researchRecord;

  }

  /**
   * 查询当前登录人的学员上级领导 问卷作答
   *
   * @param requestContext
   * @param subject
   * @return
   */
  @RequestMapping(value = "/get-by-researchl", method = RequestMethod.GET)
  @Param(name = "researchId", type = String.class, required = true)
  @JSON("id,createTime,researchQuestionaryId,status,memberId")
  @JSON("researchQuestionary.(id,name,startTime,endTime,status,permitViewCount,questionaryDetail)")
  public ResearchRecord getByResearchIdAndMemberIdl(RequestContext requestContext,
      Subject<Member> subject) {
    ResearchRecord researchRecord = questionnaireSurveyService.getByResearchIdAndMemberId(
        requestContext.getString("researchId"),
        subject.getCurrentUserId()
    );
    if (researchRecord != null && (null == researchRecord.getStatus()
        || researchRecord.getStatus() == ResearchRecord.STATUS_UNFINISH)) {
      String result = answerCache.get(
          ResearchAnswerRecord.DEGREE_OF_SATISACTION_ANSWER + researchRecord
              .getResearchQuestionaryId() + subject.getCurrentUserId(), String.class);
      if (result != null && result != "") {
        researchRecord.setStatus(ResearchRecord.STATUS_FINISHED);
      }
    } else {
      answerCache.clear(ResearchAnswerRecord.DEGREE_OF_SATISACTION_ANSWER + researchRecord
          .getResearchQuestionaryId() + subject.getCurrentUserId());
    }
    return researchRecord;

  }

  /**
   * 班级详情页学员满意度问卷
   *
   * @param requestContext
   * @param subject
   */
  @RequestMapping(value = "/front/research-detail", method = RequestMethod.GET)
  @Param(name = "researchQuestionaryId", type = String.class)
  @Param(name = "classId", type = String.class)
  @JSON("id,createTime")
  @JSON("researchQuestionary.(id,name,questionaryDetail,permitViewCount,answerPaperRule,classId)")
  @JSON("member.(id,name,fullName)")
  @JSON("researchQuestionary.dimensions.(id,name,description,order,researchQuestionaryId)")
  @JSON("researchQuestionary.dimensions.questions.(id,content,createTime,order,dimensionId,type,score)")
  @JSON("researchQuestionary.dimensions.questions.questionAttrs.(*)")
//    @CachedResult(params = {"classId", "researchQuestionaryId"}, group = DEGREE_OF_SATISFACTION, expired = 60 * 30)
  public ResearchRecord getResearchFrontDetail(RequestContext requestContext,
      Subject<Member> subject) {
    String researchQuestionaryId = requestContext.getString("researchQuestionaryId");
    String classId = requestContext.getString("classId");
//    	String result = scheduleCache.get(DEGREE_OF_SATISFACTION + "#" + classId + "#" + subject.getCurrentUserId(), String.class);
    ResearchRecord researchRecord = questionnaireSurveyService
        .getResearchRecordByResearchIdAndMemberId(
            researchQuestionaryId, subject.getCurrentUserId()).orElseGet(() -> {
          return questionnaireSurveyService
              .insert(researchQuestionaryId, subject.getCurrentUserId());
        });
//    	if (result != null && result != "") {
//    	    ResearchRecord parseObject = com.alibaba.fastjson.JSON.parseObject(result, ResearchRecord.class);
//    	    parseObject.setId(researchRecord.getId());
//    	    return parseObject;
//    	}
    Optional<ResearchQuestionary> rq = questionnaireSurveyService
        .getSimpleData(researchQuestionaryId);
//    	if (researchQuestionary.getStartTime() != null && System.currentTimeMillis() < researchQuestionary.getStartTime()) {
//    		throw new ValidationException(ErrorCode.ResearchNoStart);
//    	}
//    	if (researchRecord.getStatus() == ResearchRecord.STATUS_FINISHED) {
//    		throw new ValidationException(ErrorCode.ResearchHadFinished);
//    	}
    if (rq.isPresent()) {
      ResearchQuestionary researchQuestionary = rq.get();
      researchQuestionary.setDimensions(
          questionnaireSurveyService.findDimensionsByResearchId(researchQuestionaryId));
      researchRecord.setResearchQuestionary(researchQuestionary);
    }
    researchRecord.setMember(memberService.getMember(subject.getCurrentUserId()));
    String jsonString = com.alibaba.fastjson.JSON.toJSONString(researchRecord);
//    	scheduleCache.set(DEGREE_OF_SATISFACTION + "#" + classId + "#" + subject.getCurrentUserId(), jsonString, 60 * 30);
    return researchRecord;
  }

  /**
   * 班级详情页学员满意度问卷
   *
   * @param requestContext
   * @param subject
   */
  @RequestMapping(value = "/front/research-detail/service", method = RequestMethod.GET)
  @Param(name = "researchQuestionaryId", type = String.class)
  @Param(name = "classId", type = String.class)
  @JSON("id,createTime")
  @JSON("researchQuestionary.(id,name,questionaryDetail,permitViewCount,answerPaperRule,classId)")
  @JSON("member.(id,name,fullName)")
  @JSON("researchQuestionary.dimensions.(id,name,description,order,researchQuestionaryId)")
  @JSON("researchQuestionary.dimensions.questions.(id,content,createTime,order,dimensionId,type,score)")
  @JSON("researchQuestionary.dimensions.questions.questionAttrs.(*)")
//    @CachedResult(params = {"classId", "researchQuestionaryId"}, group = DEGREE_OF_SATISFACTION, expired = 60 * 30)
  public ResearchRecord getResearchFrontDetailService(RequestContext requestContext,
      Subject<Member> subject) {
    String researchQuestionaryId = requestContext.getString("researchQuestionaryId");
    String classId = requestContext.getString("classId");
    // String result = scheduleCache.get(DEGREE_OF_SATISFACTION + "#" + classId + "#" + subject.getCurrentUserId(), String.class);
    ResearchRecord researchRecord = questionnaireSurveyService
        .getResearchRecordByResearchIdAndMemberId(
            researchQuestionaryId, subject.getCurrentUserId()).orElseGet(() -> {
          return questionnaireSurveyService
              .insert(researchQuestionaryId, subject.getCurrentUserId());
        });
//        if (result != null && result != "") {
//            ResearchRecord parseObject = com.alibaba.fastjson.JSON.parseObject(result, ResearchRecord.class);
//            parseObject.setId(researchRecord.getId());
//            return parseObject;
//        }
    Optional<ResearchQuestionary> rq = questionnaireSurveyService
        .getSimpleData(researchQuestionaryId);
//    	if (researchQuestionary.getStartTime() != null && System.currentTimeMillis() < researchQuestionary.getStartTime()) {
//    		throw new ValidationException(ErrorCode.ResearchNoStart);
//    	}
//    	if (researchRecord.getStatus() == ResearchRecord.STATUS_FINISHED) {
//    		throw new ValidationException(ErrorCode.ResearchHadFinished);
//    	}
    if (rq.isPresent()) {
      ResearchQuestionary researchQuestionary = rq.get();
      researchQuestionary.setDimensions(
          questionnaireSurveyService.findDimensionsByResearchId(researchQuestionaryId));
      researchRecord.setResearchQuestionary(researchQuestionary);
    }
    researchRecord.setMember(memberService.getMember(subject.getCurrentUserId()));
    String jsonString = com.alibaba.fastjson.JSON.toJSONString(researchRecord);
//        scheduleCache.set(DEGREE_OF_SATISFACTION + "#" + classId + "#" + subject.getCurrentUserId(), jsonString, 60 * 30);
    return researchRecord;
  }


  /**
   * 学员满意度问卷详情页
   *
   * @param requestContext
   * @param subject
   * @return
   */
  @RequestMapping(value = "/detail", method = RequestMethod.GET)
  @Param(name = "researchRecordId", type = String.class, required = true)
  @Param(name = "classId", type = String.class)
  @JSON("id,createTime")
  @JSON("researchQuestionary.(id,name,questionaryDetail,answerPaperRule,permitViewCount)")
  @JSON("member.(id,name,fullName)")
  @JSON("researchQuestionary.dimensions.(id,name,description,order,researchQuestionaryId)")
  @JSON("researchQuestionary.dimensions.questions.(id,content,createTime,order,dimensionId,type,score)")
  @JSON("researchQuestionary.dimensions.questions.questionAttrs.(*)")
  @JSON("researchQuestionary.researchAnswerRecordMaps.(researchRecordId)")
  @JSON("researchQuestionary.researchAnswerRecordMaps.researchAnswerRecords.(*)")
  public ResearchRecord getRecordDetail(RequestContext requestContext, Subject<Member> subject) {
    ResearchRecord researchRecord = questionnaireSurveyService
        .get(requestContext.getString("researchRecordId"));
    Optional<ResearchQuestionary> rq = questionnaireSurveyService
        .getSimpleData(researchRecord.getResearchQuestionaryId());
    if (rq.isPresent()) {
      ResearchQuestionary researchQuestionary = rq.get();
      researchQuestionary.setDimensions(questionnaireSurveyService
          .findDimensionsByResearchId(researchRecord.getResearchQuestionaryId()));
      researchRecord.setResearchQuestionary(researchQuestionary);
//	        if (null == researchRecord.getStatus() || ResearchRecord.STATUS_UNFINISH == researchRecord.getStatus()) {
//	            String result = answerCache.get(ResearchAnswerRecord.DEGREE_OF_SATISACTION_ANSWER + researchRecord.getResearchQuestionaryId() + researchRecord.getMemberId(), String.class);
//	            //如果缓存中还有值说明还未保存进数据库中
//	            if (result != null && result != "") {
//	                List<ResearchAnswerRecord> list = com.alibaba.fastjson.JSON.parseArray(result, ResearchAnswerRecord.class);
//	                if (list != null && list.size() >0) {
//	                    List<ResearchAnswerRecordMap> ll = new ArrayList<ResearchAnswerRecordMap>();
//	                    for (ResearchAnswerRecord r : list) {
//	                        ResearchAnswerRecordMap map = new ResearchAnswerRecordMap();
//	                        map.setResearchRecordId(r.getResearchRecordId());
//	                        map.setResearchAnswerRecords(list);
//	                        ll.add(map);
//	                    }
//	                    researchQuestionary.setResearchAnswerRecordMaps(ll);
//	                    researchRecord.setResearchQuestionary(researchQuestionary);
//	                    return researchRecord;
//	                }
//	            }
//
//	        }
//        answerCache.clear(ResearchAnswerRecord.DEGREE_OF_SATISACTION_ANSWER + researchRecord.getResearchQuestionaryId() + subject.getCurrentUserId());
      researchQuestionary.setResearchAnswerRecordMaps(questionnaireSurveyService
          .findRecordsByResearchQuestionaryId(researchRecord.getResearchQuestionaryId()));
      researchRecord.setResearchQuestionary(researchQuestionary);
    }
    return researchRecord;
  }

  /**
   * 学员满意度问卷详情页
   *
   * @param requestContext
   * @param subject
   * @return
   */
  @RequestMapping(value = "/detail-answer", method = RequestMethod.GET)
  @Param(name = "researchRecordId", type = String.class, required = true)
  @Param(name = "classId", type = String.class)
  @JSON("id,createTime")
  @JSON("researchQuestionary.(id,name,questionaryDetail,answerPaperRule,permitViewCount)")
  @JSON("member.(id,name,fullName)")
  @JSON("researchQuestionary.dimensions.(id,name,description,order,researchQuestionaryId)")
  @JSON("researchQuestionary.dimensions.questions.(id,content,createTime,order,dimensionId,type,score)")
  @JSON("researchQuestionary.dimensions.questions.questionAttrs.(*)")
  @JSON("researchQuestionary.researchAnswerRecordMaps.(researchRecordId)")
  @JSON("researchQuestionary.researchAnswerRecordMaps.researchAnswerRecords.(*)")
  public ResearchRecord getRecordDetailAnswer(RequestContext requestContext,
      Subject<Member> subject) {
    ResearchRecord researchRecord = questionnaireSurveyService
        .get(requestContext.getString("researchRecordId"));
    Optional<ResearchQuestionary> rq = questionnaireSurveyService
        .getSimpleData(researchRecord.getResearchQuestionaryId());
    if (rq.isPresent()) {
      ResearchQuestionary researchQuestionary = rq.get();
      researchQuestionary.setDimensions(questionnaireSurveyService
          .findDimensionsByResearchId(researchRecord.getResearchQuestionaryId()));
      researchRecord.setResearchQuestionary(researchQuestionary);
//	        if (null == researchRecord.getStatus() || ResearchRecord.STATUS_UNFINISH == researchRecord.getStatus()) {
//	            String result = answerCache.get(ResearchAnswerRecord.DEGREE_OF_SATISACTION_ANSWER + researchRecord.getResearchQuestionaryId() + researchRecord.getMemberId(), String.class);
//	            //如果缓存中还有值说明还未保存进数据库中
//	            if (result != null && result != "") {
//	                List<ResearchAnswerRecord> list = com.alibaba.fastjson.JSON.parseArray(result, ResearchAnswerRecord.class);
//	                if (list != null && list.size() >0) {
//	                    List<ResearchAnswerRecordMap> ll = new ArrayList<ResearchAnswerRecordMap>();
//	                    for (ResearchAnswerRecord r : list) {
//	                        ResearchAnswerRecordMap map = new ResearchAnswerRecordMap();
//	                        map.setResearchRecordId(r.getResearchRecordId());
//	                        map.setResearchAnswerRecords(list);
//	                        ll.add(map);
//	                    }
//	                    researchQuestionary.setResearchAnswerRecordMaps(ll);
//	                    researchRecord.setResearchQuestionary(researchQuestionary);
//	                    return researchRecord;
//	                }
//	            }
//
//	        }
//        answerCache.clear(ResearchAnswerRecord.DEGREE_OF_SATISACTION_ANSWER + researchRecord.getResearchQuestionaryId() + subject.getCurrentUserId());
      researchQuestionary.setResearchAnswerRecordMaps(questionnaireSurveyService
          .findRecordsByResearchQuestionaryId(researchRecord.getResearchQuestionaryId()));
      researchRecord.setResearchQuestionary(researchQuestionary);
    }
    return researchRecord;
  }

  /**
   * 查询当前用户
   *
   * @param member
   * @return
   */
  private Member getMember(com.zxy.product.human.entity.Member member) {
    Member result = new Member();
    result.setId(member.getId());
    result.setName(member.getName());
    result.setFullName(member.getFullName());
    result.setCreateTime(member.getCreateTime());
    return result;
  }

  @RequestMapping(value = "/agencyl", method = RequestMethod.GET)
  @Param(name = "page", type = Integer.class, required = true)
  @Param(name = "pageSize", type = Integer.class, required = true)
  @Param(name = "stutas", type = String.class)
  @JSON("recordCount")
  @JSON("items.(id,name,className,startTime,researchRecordId,classId,commitStatus,endTime,type)")
  public PagedResult<ResearchQuestionary> findl(RequestContext requestContext,
      Subject<Member> subject) {
    return questionnaireSurveyService.findResearchl(requestContext.get("page", Integer.class),
        requestContext.get("pageSize", Integer.class),
        subject.getCurrentUserId(),
        requestContext.getOptional("stutas", String.class)
    );
  }

  @RequestMapping(value = "/agency", method = RequestMethod.GET)
  @Param(name = "page", type = Integer.class, required = true)
  @Param(name = "pageSize", type = Integer.class, required = true)
  @Param(name = "stutas", type = String.class)
  @JSON("recordCount")
  @JSON("items.(id,name,className,startTime,researchRecordId,classId,commitStatus,commitStatuss,commitStatusn,endTime,type,isPartyCadre)")
  public PagedResult<ResearchQuestionary> find(RequestContext requestContext,
      Subject<Member> subject) {
    return questionnaireSurveyService.findResearch(requestContext.get("page", Integer.class),
        requestContext.get("pageSize", Integer.class),
        subject.getCurrentUserId(),
        requestContext.getOptional("stutas", String.class)
    );
  }

  @RequestMapping(method = RequestMethod.POST, value = "/updateClass")
  @Param(name = "classId", type = String.class, required = true)
  @Param(name = "zong", type = String.class, required = true)
  @Param(name = "ke", type = String.class, required = true)
  @JSON("*")
  public ClassInfo update(RequestContext requestContext, Subject<Member> subject) {
    return questionnaireSurveyService.updateClass(
        requestContext.get("classId", String.class),
        requestContext.get("zong", String.class),
        requestContext.get("ke", String.class));
  }

  /**
   * 生成上级领导问卷
   *
   * @param requestContext
   * @param subject
   * @return
   */
  @RequestMapping(method = RequestMethod.POST, value = "/makel")
  @Param(name = "classId", type = String.class, required = true)
  @Param(name = "memberId", type = String.class, required = true)
  @Param(name = "userId", type = String.class, required = true)
  @JSON("*")
  public int updateL(RequestContext requestContext, Subject<Member> subject) {

    sender.send(MessageTypeContent.MAKE_QUESTIONNAIREL,
        MessageHeaderContent.MEMBERID, requestContext.get("userId", String.class),
        MessageHeaderContent.CLASSID, requestContext.getString("classId"),
        MessageHeaderContent.LEADER_MEMBER_ID, requestContext.get("memberId", String.class));
    return 0;
  }

  /**
   * 查询系统当前时间
   *
   * @return
   */
  @RequestMapping(method = RequestMethod.GET, value = "/system")
  @JSON("*")
  public long getSystem() {
    return System.currentTimeMillis();
  }

  /**
   * @param two1
   * @param one1
   * @param responseOne
   * @param title       标题集合 tilte的长度应该与list中的model的属性个数一致
   * @param maps        内容集合
   * @param mergeIndex  合并单元格的列
   * @throws IOException
   */
  @SuppressWarnings("null")
  public void createExcel(String responseOne, String population, String curriculum,
      HttpServletResponse response,
      Map<String/*sheet名*/, List<Map<String/*对应title的值*/, String>>> maps, int[] mergeIndex,
      XSSFWorkbook workbook) throws IOException {
    /*初始化excel模板*/
    XSSFSheet sheet = null;
    CellStyle style = workbook.createCellStyle();
    style.setFillForegroundColor(IndexedColors.SKY_BLUE.getIndex());
    style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
    style.setAlignment(HorizontalAlignment.CENTER);
    style.setVerticalAlignment(VerticalAlignment.CENTER);
    style.setBorderBottom(BorderStyle.THIN);
    style.setBorderLeft(BorderStyle.THIN);
    style.setBorderTop(BorderStyle.THIN);
    style.setBorderRight(BorderStyle.THIN);
    Font font = workbook.createFont();
    Short num = 11;
    font.setFontHeightInPoints(num);
    font.setFontName(" 微软雅黑 ");
    font.setBold(true);
    style.setFont(font);

    int n = 0;
    int rowIndex = 0;
    /*循环sheet页*/
    for (Map.Entry<String, List<Map<String/*对应title的值*/, String>>> entry : maps.entrySet()) {
      /*实例化sheet对象并且设置sheet名称，book对象*/
      if (!"客观题（二）".equals(entry.getKey())) {//客观题只生成一次sheet
        try {
          sheet = workbook.createSheet();
          workbook.setSheetName(n, entry.getKey());
          workbook.setSelectedTab(0);
        } catch (Exception e) {
          e.printStackTrace();
        }
        n++;
      }
      if (entry.getKey().equals("客观题")) {
        /*初始化head，填值标题行（第一行）*/
        XSSFRow row0 = sheet.createRow(0);
        row0.setHeightInPoints(20);
        Cell cell00 = row0.createCell(0);
        Cell cell01 = row0.createCell(1);
        Cell cell02 = row0.createCell(2);
        Cell cell03 = row0.createCell(3);
        Cell cell04 = row0.createCell(4);
        Cell cell05 = row0.createCell(5);
        cell00.setCellStyle(style);
        cell02.setCellStyle(style);
        cell04.setCellStyle(style);
        cell00.setCellValue("学员问卷反馈率：");
        cell01.setCellValue(responseOne + "%");
        cell02.setCellValue("总体满意率：");
        cell03.setCellValue(population + "%");
        cell04.setCellValue("课程满意率：");
        cell05.setCellValue(curriculum + "%");
        XSSFRow row1 = sheet.createRow(2);
        row1.setHeightInPoints(20);
        sheet.setColumnWidth(0, 15 * 512);
        sheet.setColumnWidth(1, 15 * 512);
        sheet.setColumnWidth(2, 10 * 512);
        sheet.setColumnWidth(3, 10 * 512);
        sheet.setColumnWidth(4, 10 * 512);
        sheet.setColumnWidth(5, 10 * 512);
        sheet.setColumnWidth(6, 10 * 512);
        sheet.setColumnWidth(7, 10 * 512);
        sheet.setColumnWidth(8, 10 * 512);
        sheet.setColumnWidth(9, 10 * 512);
        sheet.setColumnWidth(10, 10 * 512);
        sheet.setColumnWidth(11, 10 * 512);
        sheet.setColumnWidth(12, 10 * 512);
        sheet.setColumnWidth(13, 10 * 512);
        sheet.setColumnWidth(14, 10 * 512);
        sheet.setColumnWidth(15, 10 * 512);
        sheet.setColumnWidth(16, 10 * 512);
        sheet.setColumnWidth(17, 10 * 512);
        String[] title = {"序号", "内容", "满意", "基本满意", "一般", "不满意", "很不满意", "合计（个）", "满意率（%）", "满意（%）",
            "基本满意（%）",
            "一般（%）", "不满意（%）", "很不满意（%）"};
        for (int i = 0; i < title.length; i++) {
          /* 创建单元格，指定类型 */
          Cell cell_1 = row1.createCell(i);
          cell_1.setCellValue(title[i]);
          cell_1.setCellStyle(style);
        }
        /* 得到当前sheet下的数据集合 */
        List<Map<String/* 对应title的值 */, String>> list = entry.getValue();
        if (null != workbook) {
          Iterator<Map<String, String>> iterator = list.iterator();
          int index = 3;/* 这里1是从excel的第二行开始，第一行已经塞入标题了 */
          while (iterator.hasNext()) {
            Row row = sheet.createRow(index);
            /* 取得当前这行的map，该map中以key，value的形式存着这一行值 */
            Map<String, String> map = iterator.next();
            /* 循环列数，给当前行塞值 */
            for (int i = 0; i < title.length; i++) {
              Cell cell = row.createCell(i);
              cell.setCellValue(map.get(title[i]));
            }
            index++;
          }
          rowIndex = index;
        }
      } else if (entry.getKey().equals("客观题（二）")) {
        XSSFRow row2 = sheet.createRow(rowIndex++);
        String[] title2 = {"讲师", "课程", "内容", "满意", "基本满意", "一般", "不满意", "很不满意", "合计（个）", "推荐次数（次）",
            "课程师资满意率（%）", "满意率（%）"
            , "满意（%）", "基本满意（%）", "一般（%）", "不满意（%）", "很不满意（%）", "推荐率（%）"};
        for (int i = 0; i < title2.length; i++) {
          /*创建单元格，指定类型*/
          Cell cell_1 = row2.createCell(i);
          cell_1.setCellValue(title2[i]);
          cell_1.setCellStyle(style);
        }
        /*得到当前sheet下的数据集合*/
        List<Map<String/*对应title的值*/, String>> list2 = entry.getValue();
        /*遍历该数据集合*/
        List<PoiModel> poiModels = Lists.newArrayList();
        if (null != workbook) {
          Iterator<Map<String, String>> iterator = list2.iterator();
          int index = 14;/*这里1是从excel的第二行开始，第一行已经塞入标题了*/
          while (iterator.hasNext()) {
            Row row = sheet.createRow(rowIndex++);
            /*取得当前这行的map，该map中以key，value的形式存着这一行值*/
            Map<String, String> map = iterator.next();
            /*循环列数，给当前行塞值*/
            for (int i = 0; i < title2.length; i++) {
              String old = "";
              /*old存的是上一行统一位置的单元的值，第一行是最上一行了，所以从第二行开始记*/
              if (index > 14) {
                old = poiModels.get(i) == null ? "" : poiModels.get(i).getContent();
              }
              /*循环需要合并的列*/
              for (int j = 0; j < mergeIndex.length; j++) {
                if (index == 14) {
                  /*记录第一行的开始行和开始列*/
                  PoiModel poiModel = new PoiModel();
                  poiModel.setOldContent(map.get(title2[i]));
                  poiModel.setContent(map.get(title2[i]));
                  poiModel.setRowIndex(14);
                  poiModel.setCellIndex(i);
                  poiModels.add(poiModel);
                  break;
                } else if (i > 0 && mergeIndex[j] == i) {/*这边i>0也是因为第一列已经是最前一列了，只能从第二列开始*/
                  /*当前同一列的内容与上一行同一列不同时，把那以上的合并, 或者在当前元素一样的情况下，前一列的元素并不一样，这种情况也合并*/
                  /*如果不需要考虑当前行与上一行内容相同，但是它们的前一列内容不一样则不合并的情况，把下面条件中||poiModels.get(i).getContent().equals(map.get(title[i])) && !poiModels.get(i - 1).getOldContent().equals(map.get(title[i-1]))去掉就行*/
                  if (!poiModels.get(i).getContent().equals(map.get(title2[i]))
                      || poiModels.get(i).getContent().equals(map.get(title2[i])) && !poiModels
                      .get(i - 1).getOldContent().equals(map.get(title2[i - 1]))) {
                    /*当前行的当前列与上一行的当前列的内容不一致时，则把当前行以上的合并*/
                    if ((index - 1) > poiModels.get(i).getRowIndex()) {
                      CellRangeAddress cra = new CellRangeAddress(
                          poiModels.get(i).getRowIndex()/*从第二行开始*/, index - 1/*到第几行*/,
                          poiModels.get(i).getCellIndex()/*从某一列开始*/,
                          poiModels.get(i).getCellIndex()/*到第几列*/);
                      //在sheet里增加合并单元格
                      sheet.addMergedRegion(cra);

                    }
                    /*重新记录该列的内容为当前内容，行标记改为当前行标记，列标记则为当前列*/
                    poiModels.get(i).setContent(map.get(title2[i]));
                    poiModels.get(i).setRowIndex(index);
                    poiModels.get(i).setCellIndex(i);
                  }
                }
                /*处理第一列的情况*/
                if (mergeIndex[j] == i && i == 0 && !poiModels.get(i).getContent()
                    .equals(map.get(title2[i]))) {
                  if ((index - 1) > poiModels.get(i).getRowIndex()) {
                    /*当前行的当前列与上一行的当前列的内容不一致时，则把当前行以上的合并*/
                    CellRangeAddress cra = new CellRangeAddress(
                        poiModels.get(i).getRowIndex()/*从第二行开始*/, index - 1/*到第几行*/,
                        poiModels.get(i).getCellIndex()/*从某一列开始*/,
                        poiModels.get(i).getCellIndex()/*到第几列*/);
                    //在sheet里增加合并单元格
                    sheet.addMergedRegion(cra);

                  }
                  /*重新记录该列的内容为当前内容，行标记改为当前行标记*/
                  poiModels.get(i).setContent(map.get(title2[i]));
                  poiModels.get(i).setRowIndex(index);
                  poiModels.get(i).setCellIndex(i);
                }

                /*最后一行没有后续的行与之比较，所有当到最后一行时则直接合并对应列的相同内容*/
                if (mergeIndex[j] == i && index == list2.size() + 13) {
                  if ((index) > poiModels.get(i).getRowIndex()) {
                    CellRangeAddress cra = new CellRangeAddress(
                        poiModels.get(i).getRowIndex()/*从第二行开始*/, index/*到第几行*/,
                        poiModels.get(i).getCellIndex()/*从某一列开始*/,
                        poiModels.get(i).getCellIndex()/*到第几列*/);
                    //在sheet里增加合并单元格
                    sheet.addMergedRegion(cra);
                  }
                }
              }
              Cell cell = row.createCell(i);
              cell.setCellValue(map.get(title2[i]));
              /*在每一个单元格处理完成后，把这个单元格内容设置为old内容*/
              poiModels.get(i).setOldContent(old);
            }
            index++;
          }
        }
      } else {
        XSSFRow row1 = sheet.createRow(0);
        row1.setHeightInPoints(20);
        sheet.setColumnWidth(0, 5 * 512);
        sheet.setColumnWidth(1, 30 * 512);
        sheet.setColumnWidth(2, 30 * 512);
        sheet.setColumnWidth(3, 10 * 512);
        sheet.setColumnWidth(4, 10 * 512);
        sheet.setColumnWidth(5, 10 * 512);
        String[] title = {"序号", "问题", "意见与建议", "学员", "手机号", "单位"};
        for (int i = 0; i < title.length; i++) {
          /*创建单元格，指定类型*/
          Cell cell_1 = row1.createCell(i);
          cell_1.setCellValue(title[i]);
          cell_1.setCellStyle(style);
        }
        /*得到当前sheet下的数据集合*/
        List<Map<String/*对应title的值*/, String>> list = entry.getValue();
        /*遍历该数据集合*/
        List<PoiModel> poiModels = Lists.newArrayList();
        if (null != workbook) {
          Iterator<Map<String, String>> iterator = list.iterator();
          int index = 1;/*这里1是从excel的第二行开始，第一行已经塞入标题了*/
          while (iterator.hasNext()) {
            Row row = sheet.createRow(index);
            /*取得当前这行的map，该map中以key，value的形式存着这一行值*/
            Map<String, String> map = iterator.next();
            /*循环列数，给当前行塞值*/
            for (int i = 0; i < title.length; i++) {
              String old = "";
              /*old存的是上一行统一位置的单元的值，第一行是最上一行了，所以从第二行开始记*/
              if (index > 1) {
                old = poiModels.get(i) == null ? "" : poiModels.get(i).getContent();
              }
              /*循环需要合并的列*/
              int[] merge = {0, 1};
              for (int j = 0; j < merge.length; j++) {
                if (index == 1) {
                  /*记录第一行的开始行和开始列*/
                  PoiModel poiModel = new PoiModel();
                  poiModel.setOldContent(map.get(title[i]));
                  poiModel.setContent(map.get(title[i]));
                  poiModel.setRowIndex(1);
                  poiModel.setCellIndex(i);
                  poiModels.add(poiModel);
                  break;
                } else if (i > 0 && merge[j] == i) {/*这边i>0也是因为第一列已经是最前一列了，只能从第二列开始*/
                  /*当前同一列的内容与上一行同一列不同时，把那以上的合并, 或者在当前元素一样的情况下，前一列的元素并不一样，这种情况也合并*/
                  /*如果不需要考虑当前行与上一行内容相同，但是它们的前一列内容不一样则不合并的情况，把下面条件中||poiModels.get(i).getContent().equals(map.get(title[i])) && !poiModels.get(i - 1).getOldContent().equals(map.get(title[i-1]))去掉就行*/
                  if (!poiModels.get(i).getContent().equals(map.get(title[i]))) {
                    /*当前行的当前列与上一行的当前列的内容不一致时，则把当前行以上的合并*/

                    if ((index - 1) > poiModels.get(i).getRowIndex()) {
                      CellRangeAddress cra = new CellRangeAddress(
                          poiModels.get(i).getRowIndex()/*从第二行开始*/, index - 1/*到第几行*/,
                          poiModels.get(i).getCellIndex()/*从某一列开始*/,
                          poiModels.get(i).getCellIndex()/*到第几列*/);
                      //在sheet里增加合并单元格
                      sheet.addMergedRegion(cra);

                    }
                    /*重新记录该列的内容为当前内容，行标记改为当前行标记，列标记则为当前列*/
                    poiModels.get(i).setContent(map.get(title[i]));
                    poiModels.get(i).setRowIndex(index);
                    poiModels.get(i).setCellIndex(i);
                  }
                }
                /*处理第一列的情况*/
                if (merge[j] == i && i == 0 && !poiModels.get(i).getContent()
                    .equals(map.get(title[i]))) {
                  /*当前行的当前列与上一行的当前列的内容不一致时，则把当前行以上的合并*/
                  if ((index - 1) > poiModels.get(i).getRowIndex()) {
                    CellRangeAddress cra = new CellRangeAddress(
                        poiModels.get(i).getRowIndex()/*从第二行开始*/, index - 1/*到第几行*/,
                        poiModels.get(i).getCellIndex()/*从某一列开始*/,
                        poiModels.get(i).getCellIndex()/*到第几列*/);
                    //在sheet里增加合并单元格
                    sheet.addMergedRegion(cra);

                  }
                  /*重新记录该列的内容为当前内容，行标记改为当前行标记*/
                  poiModels.get(i).setContent(map.get(title[i]));
                  poiModels.get(i).setRowIndex(index);
                  poiModels.get(i).setCellIndex(i);

                }

                /*最后一行没有后续的行与之比较，所有当到最后一行时则直接合并对应列的相同内容*/
                if (merge[j] == i && index == list.size()) {
                  if ((index) > poiModels.get(i).getRowIndex()) {
                    CellRangeAddress cra = new CellRangeAddress(
                        poiModels.get(i).getRowIndex()/*从第二行开始*/, index/*到第几行*/,
                        poiModels.get(i).getCellIndex()/*从某一列开始*/,
                        poiModels.get(i).getCellIndex()/*到第几列*/);
                    //在sheet里增加合并单元格
                    sheet.addMergedRegion(cra);
                  }
                }
              }
              Cell cell = row.createCell(i);
              cell.setCellValue(map.get(title[i]));
              /*在每一个单元格处理完成后，把这个单元格内容设置为old内容*/
              poiModels.get(i).setOldContent(old);
            }
            index++;
          }
        }
      }
    }
  }

  @RequestMapping(value = "/get-researchRecord", method = RequestMethod.GET)
  @Param(name = "classId", type = String.class, required = true)
  @Param(name = "type", type = Integer.class, required = true)
  @JSON("*")
  public ResearchRecord getResearchRecord(RequestContext requestContext, Subject<Member> subject) {

    ResearchRecord findResearchRecord = questionnaireSurveyService
        .findResearchRecord(subject.getCurrentUserId(), requestContext.get("classId", String.class),
            requestContext.get("type", Integer.class));
    if (findResearchRecord != null) {
      String result = answerCache.get(
          ResearchAnswerRecord.DEGREE_OF_SATISACTION_ANSWER + findResearchRecord
              .getResearchQuestionaryId() + subject.getCurrentUserId(), String.class);
      if (result != null && result != "") {
        findResearchRecord.setStatus(ResearchRecord.STATUS_FINISHED);
        return findResearchRecord;
      } else {
        return findResearchRecord;
      }
    } else {
      return null;
    }
  }

}
