
package com.zxy.product.train.web.controller;

import com.google.common.collect.ImmutableMap;
import com.zxy.common.base.helper.PagedResult;
import com.zxy.common.cache.Cache;
import com.zxy.common.cache.CacheService;
import com.zxy.common.restful.RequestContext;
import com.zxy.common.restful.annotation.Param;
import com.zxy.common.restful.audit.Audit;
import com.zxy.common.restful.json.JSON;
import com.zxy.common.restful.security.Permitted;
import com.zxy.common.restful.security.Subject;
import com.zxy.product.course.content.CommonConstant;
import com.zxy.product.human.api.MemberConfigService;
import com.zxy.product.human.api.MemberPositionInnerService;
import com.zxy.product.human.api.MemberService;
import com.zxy.product.human.api.position.PositionService;
import com.zxy.product.human.entity.MemberConfig;
import com.zxy.product.human.entity.Position;
import com.zxy.product.system.api.internalswitch.InternalSwitchService;
import com.zxy.product.system.api.permission.GrantService;
import com.zxy.product.system.entity.InternalSwitch;
import com.zxy.product.system.entity.UserBehavior;
import com.zxy.product.system.util.DateUtil;
import com.zxy.product.train.api.ClassInfoService;
import com.zxy.product.train.api.RectifyDeviationService;
import com.zxy.product.train.api.SettlementService;
import com.zxy.product.train.entity.ClassDetail;
import com.zxy.product.train.entity.ClassInfo;
import com.zxy.product.train.entity.Member;
import com.zxy.product.train.entity.ResearchQuestionary;
import com.zxy.product.train.entity.Settlement;
import com.zxy.product.train.entity.SettlementMemberQuantity;
import com.zxy.product.train.util.StringUtils;
import com.zxy.product.train.web.aspectj.Behavior;
import com.zxy.product.train.web.util.ImportExportUtil;
import org.apache.poi.hssf.usermodel.HSSFCell;
import org.apache.poi.hssf.usermodel.HSSFCellStyle;
import org.apache.poi.hssf.usermodel.HSSFFont;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.hssf.util.HSSFColor;
import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.ss.util.CellRangeAddress;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.zxy.product.train.web.util.SecurePathCdnUtils.generateSecurePathCdn;

/**
 * <AUTHOR>
 * @date 17/02/09...
 */
@Controller
@RequestMapping("/class-info")
public class ClassInfoController {
	private static final Logger LOGGER = LoggerFactory.getLogger(ClassInfoController.class);
	
    private ClassInfoService classInfoService;
    private MemberService memberService;
    private GrantService grantService;
    private RectifyDeviationService rectifyDeviationService;
    private MemberConfigService memberConfigService;

    private Cache staffCache;
    private Cache detailCache;
    private Cache infoCache;
    private Cache signUpCache;
    private Cache activityClassInfoCache;
    private CacheService cacheService;
    private static final String CACHE_CLASSSTAFF_FOR_DETAIL = "class-detail-staff-key";
    private static final String CLASS_BASIC_INFO_FOR_SIGN_UP = "class-basic-info-for-sign-up";

    private Cache intelligentSearchCache;
    private InternalSwitchService internalSwitchService;
    private final static String KEY = "intelligentSearch";

    private MemberPositionInnerService memberPositionInnerService;

    private PositionService positionService;

    private SettlementService settlementService;

    @Autowired
    public void setSettlementService(SettlementService settlementService) {
        this.settlementService = settlementService;
    }

    @Autowired
    public void setPositionService(PositionService positionService) {
        this.positionService = positionService;
    }

    @Autowired
    public void setMemberPositionInnerService(MemberPositionInnerService memberPositionInnerService) {
        this.memberPositionInnerService = memberPositionInnerService;
    }

    @Autowired
    public void setInternalSwitchService(InternalSwitchService internalSwitchService) {
        this.internalSwitchService = internalSwitchService;
    }

    @Autowired
    public void setMemberService(MemberService memberService) {
        this.memberService = memberService;
    }

    @Autowired
    public void setRectifyDeviationService(RectifyDeviationService rectifyDeviationService) {
        this.rectifyDeviationService = rectifyDeviationService;
    }

    @Autowired
    public void setClassInfoService(ClassInfoService classInfoService) {
        this.classInfoService = classInfoService;
    }
    @Autowired
    public void setGrantService(GrantService grantService) {
        this.grantService = grantService;
    }

    @Autowired
    public void setCacheService(CacheService cacheService) {
        this.cacheService = cacheService;
        this.staffCache = cacheService.create("staff");
        this.detailCache = cacheService.create("detail");
        this.infoCache = cacheService.create("info");
        this.signUpCache = cacheService.create("train");
        this.intelligentSearchCache = cacheService.create(InternalSwitch.KEY);
        this.activityClassInfoCache = cacheService.create("activity-class-info-list");
    }
    @Autowired
    public void setMemberConfigService(MemberConfigService memberConfigService) {
        this.memberConfigService = memberConfigService;
    }
    /**
     * 前端查询班级信息分页列表
     */
    @RequestMapping(value = "frontend", method = RequestMethod.GET)
    @Param(name = "page", type = Integer.class, required = true)
    @Param(name = "pageSize", type = Integer.class, required = true)
    @Param(name = "MIScode", type = String.class)
    @Param(name = "className", type = String.class)
    @Param(name = "startTime", type = String.class)
    @Param(name = "organizationId", type = String.class)
    @JSON("recordCount")
    @JSON("items.(id,code,className,organization,contactPeople,contactPhone,arriveDate,returnDate,projectId,status,chatGroup,chatGroupId,conversionId,isManualFinish)")
    public PagedResult<ClassInfo> findFront(RequestContext requestContext, Subject<Member> subject) {
        Optional<String> OpStartTime = requestContext.getOptional("startTime", String.class);
        Integer reachYear = null;
        Integer reachMonth = null;
        if (OpStartTime.isPresent()) {
            String startTime = OpStartTime.get();
            reachYear = Integer.parseInt(startTime.substring(0, 4));
            reachMonth = Integer.parseInt(startTime.substring(5));
        }
        return classInfoService.frontFind(
                requestContext.get("page", Integer.class),
                requestContext.get("pageSize", Integer.class),
                Optional.of(subject.getCurrentUserId()),
                requestContext.getOptional("MIScode", String.class),
                requestContext.getOptional("className", String.class),
                Optional.ofNullable(reachYear),
                Optional.ofNullable(reachMonth),
                Optional.empty(), requestContext.getOptional("organizationId", String.class),0);
    }

    /**
     * 响应中心班级列表
     */
    @RequestMapping(value = "response", method = RequestMethod.GET)
    @Param(name = "page", type = Integer.class, required = true)
    @Param(name = "pageSize", type = Integer.class, required = true)
    @Param(name = "MIScode", type = String.class)
    @Param(name = "className", type = String.class)
    @Param(name = "classStatus", type = Integer.class)
    @JSON("recordCount")
    @JSON("items.(id,code,className,amount,registNumber,organization,contactPeople,contactPhone,traineeNum,arriveDate,status,questionaryId,projectId,notice,questionaryType,isPartyCadre)")
    public PagedResult<ClassInfo> responseFind(RequestContext requestContext) {
        return classInfoService.frontResponse(
                requestContext.get("page", Integer.class),
                requestContext.get("pageSize", Integer.class),
                requestContext.getOptional("MIScode", String.class),
                requestContext.getOptional("className", String.class),
                requestContext.getOptional("classStatus", Integer.class));
    }

    @RequestMapping(value = "find", method = RequestMethod.GET)
    @Param(name = "page", type = Integer.class, required = true)
    @Param(name = "pageSize", type = Integer.class, required = true)
    @Param(name = "organizationId", type = String.class)
    @Param(name = "status", type = Integer.class)
    @Param(name = "name", type = String.class)
    @Param(name = "MIScode", type = String.class)
    @Param(name = "returnDay", type = String.class)
    @Param(name = "reportDay", type = String.class)
    @Param(name = "month", type = Integer.class)
    @Param(name = "year", type = Integer.class)
    @Param(name = "isOutside", type = Integer.class) //培训方式 0：内部培训 1：院外培训 2：在线学习
    @JSON("recordCount")
    @JSON("items.(id,code,className,organization,contactPeople,contactPhone,arriveDate,returnDate,status,projectId,organizationId,isOutside,"
            + "implementationYear,implementationMonth,traineeNum,submitNum)")
    public PagedResult<ClassInfo> find(RequestContext requestContext,Subject<Member> subject) {
        Optional<Integer> month = requestContext.getOptional("month", Integer.class);
        Optional<Integer> year = requestContext.getOptional("year", Integer.class);
        Optional<String> returnDay = requestContext.getOptional("returnDay", String.class);
        Optional<String> reportDay = requestContext.getOptional("reportDay", String.class);
        Optional<Integer> status = requestContext.getOptional("status", Integer.class);
        Optional<Integer> isOutside = requestContext.getOptional("isOutside", Integer.class);
        Long returnBeginDate = null;
        Long reportBeginDate = null;
        Long reportEndDate = null;
        Long returnEndDate = null;

        if (returnDay.isPresent()) {
            String day[] = returnDay.get().split("to");
            returnBeginDate = this.dateConvert(day[0]);
            returnEndDate = this.dateConvert(day[1]);
        }
        if (reportDay.isPresent()) {
            String days[] = reportDay.get().split("to");
            reportBeginDate = this.dateConvert(days[0]);
            reportEndDate = this.dateConvert(days[1]);
        }
        if (year.isPresent() && year.get() == 0) {
            year = year.ofNullable(null);
        }
        if (month.isPresent() && month.get() == 0) {
            month = month.ofNullable(null);
        }
        if (status.isPresent() && status.get() == 0) {
            status = status.ofNullable(null);
        }

        List<String> organizationIds = grantService.findGrantedOrganization(subject.getCurrentUserId(), "train/class-info", Optional.empty(), Optional.empty(),
                Optional.empty(), Optional.empty(), requestContext.getOptional("organizationId", String.class),
                Optional.empty(), Optional.empty()).stream().map(a -> a.getId()).collect(Collectors.toList());
        return classInfoService.find(
                requestContext.get("page", Integer.class),
                requestContext.get("pageSize", Integer.class),
                requestContext.getOptional("name", String.class),
                requestContext.getOptional("MIScode", String.class),
                requestContext.getOptional("organizationId", String.class),
                status,
                isOutside,
                Optional.ofNullable(reportBeginDate),
                Optional.ofNullable(reportEndDate),
                Optional.ofNullable(returnBeginDate),
                Optional.ofNullable(returnEndDate),
                year,
                month, Optional.empty(),organizationIds);
    }
    @RequestMapping(value = "findClass", method = RequestMethod.GET)
    @Param(name = "page", type = Integer.class, required = true)
    @Param(name = "pageSize", type = Integer.class, required = true)
    @Param(name = "config", type = Integer.class, required = true)
    @Param(name = "organizationId", type = String.class)
    @Param(name = "name", type = String.class)
    @Param(name = "course", type = Integer.class)
    @Param(name = "month", type = Integer.class)
    @Param(name = "classTime", type = String.class)
    @Param(name = "year", type = Integer.class)
    @Param(name = "typeId", type = String.class)
    @JSON("recordCount")
    @JSON("items.(id,code,className,organization,projectId,arriveDate,returnDate,organizationId,type)")
    public PagedResult<ClassInfo> findClass(RequestContext requestContext,Subject<Member> subject) {
        List<String> organizationIds = grantService.findGrantedOrganization(subject.getCurrentUserId(), "train/class-info", Optional.empty(), Optional.empty(),
                Optional.empty(), Optional.empty(), requestContext.getOptional("organizationId", String.class),
                Optional.empty(), Optional.empty()).stream().map(a -> a.getId()).collect(Collectors.toList());
        Optional<Integer> month = requestContext.getOptional("month", Integer.class);
        Optional<Integer> year = requestContext.getOptional("year", Integer.class);
        Optional<String> classTime = requestContext.getOptional("classTime", String.class);
        Long reportBeginDate = null;
        Long reportEndDate = null;
        if (year.isPresent() && year.get() == 0) {
            year = year.ofNullable(null);
        }
        if (month.isPresent() && month.get() == 0) {
            month = month.ofNullable(null);
        }
        if (classTime.isPresent()) {
            String days[] = classTime.get().split("to");
            reportBeginDate = this.dateConvert(days[0]);
            reportEndDate = this.dateConvert(days[1]);
        }
        return classInfoService.findClass(
        		requestContext.get("page", Integer.class),
                requestContext.get("pageSize", Integer.class),
                requestContext.get("config", Integer.class),
                requestContext.getOptional("name", String.class),
                requestContext.getOptional("typeId", String.class),
                month,
                requestContext.getOptional("course", Integer.class),
                year,
                Optional.ofNullable(reportBeginDate),
                Optional.ofNullable(reportEndDate),
                requestContext.getOptional("organizationId", String.class),organizationIds);
    }

    public Long dateConvert(String stringDate) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        Long longDate = null;
        try {
            Date date = simpleDateFormat.parse(stringDate.trim());
            longDate = date.getTime();
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return longDate;
    }

    /**
     * 根据计划ID查询班级信息
     *
     * @param requestContext
     * @return
     */
    @Permitted
    @RequestMapping(value = "find-by-project-id/{id}", method = RequestMethod.GET)
    @Param(name = "id", type = String.class, required = true)
//    @JSON("id,code,className,projectId,classTeacherPhone,classTeacher,arriveDate,returnDate,isOutside,"
//            + "surveyType,target,classInfoType,studentType,simpleType,isPlan,status,groupId,shortName,"
//            + "groupOrder,implementationYear,implementationMonth,organization, contactPhone, teacherPhone, "
//            + "teacherEmail, contachEmail, teacher, contactPeople, trainObject, romm, classRoom,"
//            + "classRoomName,diningRoom,address,confirm,restRoom,notice")
//    @JSON("classDetail.(id,classId,coverId,bannerId,attendanceType,haveProvinceLeader,haveMinister,needGroupPhoto,"
//            + "photoTime,needVideo,videoRequirement,needMakeCourse,courseVideoRequirement,needNet,tableType,"
//            + "otherRequirement,showRanking,rankingRule,notice)")
    @JSON("*.*")
    public ClassInfo findByProjectId(RequestContext requestContext) {
        return classInfoService.findByProjectId(requestContext.get("id", String.class));
    }


    @RequestMapping(value = "/{id}", method = RequestMethod.PUT)
    @Param(name = "id", type = String.class, required = true)
    @Param(name = "memberId", type = String.class)
    @Param(name = "classTeacher", type = String.class)
    @Param(name = "restRoom", type = String.class)
    @Param(name = "classRoom", type = String.class)
    @Param(name = "diningRoom", type = String.class)
    @Param(name = "teacherPhone", type = String.class)
    @Param(name = "cover", type = String.class)//班级封面id
    @Param(name = "coverPath", type = String.class)//班级封面路径
    @Param(name = "haveProvinceLeader", type = Integer.class)
    @Param(name = "haveMinister", type = Integer.class)
    @Param(name = "needGroupPhoto", type = Integer.class)
    @Param(name = "photoTime", type = String.class)
    @Param(name = "needVideo", type = Integer.class)
    @Param(name = "videoRequirement", type = String.class)
    @Param(name = "needMakeCourse", type = Integer.class)
    @Param(name = "courseVideoRequirement", type = String.class)
    @Param(name = "needNet", type = Integer.class)
    @Param(name = "tableType", type = String.class)
    @Param(name = "otherRequirement", type = String.class)
    @Param(name = "bannerId", type = String.class)
    @Param(name = "confirm", type = Integer.class)
    @Param(name = "classInfoType", type = String.class)
    @Param(name = "classroomIds", type = String.class)
    @Param(name = "restaurantsIds", type = String.class)
    @Param(name = "guestroomsIds", type = String.class)
    @Param(name = "path", type = String.class)
    @Param(name = "role", type = Integer.class)
    @Param(name = "falg", type = Integer.class)
    @JSON("id,code,className,organization,contactPeople,contactPhone,arriveDate,returnDate,status,diningRoom,restRoom,classRoom,classInfoType")
    @Audit(module = "活动管理", subModule = "培训管理－班级管理", action = Audit.Action.UPDATE, fisrtAction = "管理", desc = "修改班级{0}", ids = {"id"}, jsons = {"name"}, keys = {"class-info"})
    public ClassInfo update(RequestContext requestContext, Subject<Member> subject) {
        Optional<String> classTeacher = requestContext.getOptionalString("classTeacher");
        Optional<String> path = requestContext.getOptionalString("path");
        Optional<Integer> role = requestContext.getOptionalInteger("role");
        if (classTeacher.isPresent()) {
            staffCache.clear(CACHE_CLASSSTAFF_FOR_DETAIL + requestContext.getString("id"));
        }
        detailCache.clear(ClassDetail.CACHE_DETAIL_KEY + requestContext.getString("id"));
        infoCache.clear(ClassInfo.CLASS_DETAIL_PAGE_INFO_KEY + requestContext.getString("id"));
        signUpCache.clear(CLASS_BASIC_INFO_FOR_SIGN_UP + "#" + requestContext.getString("id"));
        return classInfoService.updateManage(requestContext.get("id", String.class),
                requestContext.getOptional("classTeacher", String.class),
                requestContext.getOptional("memberId", String.class),
                requestContext.getOptional("restRoom", String.class),
                requestContext.getOptional("classRoom", String.class),
                requestContext.getOptional("diningRoom", String.class),
                requestContext.getOptional("cover", String.class),
                requestContext.getOptional("haveProvinceLeader", Integer.class),
                requestContext.getOptional("haveMinister", Integer.class),
                requestContext.getOptional("needGroupPhoto", Integer.class),
                StringUtils.datetimeString2OptionalLong(requestContext.getOptional("photoTime", String.class)),
                requestContext.getOptional("needVideo", Integer.class),
                requestContext.getOptional("videoRequirement", String.class),
                requestContext.getOptional("needMakeCourse", Integer.class),
                requestContext.getOptional("courseVideoRequirement", String.class),
                requestContext.getOptional("needNet", Integer.class),
                requestContext.getOptional("tableType", String.class),
                requestContext.getOptional("otherRequirement", String.class),
                requestContext.getOptional("bannerId", String.class),
                requestContext.getOptional("confirm", Integer.class), requestContext.getOptional("teacherPhone", String.class),
                requestContext.getOptional("classInfoType", String.class),
                requestContext.getOptionalString("path"),
                requestContext.getOptionalString("restaurantsIds"),
                requestContext.getOptionalString("guestroomsIds"),
                requestContext.getOptionalInteger("role"),
                requestContext.getOptionalString("coverPath"),
                requestContext.getOptionalInteger("falg")
        );
    }

    @RequestMapping(value = "/home-class", method = RequestMethod.GET)
    @Param()
    @JSON("id,className,organization,arriveDate,returnDate")
    public ClassInfo getHomeClass(RequestContext requestContext, Subject<Member> subject) {
        return classInfoService.findClassByMemberId(subject.getCurrentUserId()).orElse(new ClassInfo());
    }


    @RequestMapping(value = "/view/{id}", method = RequestMethod.PUT)
    @Param(name = "id", type = String.class, required = true)
    @Param(name = "view", type = Integer.class, required = true)
    @JSON("*")
    public Map<String, String> updateView(RequestContext requestContext, Subject<Member> subject) {
        classInfoService.updateView(requestContext.get("id", String.class), requestContext.getInteger("view"));
        return ImmutableMap.of("result", "success");
    }

    @RequestMapping(value = "get", method = RequestMethod.GET)
//    @Permitted --短信小程序无法传入Authorization 故而注释
    @Param(name = "id", type = String.class, required = true)
    @Param(name = "contentType")
    @JSON("*.*")
    @Behavior(contentID = "id",type= UserBehavior.CLICK,pageSource="/api/v1/train/class-info/get",contentType = "contentType")
    public ClassInfo get(RequestContext requestContext) {
        String classId = requestContext.get("id", String.class);
//        String result = infoCache.get(ClassInfo.CLASS_DETAIL_PAGE_INFO_KEY + classId, String.class);
//        if (result != null && result != "") {
//            return com.alibaba.fastjson.JSON.parseObject(result,ClassInfo.class);
//        }
        ClassInfo classInfo = classInfoService.get(requestContext.get("id", String.class));
//        infoCache.set(ClassInfo.CLASS_DETAIL_PAGE_INFO_KEY + classId, com.alibaba.fastjson.JSON.toJSONString(classInfo), 30 * 60);
        return classInfo;
    }

    /**
     * 根据计划ID查询班级信息(单表查询)
     *
     * @param requestContext
     * @return
     */
    @RequestMapping(value = "single/{id}", method = RequestMethod.GET)
    @Param(name = "id", type = String.class, required = true)
    @Permitted
    @JSON("id,code,className,projectId,status,arriveDate,returnDate,notice,confirm,organizationId,submitNum,isOverproof,courseSalary,isPartyCadre")
    public ClassInfo findSingleByProjectId(RequestContext requestContext) {
        ClassInfo classInfo =  classInfoService.findSingleByProjectId(requestContext.get("id", String.class));
        int flag = classInfo!=null&&(classInfo.getCourseSalary()==null||classInfo.getCourseSalary()==0)?0:1;
        classInfo.setCourseSalary(flag);
        return classInfo;
    }

    /**
     * 创建动态标题数组
     */
    private static String[] createDynamicTitles(String[] baseTitles, int maxSize) {
        // 计算总列数：基础列 + (结算日期 + 结算人数) * maxSize
        int totalColumns = baseTitles.length + (2 * maxSize);
        String[] dynamicTitles = new String[totalColumns];

        // 复制基础标题
        System.arraycopy(baseTitles, 0, dynamicTitles, 0, baseTitles.length);

        // 添加动态标题
        int index = baseTitles.length;
        for (int i = 0; i < maxSize; i++) {
            dynamicTitles[index++] = "培训日期";
            dynamicTitles[index++] = "结算人数";
        }
        return dynamicTitles;
    }

    /**
     * 导出班级月结算数据
     *
     * @param context
     * @throws IOException
     */
    @RequestMapping(value = "/download", method = RequestMethod.GET)
    @Param(name = "month", type = String.class, required = true)
    @Audit(module = "活动管理", subModule = "培训管理－班级管理", action = Audit.Action.EXPORT, fisrtAction = "导出月结数据", desc = "导出月结数据")
    public void download(RequestContext context,Subject<Member> subject) throws IOException {
        HttpServletResponse response = context.getResponse();
        response.setContentType("application/octet-stream;charset=utf-8");
        response.setHeader("Content-Disposition", "attachment;filename=" + new String(("月度结算数据导出").getBytes("gb2312"), "ISO-8859-1") + ".xls");
        String month = context.get("month").toString();
        List<String> organizationIds = grantService.findGrantedOrganization(subject.getCurrentUserId(), "train/class-info", Optional.empty(), Optional.empty(),
                Optional.empty(), Optional.empty(),  Optional.empty(),
                Optional.empty(), Optional.empty()).stream().map(a -> a.getId()).collect(Collectors.toList());

        List<Settlement> list = classInfoService.findMonth(month,organizationIds);

        Map<String, List<SettlementMemberQuantity>> map = new HashMap<>();
        //查询结算人员配置数据
        if(!CollectionUtils.isEmpty(list)){
            List<String> classIds = list.stream().map(Settlement::getClassId).collect(Collectors.toList());
            if(!CollectionUtils.isEmpty(classIds)){
                List<SettlementMemberQuantity> memberByClassIds = settlementService.findMemberByClassIds(classIds);
                map = memberByClassIds.stream().collect(Collectors.groupingBy(SettlementMemberQuantity::getClassId));
            }
        }

        HSSFWorkbook workbook = new HSSFWorkbook();
        HSSFSheet sheet = workbook.createSheet(month);
        OutputStream out = response.getOutputStream();
        HSSFRow row0 = sheet.createRow(0);
        Cell cell00 = row0.createCell(0);
        row0.setHeightInPoints(40);
        sheet.setColumnWidth(0, 20 * 256);
        //合并单元格
        sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, 99));
        cell00.setCellValue("“" + month + "”" + "培训班、会议结算数据");
        HSSFCellStyle style = this.font(workbook, 18, 0);
        cell00.setCellStyle(style);
        String[] basicNum = {"月份", "编号", "主办部门", "班名","培训起止时间","学习地点","班主任","住宿地点","用餐地点","教室", "结算天数", "上课天数", "结算人数", "结算人日", "备注"
                , "培训天数", "培训人数", "培训人日"};

        String[] num = new String[100];
        Integer maxSize = null;
        //查询到最大一条，然后填充标题
        if(Objects.nonNull(map) && map.size() > 0){
            maxSize = map.values().stream()
                    .mapToInt(List::size)
                    .max()
                    .orElse(0);
            num = createDynamicTitles(basicNum, maxSize);
        }else {
            num = basicNum;
        }

        Object[][] value = new Object[list.size() + 2][100];
        String dates[] = month.split("-");
        for (int m = 0; m < num.length; m++) {
            value[1][m] = num[m];
        }
        for (int i = 0; i < list.size(); i++) {
            Settlement settlement = list.get(i);
            ClassInfo classInfo = settlement.getClassInfo();
            value[i + 2][0] = dates[1];
            value[i + 2][1] = settlement.getProject().getCode();
            value[i + 2][2] = settlement.getOrganiaztionName();
            value[i + 2][3] = settlement.getProject().getName();
            value[i + 2][4] = Objects.nonNull(classInfo) ? com.zxy.product.train.web.util.DateUtil.dateLongToString(classInfo.getArriveDate(), DateUtil.YYYY_MM_DD) + "~" +com.zxy.product.train.web.util.DateUtil.dateLongToString(classInfo.getReturnDate(), DateUtil.YYYY_MM_DD) : CommonConstant.EMPTY;
            value[i + 2][5] = Objects.nonNull(settlement.getProject()) ? settlement.getProject().getAddressName(): CommonConstant.EMPTY; //学习地点
            value[i + 2][6] = Objects.nonNull(classInfo) ? settlement.getClassTeacher() : CommonConstant.EMPTY; //班主任
            value[i + 2][7] = settlement.getGuestroomsRooms(); //住宿地点
            value[i + 2][8] = settlement.getRestaurantRooms(); //用餐地点
            value[i + 2][9] = settlement.getClassrooms(); //教室
            value[i + 2][10] = settlement.getDayNumber();
            value[i + 2][11] = settlement.getAttendDays();
            value[i + 2][12] = settlement.getPeopleNumber();
            value[i + 2][13] = settlement.getPeopleDay();
            value[i + 2][14] = settlement.getExplain();
            Double dayNum = settlement.getTrainDayNum()!=null?settlement.getTrainDayNum():settlement.getProject().getDays()-1;
            value[i + 2][15] = dayNum ;
            Integer am = settlement.getProject().getAmount()!=null?settlement.getProject().getAmount():0;
            value[i + 2][16] = am;
            value[i + 2][17] =  dayNum * am;
            if(Objects.nonNull(map) && map.size() > 0){
                List<SettlementMemberQuantity> quantities = map.get(settlement.getClassId());
                if(!CollectionUtils.isEmpty(quantities)){
                    // 动态填充数据到 value[i + 2][17] 及后续位置
                    fillDynamicData(value, i + 2, basicNum.length, maxSize, quantities, i + 2);
                }
            }
        }
        style.setAlignment(HorizontalAlignment.CENTER); //水平居中
        style.setVerticalAlignment(VerticalAlignment.CENTER);//垂直居中
        style.setBorderRight(BorderStyle.THIN);//右边框
        downloadWriteArray(workbook, sheet, list.size() + 2, 100, value);
        String date = com.zxy.product.system.util.DateUtil.dateLongToString(System.currentTimeMillis(), com.zxy.product.system.util.DateUtil.YYYYMMDD);
        com.zxy.product.human.entity.Member nameAndFullName = memberService.getNameAndFullName(subject.getCurrentUserId());
        String content = (nameAndFullName.getFullName() + "  " + nameAndFullName.getName() + "  " + date);
        ImportExportUtil.putWaterRemarkToExcel(workbook, workbook.getSheetAt(0), null, 0, 0, 0, 0, 1, 1, 0, 0, content);
        workbook.write(out);
        out.close();

    }
    /**
     * 动态填充数据到指定位置
     */
    private static void fillDynamicData(Object[][] value, int rowIndex, int maxSize, int baseCols,
                                        List<SettlementMemberQuantity> quantities, int dataIndex) {
        // 从第17列（索引16）开始动态填充数据
        int targetCol = 18; // value[i + 2][17] 中的17列（索引16）
        for (int i = 0; i < quantities.size(); i++) {
            value[rowIndex][targetCol + 2 * i] = com.zxy.product.train.web.util.DateUtil.dateLongToString(quantities.get(i).getDate(), DateUtil.YYYY_MM_DD);
            // 结算人数数据
            value[rowIndex][targetCol + 2 * i + 1] = quantities.get(i).getQuantity();

        }
    }


    public void downloadWriteArray1(HSSFWorkbook wb, HSSFSheet sheet, int rows, int cells, Object[][] value,int num) {
        Row row[] = new HSSFRow[rows];
        Cell cell[] = new HSSFCell[cells];
        sheet.setColumnWidth(0, 18 * 256);
        sheet.setColumnWidth(1, 18 * 256);
        sheet.setColumnWidth(2, 18 * 256);
        sheet.setColumnWidth(3, 18 * 256);
        sheet.setColumnWidth(4, 8 * 256);
        sheet.setColumnWidth(5, 18 * 256);
        sheet.setColumnWidth(6, 6 * 256);
        sheet.setColumnWidth(7, 18 * 256);
        sheet.setColumnWidth(8, 18 * 256);
        sheet.setColumnWidth(9, 8 * 256);
        sheet.setColumnWidth(10, 18 * 256);
        sheet.setColumnWidth(11, 12 * 256);
        sheet.setColumnWidth(12, 18 * 256);
        sheet.setColumnWidth(12, 8 * 256);
        sheet.setColumnWidth(12, 8 * 256);
        HSSFCellStyle style1 = wb.createCellStyle();
        HSSFCellStyle style = wb.createCellStyle();
        HSSFCellStyle style2 = wb.createCellStyle();
        Font font1 = wb.createFont();
        Font fontList = wb.createFont();
        for (int i = 0; i < row.length; i++) {
            row[i] = sheet.createRow(i);
            for (int j = 0; j < cell.length; j++) {
                cell[j] = row[i].createCell(j);
                row[i].setHeightInPoints(35);
                if (i == 0) {
                    cell[j].setCellValue(convertString(value[0][j]));
                    style1.setBorderTop(BorderStyle.THIN);
                    style1.setBorderBottom(BorderStyle.THIN);//下边框
                    style1.setBorderLeft(BorderStyle.THIN);//左边框
                    style1.setBorderRight(BorderStyle.THIN);//右边框
                    style1.setAlignment(HorizontalAlignment.CENTER); //水平居中
                    style1.setVerticalAlignment(VerticalAlignment.CENTER);//垂直居中
                    style1.setFillForegroundColor(HSSFColor.YELLOW.index);
                    style1.setFillPattern(FillPatternType.SOLID_FOREGROUND);
                    fontList.setFontName("宋体");//设置字体名称
                    fontList.setFontHeightInPoints((short) 11);//设置字号
                    fontList.setBold(true);
                    style1.setFont(fontList);//选择需要用到的字体格式
                    style1.setWrapText(true); // 强制换行
                    cell[j].setCellStyle(style1);
                } else if(i<num+1){
                    row[i].setHeightInPoints(25);
                    style2.setBorderTop(BorderStyle.THIN);
                    style2.setBorderBottom(BorderStyle.THIN);//下边框
                    style2.setBorderLeft(BorderStyle.THIN);//左边框
                    style2.setBorderRight(BorderStyle.THIN);//右边框
                    style2.setAlignment(HorizontalAlignment.CENTER); //水平居中
                    style2.setVerticalAlignment(VerticalAlignment.CENTER);//垂直居中
                    style2.setFillForegroundColor(HSSFColor.TAN.index);
                    style2.setFillPattern(FillPatternType.SOLID_FOREGROUND);
                    font1.setFontName("宋体");//设置字体名称
                    font1.setFontHeightInPoints((short) 10);//设置字号
                    style2.setFont(font1);//选择需要用到的字体格式
                    style2.setWrapText(true); // 强制换行
                    cell[j].setCellValue(convertString(value[i][j]));
                    cell[j].setCellStyle(style2);
                }else{
                    row[i].setHeightInPoints(25);
                    style.setBorderTop(BorderStyle.THIN);
                    style.setBorderBottom(BorderStyle.THIN);//下边框
                    style.setBorderLeft(BorderStyle.THIN);//左边框
                    style.setBorderRight(BorderStyle.THIN);//右边框
                    style.setAlignment(HorizontalAlignment.CENTER); //水平居中
                    style.setVerticalAlignment(VerticalAlignment.CENTER);//垂直居中
                    font1.setFontName("宋体");//设置字体名称
                    font1.setFontHeightInPoints((short) 10);//设置字号
                    style.setFont(font1);//选择需要用到的字体格式
                    style.setWrapText(true); // 强制换行
                    cell[j].setCellValue(convertString(value[i][j]));
                    cell[j].setCellStyle(style);
                }
            }
        }
    }

    public static void downloadWriteArray(HSSFWorkbook wb, HSSFSheet sheet, int rows, int cells, Object[][] value) {
        Row row[] = new HSSFRow[rows];
        Cell cell[] = new HSSFCell[cells];
        sheet.setColumnWidth(0, 5 * 256);
        sheet.setColumnWidth(1, 18 * 256);
        sheet.setColumnWidth(2, 18 * 256);
        sheet.setColumnWidth(3, 40 * 256);
        sheet.setColumnWidth(4, 8 * 256);
        sheet.setColumnWidth(5, 8 * 256);
        sheet.setColumnWidth(6, 8 * 256);
        sheet.setColumnWidth(7, 8 * 256);
        sheet.setColumnWidth(8, 8 * 256);
        sheet.setColumnWidth(9, 8 * 256);
        sheet.setColumnWidth(10, 8 * 256);
        sheet.setColumnWidth(11, 8 * 256);
        sheet.setColumnWidth(12, 8 * 256);
        Font font1 = wb.createFont();
        Font fontList = wb.createFont();
        for (int i = 1; i < row.length; i++) {
            row[i] = sheet.createRow(i);
            for (int j = 0; j < cell.length; j++) {
                cell[j] = row[i].createCell(j);
                row[i].setHeightInPoints(35);
                if (i == 1) {
                    cell[j].setCellValue(convertString(value[1][j]));
                    HSSFCellStyle style1 = wb.createCellStyle();
                    style1.setBorderTop(BorderStyle.THIN);
                    style1.setBorderBottom(BorderStyle.THIN);//下边框
                    style1.setBorderLeft(BorderStyle.THIN);//左边框
                    style1.setBorderRight(BorderStyle.THIN);//右边框
                    style1.setAlignment(HorizontalAlignment.CENTER); //水平居中
                    style1.setVerticalAlignment(VerticalAlignment.CENTER);//垂直居中
                    fontList.setFontName("宋体");//设置字体名称
                    fontList.setFontHeightInPoints((short) 11);//设置字号
                    fontList.setBold(true);
                    style1.setFont(fontList);//选择需要用到的字体格式
                    style1.setWrapText(true); // 强制换行
                    cell[j].setCellStyle(style1);
                } else {
                    row[i].setHeightInPoints(25);
                    HSSFCellStyle style = wb.createCellStyle();
                    style.setBorderTop(BorderStyle.THIN);
                    style.setBorderBottom(BorderStyle.THIN);//下边框
                    style.setBorderLeft(BorderStyle.THIN);//左边框
                    style.setBorderRight(BorderStyle.THIN);//右边框
                    style.setAlignment(HorizontalAlignment.CENTER); //水平居中
                    style.setVerticalAlignment(VerticalAlignment.CENTER);//垂直居中
                    font1.setFontName("宋体");//设置字体名称
                    font1.setFontHeightInPoints((short) 10);//设置字号
                    style.setFont(font1);//选择需要用到的字体格式
                    style.setWrapText(true); // 强制换行
                    cell[j].setCellValue(convertString(value[i][j]));
                    cell[j].setCellStyle(style);
                }
            }
        }
    }

    public static String convertString(Object value) {
        if (value == null) {
            return "";
        } else {
            return value.toString();
        }
    }

    public HSSFCellStyle font(HSSFWorkbook workbook, int num, int flag) {
        HSSFCellStyle style = workbook.createCellStyle();
        HSSFFont font = workbook.createFont();
        font.setFontName("宋体");//设置字体名称
        font.setFontHeightInPoints((short) num);//设置字号
        if (flag == 0) {
            font.setBold(true);
        }
        style.setFont(font);
        return style;
    }

    @RequestMapping(value = "find-oraganization", method = RequestMethod.GET)
    @Param(name = "page", type = Integer.class, required = true)
    @Param(name = "pageSize", type = Integer.class, required = true)
    @Param(name = "organizationId", type = String.class)
    @JSON("recordCount")
    @JSON("items.(id,code,className,organization,contactPeople,contactPhone,arriveDate,returnDate,status)")
    public PagedResult<ClassInfo> findOraganization(RequestContext requestContext) throws IOException {
        return classInfoService.findOrganization(
                requestContext.get("page", Integer.class),
                requestContext.get("pageSize", Integer.class),
                requestContext.get("organizationId", String.class));

    }

    @RequestMapping(value = "find-member", method = RequestMethod.GET)
    @Param(name = "page", type = Integer.class, required = true)
    @Param(name = "pageSize", type = Integer.class, required = true)
    @Param(name = "organizationId", type = String.class)
    @JSON("recordCount")
    @JSON("items.(id,code,className,organization,contactPeople,contactPhone,arriveDate,returnDate,status)")
    public PagedResult<ClassInfo> findMember(RequestContext requestContext, Subject<Member> subject) throws IOException {
        return classInfoService.findMember(
                requestContext.get("page", Integer.class),
                requestContext.get("pageSize", Integer.class),
                requestContext.get(subject.getCurrentUser().getOrganizationId(), String.class));
    }

    @RequestMapping(value = "find-name-id", method = RequestMethod.GET)
    @Param(name = "ids", type = String.class, required = true)
    @JSON("id,className,organizationName")
    public List<ClassInfo> findClassNameAndOrganizationByClassId(RequestContext requestContext) {
        return this.classInfoService.findClassNameAndOrganizationByClassId(requestContext.get("ids", String.class));
    }

    /**
     * 学员端个人中心的我的任务
     *
     * @param requestContext
     * @param subject
     * @return
     */
    @RequestMapping(value = "/find-task-class", method = RequestMethod.GET)
    @Param(name = "className", type = String.class)
    @Param(name = "status", type = Integer.class)
    @JSON("id,className,arriveDate,returnDate,status")
    public List<ClassInfo> findMyTaskClass(RequestContext requestContext, Subject<Member> subject) {
        String memberId = subject.getCurrentUserId();
        Optional<Integer> status = requestContext.getOptional("status", Integer.class);
        Optional<String> className = requestContext.getOptional("className", String.class);
        return classInfoService.findMyTaskClass(memberId, status, className);
    }

    /**
     * 活动首页的班级查询
     *
     * @param requestContext
     * @return
     */
    @RequestMapping(value = "/find-activity-classinfo", method = RequestMethod.GET)
    @Param(name = "page", type = Integer.class, required = true)
    @Param(name = "pageSize", type = Integer.class, required = true)
    @Param(name = "name", type = String.class)
    @Param(name = "searchStatus", type = Integer.class)
    @JSON("recordCount")
    @JSON("items.(id,className,arriveDate,returnDate,status,traineeNum)")
    public PagedResult<ClassInfo> findActivityClassInfo(RequestContext requestContext) {

        Optional<String> stringOptional = requestContext.getOptional("name", String.class);

        if (stringOptional.isPresent()){
            //查询开关是否开启,开启则搜索兜底返回null
            InternalSwitch internalSwitch = intelligentSearchCache.get(InternalSwitch.KEY + KEY, () -> internalSwitchService.findByType(InternalSwitch.intelligentSearch), DateUtil.timeLeftInSeconds(System.currentTimeMillis()));
            if (!ObjectUtils.isEmpty(internalSwitch) && Objects.equals(internalSwitch.getStatus(), InternalSwitch.switchStatusNO)) {
                LOGGER.info("开关开启,返回null");
                return null;
            }
        }

        return classInfoService.findActivityClassInfo(
                requestContext.get("page", Integer.class),
                requestContext.get("pageSize", Integer.class),
                stringOptional,
                requestContext.getOptional("searchStatus", Integer.class));
    }

    /**
     * 个人中心里的我的活动中的我的班级
     *
     * @param requestContext
     * @param subject
     * @return
     */
    @RequestMapping(value = "/find-my-class-info", method = RequestMethod.GET)
    @Param(name = "page", type = Integer.class, required = true)
    @Param(name = "pageSize", type = Integer.class, required = true)
    @Param(name = "name", type = String.class)
    @Param(name = "status", type = Integer.class)
    @Param(name = "arriveDateOrderBy", type = Integer.class)
    @JSON("recordCount")
    @JSON("items.(id,className,arriveDate,returnDate,status,busList,classEvaluate,organizationId,organization,traineeType,memberId)")
    @JSON("items.busList.(id,name,endTime,startTime,classId)")
    @JSON("items.classEvaluate.(id,resourceName,endTime,type,resourceId,classId,evaluateStatus,startTime,release,isPartyCadre)")
    public PagedResult<ClassInfo> findMyClassInfo(RequestContext requestContext, Subject<com.zxy.product.system.entity.Member> subject) {
        String memberId = subject.getCurrentUserId();
        List<String> memberIds =memberService.getMembersByUserId(memberId);
        PagedResult<ClassInfo> pageList = classInfoService.findMyClass(requestContext.get("page",Integer.class),
                requestContext.get("pageSize",Integer.class),memberIds,
                requestContext.getOptional("status", Integer.class),
                requestContext.getOptional("name", String.class),
                requestContext.getOptional("arriveDateOrderBy", Integer.class));
        return pageList;
    }


    /**
     * 根据IDS查询班级信息
     * @param requestContext
     * @return
     */
    @RequestMapping(value = "front/find-by-ids", method = RequestMethod.GET)
    @Param(name = "ids", type = String.class, required = true)
    @JSON("id,className,organization,organizationId,arriveDate,returnDate,amount,bannerId," +
            "path,address,traineeNum,coverPath")
    public List<ClassInfo> findByIds(RequestContext requestContext, Subject<Member> subject) {
        String idstr = requestContext.get("ids", String.class);
        String[] idArray = idstr.split(",");
        List<String> ids = Arrays.asList(idArray);
        List<ClassInfo> pageList = classInfoService.findClassInfoByIds(ids);
        pageList.forEach(info ->{
            info.setPath(generateSecurePathCdn(info.getPath()));
            info.setCoverPath(generateSecurePathCdn(info.getCoverPath()));
        });
        return pageList;
    }
    @RequestMapping(value = "find-by-group", method = RequestMethod.GET)
    @Param(name = "page", type = Integer.class, required = true)
    @Param(name = "pageSize", type = Integer.class, required = true)
    @Param(name = "organizationId", type = String.class)
    @Param(name = "name", type = String.class)
    @JSON("recordCount")
    @JSON("items.(id,code,className,organization,contactPeople,contactPhone,arriveDate,returnDate,status,projectId,organizationId,"
            + "implementationYear,implementationMonth,traineeNum,submitNum)")
    public PagedResult<ClassInfo> findByGroup(RequestContext requestContext,Subject<Member> subject) {
        List<String> organizationIds = grantService.findGrantedOrganization(subject.getCurrentUserId(), "train/class-info", Optional.empty(), Optional.empty(),
                Optional.empty(), Optional.empty(), requestContext.getOptional("organizationId", String.class),
                Optional.empty(), Optional.empty()).stream().map(a -> a.getId()).collect(Collectors.toList());
//        List<String> organizationIds = new ArrayList<>();
//        organizationIds.add("1");
        return classInfoService.findByGroup(
                requestContext.get("page", Integer.class),
                requestContext.get("pageSize", Integer.class),
                requestContext.getOptional("name", String.class),
                requestContext.getOptional("organizationId", String.class),
                organizationIds);
    }
    @RequestMapping(value = "ids-class-filter", method = RequestMethod.GET)
    @Param(name = "ids", type = String.class, required = true)
    @JSON("id,className,organization,organizationId,arriveDate,returnDate,amount,bannerId," +
            "path,address,traineeNum")
    public List<ClassInfo> findByUserIds(RequestContext requestContext,Subject<Member> memberSubject) {
        String idstr = requestContext.get("ids", String.class);
        String[] idArray = idstr.split(",");
        List<String> ids = Arrays.asList(idArray);
        List<ClassInfo> pageList = classInfoService.findByUserIds(ids,memberSubject.getCurrentUserId());
        return pageList;
    }
    /**
     * 通过班级id返回班级名称
     * @return
     */
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @Param(name = "id", type = String.class, required = true)
    @JSON("name")
    public ClassInfo findClassIdByProjectName(RequestContext requestContext) {
        return classInfoService.findClassIdByProjectName(requestContext.get("id",String.class));
    }

    /**
     * 小banner数据过滤
     *
     * @param requestContext
     * @param memberSubject
     * @return
     */
    @RequestMapping(value = "ids-filter", method = RequestMethod.GET)
    @Param(name = "ids", required = true)
    @JSON("id,notice")
    public List<ClassInfo> searchByIdsFilter(RequestContext requestContext, Subject<Member> memberSubject) {
        String ids = requestContext.getString("ids");
        return classInfoService.filterByIds(ids.split(","), memberSubject.getCurrentUserId());
    }
    @RequestMapping(value = "/download-member", method = RequestMethod.GET)
    @Param(name = "month", type = String.class, required = true)
    @Param(name = "flag", type = Integer.class, required = true)
    public void downloadMember(RequestContext context,Subject<Member> subject) throws IOException {
        HttpServletResponse response = context.getResponse();
        String month = context.get("month").toString();
        response.reset();
        boolean state = false;
        response.setContentType("application/octet-stream;charset=utf-8");
        String names = "培训班结算数据";
        if(context.get("flag",Integer.class)!=null&&(context.get("flag",Integer.class)).equals(2)){
            state = true;
            names = "学员名单（财务）";
        }
        List<String> organizationIds = grantService.findGrantedOrganization(subject.getCurrentUserId(), "train/class-info", Optional.empty(), Optional.empty(),
                Optional.empty(), Optional.empty(),  Optional.empty(),
                Optional.empty(), Optional.empty()).stream().map(a -> a.getId()).collect(Collectors.toList());
        response.setHeader("Content-Disposition", "attachment;filename=" + new String((month + names).getBytes("gb2312"), "ISO-8859-1") + ".xls");
        List<Settlement> list =  classInfoService.findMonthMember(month,organizationIds, context.get("flag",Integer.class));
        if(state){
            List<Settlement> financeList = rectifyDeviationService.findByMonth(organizationIds,month);
            this.downloadFinance(month,response,list,financeList);
        }else {
            this.downloadDetail(month, response, list);
        }
    }
    public void downloadFinance(String month,HttpServletResponse response,List<Settlement> list,List<Settlement> financeList)throws IOException{
        HSSFWorkbook workbook = new HSSFWorkbook();
        HSSFSheet sheet = workbook.createSheet(month);
        OutputStream out = response.getOutputStream();
        HSSFCellStyle style = this.font(workbook, 18, 0);
        String[] num = {"培训班编号", "培训班", "主办部门", "培训类型", "姓名", "员工编号", "民族", "性别", "公司"
                , "部门", "职位", "手机","邮箱","月份","培训天数"};
        Object[][] value = new Object[list.size()+financeList.size() + 1][15];
        String dates[] = month.split("-");
        for (int m = 0; m < num.length; m++) {
            value[0][m] = num[m];
        }
        int j = 1;
        List<MemberConfig> memList = memberConfigService.list(Optional.of(2));
        Map map = new HashMap();
        for (Settlement t : list) {
            for(MemberConfig mc:memList){
                if(mc.getId().equals(t.getTrainee().getNation())){
                    map.put(mc.getId(),mc.getValue());
                }
            }
            t.setIndex(j);
            j++;
        }
        for (Settlement t : financeList) {
            for(MemberConfig mc:memList){
                if(mc.getId().equals(t.getTrainee().getNation())){
                    map.put(mc.getId(),mc.getValue());
                }
            }
            t.setIndex(j);
            j++;
        }
        for (int i = 0;i<financeList.size();i++){
            Settlement settlement = financeList.get(i);
            value[i + 1][0] = settlement.getProject().getCode();
            value[i + 1][1] = settlement.getProject().getName();
            value[i + 1][2] = settlement.getOrganiaztionName();
            value[i + 1][3] = settlement.getProject().getTypeId();
            value[i + 1][4] = settlement.getTrainee().getMember().getFullName();
            value[i + 1][5] = settlement.getTrainee().getMember().getName();
            value[i + 1][6] =  map.get(settlement.getTrainee().getNation());
            value[i + 1][7] = settlement.getTrainee().getSex()==0?"男":"女";
            String val = settlement.getSettleOrganizationName()!=null ?settlement.getSettleOrganizationName():settlement.getSettlementCompany();
            value[i + 1][8] = val;
            value[i + 1][9] = settlement.getOrganizationId();
            value[i + 1][10] = settlement.getRelation();
            value[i + 1][11] = settlement.getTrainee().getPhoneNumber();
            value[i + 1][12] = settlement.getTrainee().getEmail();
            value[i + 1][13] = dates[1];
            Double dayNum = 0.0;
            if(settlement.getTrainDayNum()!=null){
                dayNum = settlement.getTrainDayNum();
                if(settlement.getTrainee().getFinance()!=null && settlement.getTrainee().getFinance().equals(2)){
                    dayNum = -settlement.getTrainDayNum();
                }
            }else{
                dayNum = settlement.getProject().getDays().doubleValue();
                if(settlement.getTrainee().getFinance()!=null && settlement.getTrainee().getFinance().equals(2)){
                    dayNum = -settlement.getProject().getDays().doubleValue();
                }
            }
            value[i + 1][14] = dayNum;
        }
        int numSize = list.size() + financeList.size();
        for (int i = financeList.size(); i < numSize; i++) {
            Settlement settlement = list.get((i-financeList.size()));
            value[i + 1][0] = settlement.getProject().getCode();
            value[i + 1][1] = settlement.getProject().getName();
            value[i + 1][2] = settlement.getOrganiaztionName();
            value[i + 1][3] = settlement.getProject().getTypeId();
            value[i + 1][4] = settlement.getTrainee().getMember().getFullName();
            value[i + 1][5] = settlement.getTrainee().getMember().getName();
            value[i + 1][6] =  map.get(settlement.getTrainee().getNation());
            value[i + 1][7] = settlement.getTrainee().getSex()==0?"男":"女";
            String val = settlement.getSettleOrganizationName()!=null ?settlement.getSettleOrganizationName():settlement.getSettlementCompany();
            value[i + 1][8] = val;
            value[i + 1][9] = settlement.getOrganizationId();
            value[i + 1][10] = settlement.getRelation();
            value[i + 1][11] = settlement.getTrainee().getPhoneNumber();
            value[i + 1][12] = settlement.getTrainee().getEmail();
            value[i + 1][13] = dates[1];
            value[i + 1][14] = settlement.getTrainDayNum()!=null?settlement.getTrainDayNum():settlement.getProject().getDays();
        }
        style.setAlignment(HorizontalAlignment.CENTER); //水平居中
        style.setVerticalAlignment(VerticalAlignment.CENTER);//垂直居中
        style.setBorderRight(BorderStyle.THIN);//右边框
        this.downloadWriteArray1(workbook, sheet, list.size()+ financeList.size()+ 1, 15, value,financeList.size());
        workbook.write(out);
        out.flush();
        out.close();
    }

    public void downloadDetail(String month,HttpServletResponse response,List<Settlement> list)throws IOException{
        HSSFWorkbook workbook = new HSSFWorkbook();
        HSSFSheet sheet = workbook.createSheet(month);
        OutputStream out = response.getOutputStream();
        HSSFCellStyle style = this.font(workbook, 18, 0);
        String[] num = {"培训班编号", "培训班", "主办部门", "培训类型", "姓名", "员工编号", "民族", "性别", "公司"
                , "部门", "职位", "手机","邮箱","月份","培训天数"};
        Object[][] value = new Object[list.size() + 1][15];
        String dates[] = month.split("-");
        for (int m = 0; m < num.length; m++) {
            value[0][m] = num[m];
        }
        int j = 1;
        List<MemberConfig> memList = memberConfigService.list(Optional.of(2));
        Map map = new HashMap();
        for (Settlement t : list) {
            for(MemberConfig mc:memList){
                if(mc.getId().equals(t.getTrainee().getNation())){
                    map.put(mc.getId(),mc.getValue());
                }
            }
            t.setIndex(j);
            j++;
        }
        for (int i = 0; i < list.size(); i++) {
            Settlement settlement = list.get(i);
            value[i + 1][0] = settlement.getProject().getCode();
            value[i + 1][1] = settlement.getProject().getName();
            value[i + 1][2] = settlement.getOrganiaztionName();
            value[i + 1][3] = settlement.getProject().getTypeId();
            value[i + 1][4] = settlement.getTrainee().getMember().getFullName();
            value[i + 1][5] = settlement.getTrainee().getMember().getName();
            value[i + 1][6] =  map.get(settlement.getTrainee().getNation());
            value[i + 1][7] = settlement.getTrainee().getSex()==0?"男":"女";
            String val = settlement.getSettleOrganizationName()!=null ?settlement.getSettleOrganizationName():settlement.getSettlementCompany();
            value[i + 1][8] = val;
            value[i + 1][9] = settlement.getOrganizationId();
            value[i + 1][10] = settlement.getRelation();
            value[i + 1][11] = settlement.getTrainee().getPhoneNumber();
            value[i + 1][12] = settlement.getTrainee().getEmail();
            value[i + 1][13] = dates[1];
            value[i + 1][14] = settlement.getTrainDayNum()!=null?settlement.getTrainDayNum():settlement.getProject().getDays();
        }
        style.setAlignment(HorizontalAlignment.CENTER); //水平居中
        style.setVerticalAlignment(VerticalAlignment.CENTER);//垂直居中
        style.setBorderRight(BorderStyle.THIN);//右边框
        this.downloadWriteArray1(workbook, sheet, list.size() + 1, 15, value,0);
        workbook.write(out);
        out.flush();
        out.close();
    }

    /**
     * 培训班列表
     */
    @RequestMapping(value = "/class-experience", method = RequestMethod.GET)
    @Param(name = "memberId", required = true)
    @JSON("id,arriveDate,returnDate,organization")
    @JSON("project.(id,name,year,month,days)")
    @Permitted
    public List<ClassInfo> classExperience(RequestContext requestContext) {
        return classInfoService.classExperience(
                requestContext.getString("memberId"));
    }

    /**
     * 获取问卷分数
     */
    @RequestMapping(value = "/get-questionnaire-scores", method = RequestMethod.GET)
    @Param(name = "memberId", required = true)
    @Param(name = "classId", required = true)
    @JSON("score,rId")
    @Permitted
    public ResearchQuestionary getQuestionnaireScores(RequestContext requestContext){
        return classInfoService.getQuestionnaireScores(requestContext.getString("memberId"), requestContext.getString("classId"));
    }


    /**
     * 查询详情
     */
    @RequestMapping(value = "/get-class-and-member-detail", method = RequestMethod.GET)
    @Param(name = "memberId", required = true)
    @Param(name = "classId", required = true)
    @JSON("id,arriveDate,returnDate,organization,address,oldPositionName,newPositionName")
    @Permitted
    public ClassInfo getClassAndMemberDetail(RequestContext requestContext) {
        String memberId = requestContext.getString("memberId");
        ClassInfo classInfo = classInfoService.getClassAndMemberDetail(
                memberId,
                requestContext.getString("classId")
        );
        String positionName = memberPositionInnerService.getByMemberId(memberId, classInfo.getArriveDate(), classInfo.getReturnDate());
        if (!org.springframework.util.StringUtils.isEmpty(positionName)) {
            classInfo.setOldPositionName(positionName);
        }
        com.zxy.product.human.entity.Member member = memberService.get(memberId);
        Optional<Position> optional = positionService.getOptional(member.getMajorPositionId());
        optional.ifPresent(position -> classInfo.setNewPositionName(position.getName()));
        return classInfo;
    }
}
