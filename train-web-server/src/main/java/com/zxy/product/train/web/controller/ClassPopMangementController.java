package com.zxy.product.train.web.controller;

import com.zxy.common.restful.RequestContext;
import com.zxy.common.restful.annotation.Param;
import com.zxy.common.restful.json.JSON;
import com.zxy.common.restful.security.Subject;
import com.zxy.product.train.api.ClassPopMangementService;
import com.zxy.product.train.entity.ClassPopMangement;
import com.zxy.product.train.entity.Member;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.Optional;

/**
 * @Auther: xxh
 * @Date: 2025/8/28 - 08 - 28 - 18:16
 * @Description: com.zxy.product.train.web.controller
 * @version: 1.0
 */
@Controller
@RequestMapping("/class-pop")
public class ClassPopMangementController {

    private ClassPopMangementService classPopMangementService;

    @Autowired
    public void setClassPopMangementService(ClassPopMangementService classPopMangementService) {
        this.classPopMangementService = classPopMangementService;
    }


    private ClassPopMangement setData(String memberId, String classId, Optional<Integer> popFlag, Optional<Integer> type) {
        ClassPopMangement classPopMangement = new ClassPopMangement();
        classPopMangement.forInsert();
        classPopMangement.setMemberId(memberId);
        classPopMangement.setClassId(classId);
        classPopMangement.setFlag(popFlag.orElse(ClassPopMangement.POP_FLAG_CANCEL));
        type.ifPresent(t->classPopMangement.setType(t));
        return classPopMangement;
    }

    @RequestMapping(method = RequestMethod.POST)
    @Param(name = "classId", type = String.class, required = true)
    @Param(name = "popFlag", type = Integer.class)
    @Param(name = "type", type = Integer.class)
    @JSON("*.*")
    public ClassPopMangement save(RequestContext requestContext, Subject<Member> subject){
        ClassPopMangement classPopMangement = setData(subject.getCurrentUserId(),
                requestContext.getString("classId"),
                requestContext.getOptionalInteger("popFlag"),
                requestContext.getOptionalInteger("type"));
        return classPopMangementService.add(classPopMangement);
    }


    @RequestMapping(method = RequestMethod.GET)
    @Param(name = "classId", type = String.class, required = true)
    @JSON("*.*")
    public ClassPopMangement find(RequestContext requestContext, Subject<Member> subject){
        return classPopMangementService.getFlag(requestContext.getString("classId"), subject.getCurrentUserId()).orElse(new ClassPopMangement());
    }

}
