package com.zxy.product.train.web.controller;

import com.zxy.common.base.exception.UnprocessableException;
import com.zxy.common.base.helper.PagedResult;
import com.zxy.common.cache.Cache;
import com.zxy.common.cache.CacheService;
import com.zxy.common.office.excel.export.Writer;
import com.zxy.common.office.excel.export.support.ExcelWriter;
import com.zxy.common.restful.RequestContext;
import com.zxy.common.restful.annotation.Param;
import com.zxy.common.restful.annotation.Params;
import com.zxy.common.restful.audit.Audit;
import com.zxy.common.restful.json.JSON;
import com.zxy.common.restful.multipart.AttachmentResolver;
import com.zxy.common.restful.security.Permitted;
import com.zxy.common.restful.security.Subject;
import com.zxy.common.restful.util.Encrypt;
import com.zxy.product.human.api.FileService;
import com.zxy.product.human.api.MemberService;
import com.zxy.product.human.entity.Member;
import com.zxy.product.system.api.operation.MessageSendService;
import com.zxy.product.system.api.permission.GrantService;
import com.zxy.product.system.content.MessageConstant;
import com.zxy.product.train.api.ClassInfoService;
import com.zxy.product.train.api.GrantDetailService;
import com.zxy.product.train.api.ProjectOccupyService;
import com.zxy.product.train.api.ProjectService;
import com.zxy.product.train.content.ErrorCode;
import com.zxy.product.train.entity.ClassInfo;
import com.zxy.product.train.entity.Project;
import com.zxy.product.train.util.StringUtils;
import com.zxy.product.train.web.controller.xlsx.BaseImportController;
import com.zxy.product.train.web.util.DateUtil;
import com.zxy.product.train.web.util.DesensitizationUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 17/02/09
 */
@Controller
@RequestMapping("/project")
public class ProjectController extends BaseImportController{
	public static final Logger logger = LoggerFactory.getLogger(ProjectController.class);
	private ProjectService projectService;

	private MemberService memberService;

	private MessageSendService messageSendService;

	private GrantDetailService grantDetailService;

	private ProjectOccupyService occupyService;

	private GrantService grantService;

	private Cache cache;
	private Cache offlineCourseCache;
	private CacheService cacheService;
	private Cache infoCache;
	private Cache statisfactionCache;
	private Cache themeCache;

	@Value("${aes.key:d8cg8gVakEq9Agup}")
	private String aesKey;
	private static final String CLASS_BASIC_INFO_FOR_SIGN_UP = "class-basic-info-for-sign-up";
	private static final String DEGREE_OF_SATISFACTION = "satisfaction-degree-question";
	private static final String THEME_CACHE_KEY = "offline-theme-list-key";

	private ClassInfoService classService;

	@Autowired
	public void setOccupyService(ProjectOccupyService occupyService) {
		this.occupyService = occupyService;
	}

	@Autowired
	public void setMessageSendService(MessageSendService messageSendService) {
		this.messageSendService = messageSendService;
	}

	@Override
	@Autowired
	public void setAttachmentResolver(AttachmentResolver attachmentResolver) {
		this.attachmentResolver = attachmentResolver;
	}

	@Autowired
	public void setMemberService(MemberService memberService) {
		this.memberService = memberService;
	}

	@Override
	@Autowired
	public void setFileService(FileService fileService) {
		this.fileService = fileService;
	}

	@Autowired
	public void setProjectService(ProjectService projectService) {
		this.projectService = projectService;
	}

	@Autowired
	public void setGrantDetailService(GrantDetailService grantDetailService) {
		this.grantDetailService = grantDetailService;
	}

	@Autowired
	public void setGrantService(GrantService grantService) {
		this.grantService = grantService;
	}

	@Autowired

	public void setCacheService(CacheService cacheService) {
		this.cacheService = cacheService;
		this.cache = cacheService.create("train");
		this.offlineCourseCache = cacheService.create("offline-course");
		this.statisfactionCache = cacheService.create(DEGREE_OF_SATISFACTION);
		this.infoCache = cacheService.create("info");
		this.themeCache = cacheService.create(THEME_CACHE_KEY);
	}

	@Autowired
	public void setClassService(ClassInfoService classService) {
		this.classService = classService;
	}

	/**
	 * 需求方培训计划分页列表
	 */
	@RequestMapping(value = "/frontend", method = RequestMethod.GET)
	@Param(name = "page", type = Integer.class, required = true)
	@Param(name = "pageSize", type = Integer.class, required = true)
	@Param(name = "MIScode", type = String.class)
	@Param(name = "className", type = String.class)
	@Param(name = "startTime", type = String.class)
	@JSON("recordCount")
	@JSON("items.(id,code,name,amount,days,teacherName,year,month,status)")
	@JSON("items.classInfo.(arriveDate,returnDate)")
	@Permitted
	public PagedResult<Project> find(RequestContext requestContext, Subject<Member> subject) {
		Optional<String> OpStartTime = requestContext.getOptional("startTime", String.class);
		Integer reachYear = null;
		Integer reachMonth = null;
		if (OpStartTime.isPresent()) {
			String startTime = OpStartTime.get();
			reachYear = Integer.parseInt(startTime.substring(0, 4));
			reachMonth = Integer.parseInt(startTime.substring(5));
		}
		return projectService.find(requestContext.get("page", Integer.class),
				requestContext.get("pageSize", Integer.class), requestContext.getOptional("MIScode", String.class),
				requestContext.getOptional("className", String.class), Optional.ofNullable(null),
				Optional.ofNullable(null), Optional.ofNullable(null), Optional.ofNullable(null),
				Optional.ofNullable(null), Optional.ofNullable(reachYear), Optional.ofNullable(reachMonth),
				Optional.ofNullable(null), Optional.ofNullable(subject.getCurrentUserId()));
	}

	/**
	 * 需求方培训计划分页列表（后台）
	 */
	@RequestMapping(value = "/background", method = RequestMethod.GET)
	@Param(name = "page", type = Integer.class, required = true)
	@Param(name = "pageSize", type = Integer.class, required = true)
	@Param(name = "className", type = String.class)
	@Param(name = "organizationId", type = String.class)
	@Param(name = "code", type = String.class)
	@Param(name = "year", type = Integer.class)
	@Param(name = "month", type = Integer.class)
	@Param(name = "implementationYear", type = Integer.class)
	@Param(name = "implementationMonth", type = Integer.class)
	@Param(name = "approvalStatus", type = Integer.class)
	@Param(name = "classStatus", type = Integer.class)
	@JSON("recordCount")
	@JSON("items.(id,code,name,amount,days,teacherName,year,month,status,contactMemberId,contactMemberName,organizationName,classStatus,auditStatus,studentStatus,organizationId, reservationMember,reservationMemberName, reservationTime)")
	@JSON("items.classInfo.(arriveDate,returnDate,status,notice)")
	@JSON("items.projectApproval.(createMember, createTime, approvalMember,approvalTime,approvalFullName,creataFullName)")
	@Permitted
	public PagedResult<Project> findGround(RequestContext requestContext, Subject<Member> subject) {
		Optional<Integer> classStatus = requestContext.getOptional("classStatus", Integer.class);
		Optional<Integer> implementationYear = requestContext.getOptional("implementationYear", Integer.class);
		Optional<Integer> implementationMonth = requestContext.getOptional("implementationMonth", Integer.class);
		Optional<Integer> month = requestContext.getOptional("month", Integer.class);
		Optional<Integer> year = requestContext.getOptional("year", Integer.class);
		if (classStatus.isPresent()) {
			if (classStatus.get().equals(0)) {
				classStatus = classStatus.ofNullable(null);
			}
		}
		Optional<Integer> approvalStatus = requestContext.getOptional("approvalStatus", Integer.class);
		if (approvalStatus.isPresent()) {
			if (approvalStatus.get().equals(0)) {
				approvalStatus = approvalStatus.ofNullable(null);
			}
		}

		if (implementationYear.isPresent() && implementationYear.get() == 0) {
			implementationYear = implementationYear.ofNullable(null);
		}

		if (implementationMonth.isPresent() && implementationMonth.get() == 0) {
			implementationMonth = implementationMonth.ofNullable(null);
		}

		if (year.isPresent() && year.get() == 0) {
			year = year.ofNullable(null);
		}

		if (month.isPresent() && month.get() == 0) {
			month = month.ofNullable(null);
		}
		Map<String, Set<String>> grantOrganizationPathMap =
				grantService.findGrantOrganizationByUri(subject.getCurrentUserId(), "train/project", subject.getRootOrganizationId());

		return projectService.findGround(requestContext.get("page", Integer.class),
				requestContext.get("pageSize", Integer.class), requestContext.getOptional("code", String.class),
				requestContext.getOptional("className", String.class), Optional.ofNullable(null),
				year, month,
				approvalStatus, requestContext.getOptional("organizationId", String.class),
				implementationYear,
				implementationMonth, classStatus,
				grantOrganizationPathMap);
	}

	/**
	 * 根据id查询单条培训计划
	 *
	 * @param context
	 * @return
	 */
	@RequestMapping(value = "/{id}", method = RequestMethod.GET)
	@Param(name = "id", type = String.class, required = true)
	@Param(name = "shield", type = Boolean.class )
	@JSON("id, name, code, days, year, month, object, contactPhone,special, createTime, createMember, amount, address, status, contactEmail, object, typeId,projectMonth, studentStatus,cost,classStatus,projectType,projectLevel,target,surveyType,isPartyCadre,aesContactPhone,aesContactEmail")
	@JSON("member.(id, name, fullName)")
	@JSON("organization.(id, name)")
	@JSON("projectApproval.(id, suggestion)")
	@JSON("classInfo.(*)")
	@Permitted
	public Project get(RequestContext context) {
		Project p = projectService.get(context.getString("id"));
		Optional<Boolean> shield = context.getOptionalBoolean("shield");
		// if (shield.orElse(false))  和前端确认后并不知道此行解决的是什么冲突所以注释了
		p.setAesContactPhone(getAesEncryptParam(Optional.ofNullable(p.getContactPhone())));
		p.setContactPhone(DesensitizationUtil.desensitizeMobile(Optional.ofNullable(p.getContactPhone())));
		p.setAesContactEmail(getAesEncryptParam(Optional.ofNullable(p.getContactEmail())));
		p.setContactEmail(DesensitizationUtil.desensitizeEmail(Optional.ofNullable(p.getContactEmail())));
		return p;
	}

	/**
	 * 培训计划新增
	 *
	 * @param requestContext
	 * @param subject
	 * @return
	 */
	@RequestMapping(method = RequestMethod.POST)
	@Param(name = "name", type = String.class, required = true)
	@Param(name = "code", type = String.class, required = true)
	@Param(name = "object", type = String.class, required = true)
	@Param(name = "organizationId", type = String.class)
	@Param(name = "memberId", type = String.class, required = true)
	@Param(name = "contactPhone", type = String.class, required = true)
	@Param(name = "contactEmail", type = String.class)
	@Param(name = "month", type = String.class, required = true)
	@Param(name = "amount", type = Integer.class, required = true)
	@Param(name = "days", type = Integer.class, required = true)
	@Param(name = "address", type = String.class, required = true)
	@Param(name = "typeId", type = String.class)
	@Param(name = "cost", type = String.class)
	@JSON("id,name,code,object,organizationId,contactMemberId,cost"
			+ "contactPhone,contactEmail,year,month,amount,days,address,typeId")
	@Audit(module = "活动管理", subModule = "培训管理－培训计划", action = Audit.Action.INSERT, fisrtAction = "添加", desc = "添加培训计划{0}", params = {"name"})
	@Permitted
	public int insert(RequestContext requestContext, Subject<Member> subject) {
		String memberId = requestContext.get("memberId", String.class);
		String contactPhone = requestContext.get("contactPhone", String.class);
		String projectName = requestContext.get("name", String.class);
		String startDate = requestContext.get("month", String.class);

		Optional<String> projectCode = requestContext.getOptional("code", String.class);
		Optional<String> contactEmail = requestContext.getOptional("contactEmail", String.class);

		// 提取并解析年和月
		int year = Integer.parseInt(startDate.substring(0, 4));
		int month = Integer.parseInt(startDate.substring(5));

		// 检查编码是否重复
		projectCode.ifPresent(code -> {
			if (projectService.checkCode(code, Optional.empty()) != 0) {
				throw new UnprocessableException(ErrorCode.ProjectCodeRepeat);
			}
		});

		Project projectData = projectService.insert(
				projectName,
				projectCode,
				requestContext.get("object", String.class),
				requestContext.get("organizationId", String.class),
				memberId,
				contactPhone,
				contactEmail,
				year,
				month,
				requestContext.get("amount", Integer.class),
				requestContext.get("days", Integer.class),
				requestContext.get("address", String.class),
				requestContext.get("typeId", String.class),
				requestContext.get("cost", String.class),
				Optional.of(subject.getCurrentUserId())
		);

		// 发送消息通知
		if (projectData != null && projectData.getId() != null) {
			String[] memberIds = { memberId };
			String[] notificationParams = { projectName };
			messageSendService.sendTep(
					subject.getCurrentUserId(),
					memberIds,
					memberIds,
					MessageConstant.CLASS_PLAN_EXECUTE,
					Optional.of(projectData.getId()),
					Optional.empty(),
					Optional.of(notificationParams)
			);
		}

		return 1;
	}

	/**
	 * 根据ID更新
	 *
	 * @param name
	 *            班级名称
	 * @param code
	 *            MIS编号
	 * @param contactEmail
	 *            邮箱
	 * @param contactMemberId
	 *            需求单位联系人
	 * @param organizationId
	 *            需求单位
	 * @param contactPhone
	 *            联系电话
	 * @param year
	 *            计划年份
	 * @param month
	 *            计划月份
	 * @param amount
	 *            计划人数
	 * @param object
	 *            培训对象
	 * @param days
	 *            培训天数
	 * @param address
	 *            培训地点
	 * @param typeId
	 *            培训类型
	 * @return
	 */
	@RequestMapping(method = RequestMethod.PUT, value = "/{id}")
	@Param(name = "id", type = String.class, required = true)
	@Param(name = "name", type = String.class, required = true)
	@Param(name = "code", type = String.class, required = true)
	@Param(name = "aesContactEmail")
	@Param(name = "memberId", type = String.class, required = true)
	@Param(name = "organizationId", type = String.class)
	@Param(name = "aesContactPhone", required = true)
	@Param(name = "month", type = String.class, required = true)
	@Param(name = "amount", type = Integer.class, required = true)
	@Param(name = "object", type = String.class, required = true)
	@Param(name = "days", type = Integer.class, required = true)
	@Param(name = "address", type = String.class, required = true)
	@Param(name = "typeId", type = String.class)
	@Param(name = "flag", type = Integer.class)
	@Param(name = "cost", type = String.class)
	@Param(name = "beforeAmount", type = Integer.class, required = true)
	@JSON("id,name,code,contactEmail,contactMemberId,organizationId,contactPhone,year,month,amount,object,days,address,typeId,cost,aesContactPhone,aesContactEmail")
	@Audit(module = "活动管理", subModule = "培训管理－培训计划", action = Audit.Action.UPDATE, fisrtAction = "编辑", desc = "编辑培训计划{0}", params = {"name"})
	@Permitted
	public Project update(RequestContext requestContext, Subject<Member> subject) {
		Optional<String> OpStartTime = requestContext.getOptional("month", String.class);
		Integer reachYear = null;
		Integer reachMonth = null;
		if (OpStartTime.isPresent()) {
			String startTime = OpStartTime.get();
			reachYear = Integer.parseInt(startTime.substring(0, 4));
			reachMonth = Integer.parseInt(startTime.substring(5));
		}
		String id = requestContext.getString("id");
		Optional<String> code = requestContext.getOptional("code", String.class);
		if (code.isPresent()) {
			if (projectService.checkCode(code.get(), Optional.of(id)) != 0)
				throw new UnprocessableException(ErrorCode.ProjectCodeRepeat);
		}
		Optional<String> address = requestContext.getOptional("address", String.class);
		Optional<String> name = requestContext.getOptional("name", String.class);
		if (address.isPresent() || name.isPresent()) {
			ClassInfo classInfo = classService.findClassIdByProjectId(id);
			if (classInfo != null) {
				cache.clear(CLASS_BASIC_INFO_FOR_SIGN_UP + "#" + classInfo.getId());
				infoCache.clear(ClassInfo.CLASS_DETAIL_PAGE_INFO_KEY + classInfo.getId());
			}
		}
		if(requestContext.getOptional("flag", Integer.class).isPresent()&&requestContext.getOptional("flag", Integer.class).get().equals(1)){
			return projectService.updateNameAndCode(id,requestContext.getOptional("name", String.class).get(),requestContext.getOptional("code", String.class));
		}
		String contactPhone = getDecryptParam(requestContext.getOptionalString("aesContactPhone"));
		String contactEmail = getDecryptParam(requestContext.getOptional("aesContactEmail", String.class));
		Project project = projectService.updateOne(id, requestContext.getOptional("name", String.class),
				requestContext.getOptional("code", String.class),
				Optional.ofNullable(contactEmail),
				requestContext.getOptional("memberId", String.class),
				requestContext.getOptional("organizationId", String.class),
				Optional.ofNullable(contactPhone), Optional.of(reachYear),
				Optional.of(reachMonth), requestContext.getOptional("amount", Integer.class),
				requestContext.getOptional("object", String.class), requestContext.getOptional("days", Integer.class),
				requestContext.getOptional("address", String.class), requestContext.getOptional("typeId", String.class),
				Optional.empty(), Optional.empty(), Optional.empty(), Optional.empty(), Optional.empty(),
				Optional.empty(),requestContext.getOptional("cost", String.class),
				requestContext.getOptional("beforeAmount", Integer.class),
				Optional.of(subject.getCurrentUserId()));
		project.setAesContactPhone(getAesEncryptParam(Optional.ofNullable(project.getContactPhone())));
		project.setContactPhone(DesensitizationUtil.desensitizeMobile(Optional.ofNullable(project.getContactPhone())));
		project.setAesContactEmail(getAesEncryptParam(Optional.ofNullable(project.getContactEmail())));
		project.setContactEmail(DesensitizationUtil.desensitizeEmail(Optional.ofNullable(project.getContactEmail())));
		return project;
	}

	/**
	 * 根据ID预定/审核
	 *
	 * @param name
	 *            班级名称
	 * @param contactMemberId
	 *            需求单位联系人
	 * @param organizationId
	 *            需求单位
	 * @param contactPhone
	 *            联系电话
	 * @param days
	 *            培训天数
	 * @param amount
	 *            计划人数
	 * @param arriveDate
	 *            报到日
	 * @param returnDate
	 *            返程日
	 * @param isOutside
	 *            是否外部举办
	 * @param level
	 *            培训级别
	 * @param address
	 *            学习地点
	 * @param studentType
	 *            人员类型
	 * @param simpleType
	 *            补贴类型
	 * @param status
	 *            审核状态
	 * @param suggestion
	 *            审核意见
	 * @return
	 */
	@RequestMapping(method = RequestMethod.POST, value = "/approval/{id}")
	@Param(name = "id", type = String.class, required = true)
	@Param(name = "arriveDate", type = String.class, required = true)
	@Param(name = "returnDate", type = String.class, required = true)
	@Param(name = "isOutside", type = Integer.class, required = true)
	@Param(name = "level", type = String.class)
	@Param(name = "address", type = String.class, required = true)
	@Param(name = "status", type = Integer.class, required = true)
	@Param(name = "suggestion", type = String.class)
	@Param(name = "message", type = String.class)
	@Param(name = "target", type = String.class)
	@Param(name = "surveyType", type = String.class)
	@Param(name = "stu", type = String.class)
	@Param(name = "resourceStatus", type = Integer.class)
	@Param(name="isPartyCadre",type=Integer.class,required = true)
	@JSON("id,name,contactMemberId,organizationId,contactPhone"
			+ ",days,amount,arriveDate,returnDate,isOutside,level,address" + "studentType,simpleType,status,suggestion,cost")
	@Audit(module = "活动管理", subModule = "培训管理－培训计划", action = Audit.Action.UPDATE, fisrtAction = "审核",desc = "操作审核于培训计划{0}", ids = {"id"}, jsons = {"name"}, keys = {"project"})
	@Permitted
	public Project approval(RequestContext requestContext, Subject<Member> subject) {
		/*com.zxy.product.human.entity.Organization organization = memberService
				.getCompanyOrganizationWithLevel2ByMemberId(subject.getCurrentUserId());*/
		Optional<String> suggestion = requestContext.getOptional("suggestion", String.class);
		String classSuggestion = suggestion.orElse("无");
		ClassInfo info = classService.findClassIdByProjectId(requestContext.get("id", String.class));
		if (info != null ) {
			cache.clear(CLASS_BASIC_INFO_FOR_SIGN_UP + "#" + info.getId());
			//报道日更新课程变更，需要清除线下课程和满意度的缓存
//            themeCache.clear(THEME_CACHE_KEY + info.getId());
			statisfactionCache.clear(DEGREE_OF_SATISFACTION + info.getId());
			infoCache.clear(ClassInfo.CLASS_DETAIL_PAGE_INFO_KEY + info.getId());
			themeCache.clear(THEME_CACHE_KEY + info.getId());
		}

		Project p = projectService.approval(requestContext.get("id", String.class),
				StringUtils.dateString2OptionalLong(requestContext.get("arriveDate").toString()),
				StringUtils.dateString2OptionalLong(requestContext.get("returnDate").toString()),
				requestContext.get("isOutside", Integer.class),
				requestContext.get("address", String.class),
				requestContext.get("status", Integer.class), requestContext.getOptional("suggestion", String.class),
				requestContext.getOptional("surveyType", String.class),
				requestContext.getOptional("target", String.class),
				requestContext.get("stu", String.class),
				Optional.of(subject.getCurrentUserId()),
				requestContext.getOptional("resourceStatus", Integer.class),
				requestContext.getInteger("isPartyCadre")
		);
		Optional<String> optional = requestContext.getOptional("message", String.class);
		if (optional.isPresent()) {
			Integer stu = p.getStatus();
			String className = p.getName();
			String[] params1 = { className };
			String[] params2 = { className, classSuggestion };
			String[] mids = { p.getContactMemberId() };
			String[] tepids = { p.getContactPhone() };
			if (stu == 3 && stu != null) {
				messageSendService.sendTep(subject.getCurrentUserId(),mids,tepids,MessageConstant.CLASS_RESERVE_AUDIT_PASS,Optional.ofNullable(p.getId()),
						Optional.empty(),Optional.of(params1));

			} else {
				messageSendService.sendTep(subject.getCurrentUserId(),mids,tepids,MessageConstant.CLASS_RESERVE_AUDIT_REFUSE,Optional.ofNullable(p.getId()),
						Optional.empty(),Optional.of(params2));
			}
		}
        /*if (p != null && me.isPresent()) {
            messageSendService.send(subject.getCurrentUserId(), mids, com.zxy.product.train.content.MessageConstant.CLASS_SMS_AUDIT,
                    Optional.of(p.getId()), Optional.empty(), Optional.of(params2));
        }*/
		return p;
	}

	/**
	 * 学员端-班级-预定时间
	 *
	 * @param requestContext
	 * @return
	 */
	@RequestMapping(method = RequestMethod.PUT, value = "book/{id}")
	@Param(name = "id", type = String.class, required = true)
	@Param(name = "contactPhone", type = String.class)
	@Param(name = "contactEmail", type = String.class)
	@Param(name = "isOutside", type = Integer.class)
	@Param(name = "surveyType", type = String.class)
	@Param(name = "arriveDate", type = String.class, required = true)
	@Param(name = "returnDate", type = String.class, required = true)
	@Param(name = "target", type = String.class)
	@Param(name = "object", type = String.class)
	@Param(name = "isSubmit", type = Integer.class)
	@Param(name = "resourceStatus", type = Integer.class)
	@JSON("id,name,code,contactEmail,contactMemberId,organizationId,contactPhone,year,month,amount,object,days,address,typeId")
	@Audit(module = "活动管理", subModule = "培训管理－培训计划", action = Audit.Action.UPDATE, fisrtAction = "预定", desc = "操作预定班级时间于班级{0}", ids = {"id"}, jsons = {"name"}, keys = {"project"})
	@Permitted
	public Project booking(RequestContext requestContext, Subject<Member> subject) {
		//防止重复提交
		Integer isSubmit = requestContext.get("isSubmit", Integer.class);
		Optional<Integer> isOutside = requestContext.getOptional("isOutside",Integer.class);
		if(isSubmit != null && isSubmit == 1) {
			Integer bookCache = cache.get("book_" + requestContext.get("id", String.class), Integer.class);
			if(bookCache == null) {
				cache.set("book_" + requestContext.get("id", String.class), isSubmit, 60);
			}else{
				return new Project();
			}
		}
		Project project = projectService.get(requestContext.get("id", String.class));
		Optional<Integer> statusOptional = null;
		if (project != null) {
			if (!project.getStatus().equals(2) && !project.getStatus().equals(3)) {
				statusOptional = Optional.of(Project.STATUS_APPROVAL);
			} else {
				statusOptional = Optional.empty();
			}
		}
		Project update = projectService.update(requestContext.get("id", String.class), Optional.empty(), Optional.empty(),
				requestContext.getOptional("contactEmail", String.class), Optional.empty(), Optional.empty(),
				requestContext.getOptional("contactPhone", String.class), Optional.empty(), Optional.empty(),
				Optional.empty(), requestContext.getOptional("object", String.class), Optional.empty(),
				Optional.empty(), Optional.empty(), requestContext.getOptional("isOutside", Integer.class),
				requestContext.getOptional("surveyType", String.class),
				StringUtils.dateString2OptionalLong(requestContext.getOptional("arriveDate", String.class)),
				StringUtils.dateString2OptionalLong(requestContext.getOptional("returnDate", String.class)),
				requestContext.getOptional("target", String.class), statusOptional,Optional.empty(),Optional.empty(),Optional.ofNullable(subject.getCurrentUserId())
				,requestContext.getOptional("resourceStatus", Integer.class));
		if (isSubmit == 1) {
			if(isOutside.isPresent() && isOutside.get() == 0){
				//预定资源时修改占用资源
				occupyService.updateOccupyByDate(DateUtil.dateStringYYYYMMDD2Long(requestContext.getOptional("arriveDate", String.class).get()),
						DateUtil.dateStringYYYYMMDD2Long(requestContext.getOptional("returnDate", String.class).get()),
						project.getAmount(),
						project.getDays()-1);
				logger.error("#OCCUPY#预定资源ID：" + project.getId() +",报道日：" + requestContext.getOptional("arriveDate", String.class).get()
						+ ",返程日：" + requestContext.getOptional("returnDate", String.class).get()
						+ ",人数：" + project.getAmount() + ",天数：" + project.getDays());
			}
			//需求方联系人信息
			Member member = memberService.get(update.getContactMemberId());
			//资源管理员memberId集合
			String[] memberIds = grantService.findGrantedMemberByUriAndOrganization("train/project", update.getOrganizationId());
			//String[] memberIds = grantDetailService.findManageMember("train/project", update.getId());
			String[] params = {member.getFullName(), update.getName()};
			messageSendService.send(update.getContactMemberId(), memberIds, MessageConstant.CLASS_RESERVE_APPLY, Optional.empty(), Optional.empty(), Optional.of(params));
		}
		cache.clear("book_" + requestContext.get("id", String.class));
		return update;// 待审核
	}

	/**
	 * 根据ID删除单条培训计划
	 *
	 * @param requestContext
	 * @return
	 */
	@RequestMapping(method = RequestMethod.DELETE, value = "/{id}")
	@Param(name = "id", type = String.class, required = true)
	@JSON("*.*")
	@Permitted
	public int delete(RequestContext requestContext) {
		return projectService.delete(requestContext.getString("id"));
	}

	/**
	 * 根据月份查询当月举办的培训班
	 *
	 * @param context
	 * @return
	 */
	@RequestMapping(method = RequestMethod.GET, value = "findByDate")
	@Param(name = "month", type = String.class, required = true)
	@JSON("name,amount,days")
	@JSON("classInfo.(arriveDate,isOutside)")
	@Permitted
	public List<Project> findByDate(RequestContext context) {
		String[] date = context.get("month", String.class).split("-");
		return projectService.findProjectByDate(Integer.parseInt(date[0]), Integer.parseInt(date[1]));
	}

	/**
	 * 按月导出培训计划
	 *
	 * @param context
	 * @param subject
	 * @throws IOException
	 */
	@RequestMapping(value = "/download", method = RequestMethod.GET)
	@Param(name = "arriveDate", type = String.class)
	@Audit(module = "活动管理", subModule = "培训管理－班级管理", action = Audit.Action.EXPORT, fisrtAction = "导出培训计划", desc = "导出培训计划")
	@Permitted
	public void download(RequestContext context, Subject<Member> subject) throws IOException {
		Optional<String> OpStartTime = context.getOptional("arriveDate", String.class);
		if (OpStartTime.isPresent()) {
			String name ="培训计划" + OpStartTime.get() + "月份列表导出";
			HttpServletResponse response = context.getResponse();
			response.setContentType("application/octet-stream;charset=utf-8");
			response.setHeader("Content-Disposition",
					"attachment;filename=" + new String(name.getBytes("gb2312"), "ISO-8859-1") + ".xlsx");
			List<Project> list = this.findDown(context, subject);
			list.sort((lec1, lec2) -> lec1.getClassInfo().getArriveDate().compareTo(lec2.getClassInfo().getArriveDate()));
			Writer writer = new ExcelWriter();
			writer.sheet(name, list)
					.indexColumn(Optional.empty())
					.field("MIS编号", Project::getCode)
					.field("主办部门", Project -> Project.getOrganizationName())
					.field("班名", Project::getName)
					.field("联系人", Project -> Project.getContactMemberName())
					.field("电话", Project -> Project.getContactPhone())
					.field("邮箱", Project -> Project.getContactEmail())
					.field("预定人", Project ->  Project.getReservationMemberName())
					.field("预定时间", Project -> dataFormat(Optional.ofNullable(Project.getReservationTime())))
					.field("审核人", Project -> Objects.nonNull(Project.getProjectApproval()) ? Project.getProjectApproval().getApprovalFullName() : org.apache.commons.lang3.StringUtils.EMPTY)
					.field("审核时间", Project -> Objects.nonNull(Project.getProjectApproval()) ? dataFormat(Optional.ofNullable(Project.getProjectApproval().getApprovalTime())) : org.apache.commons.lang3.StringUtils.EMPTY)
					.field("对象", Project -> Project.getObject())
					.field("是否党校进修班",Project ->Project.getIsPartyCadre()==0?"否":"是")
					.field("培训方式",Project ->OutSideEnum.parseStatus(Project.getClassInfo().getIsOutside()))
					.field("开始日期", Project -> dataFormat(Optional.ofNullable(Project.getClassInfo().getArriveDate())))
					.field("结束日期", Project -> dataFormat(Optional.ofNullable(Project.getClassInfo().getReturnDate())))
					.field("计划时间", Project -> Project.getDays())
					.field("人数", Project -> Project.getAmount())
					.field("班主任", Project -> Project.getClassInfo().getTeacher())
					.field("班主任电话", Project -> Project.getClassInfo().getTeacherPhone())
					.field("客房", Project -> Project.getGuestroomsConfig() != null ? Project.getGuestroomsConfig() : "")
					.field("餐厅", Project -> Project.getRestaurantsConfig() != null ? Project.getRestaurantsConfig() : "")
					.field("教室", Project -> Project.getClassroomConfig() != null ? Project.getClassroomConfig() : "")
					.field("级别", Project -> Project.getProjectLevel())
					.field("类型", Project -> Project.getProjectType())
					.field("审批意见", Project -> Project.getSuggestion());
			writer.write(response.getOutputStream());
		}
	}

	private enum OutSideEnum {
		/**
		 * 培训方式 0：院内培训 1：院外培训 2：在线学习
		 */
		IN(0,"院内培训"),
		OUT(1,"院外培训"),
		ONLINE(2,"在线学习"),
		;
		private Integer status;
		private String msg;

		OutSideEnum(Integer status, String msg) {
			this.status = status;
			this.msg = msg;
		}

		private static String parseStatus(Integer status){
			for (OutSideEnum outSideEnum : OutSideEnum.values()) {
				if (outSideEnum.status.equals(status)){
					return outSideEnum.msg;
				}
			}
			return "";
		}
	}


	/**
	 * 时间格式转换
	 *
	 * @param time
	 * @return
	 */
	public String dataFormat(Optional<Long> time) {
		// 时间戳转化为Sting或Date
		if (time.isPresent()) {
			SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
			String d = format.format(new Date(time.get()));
			return d;
		}
		return "";
	}

	/**
	 * 当前用户是否为需求方
	 * @param context
	 * @param subject
	 * @return
	 */
	@RequestMapping(value = "/find-contact", method = RequestMethod.GET)
	@Permitted
	@Params
	@JSON("(*)")
	public Integer findContract(RequestContext context, Subject<Member> subject){
		return projectService.findContract(subject.getCurrentUserId());
	}
	/**
	 * 按月导出培训计划的列表查询
	 * @param requestContext
	 * @param subject
	 * @return
	 */
	@RequestMapping(value="/backdown",method=RequestMethod.GET)
	@Param(name = "arriveDate", type = String.class)
	@JSON("id,code,name,amount,days,teacherName,year,month,status,contactMemberName,organizationName,suggestion")
	@JSON("classInfo.(arriveDate,returnDate,teacher,teacherPhone,classInfoType)")
	@JSON("classResource.(restRoom,diningRoom,classroom)")
	@Permitted
	public List<Project> findDown(RequestContext requestContext, Subject<Member> subject) {
		Optional<String> OpStartTime = requestContext.getOptional("arriveDate", String.class);

		List<String> organizationIds = grantService.findGrantedOrganization(subject.getCurrentUserId(), "train/project", Optional.empty(), Optional.empty(),
				Optional.empty(), Optional.empty(), requestContext.getOptional("organizationId", String.class),
				Optional.empty(), Optional.empty()).stream().map(a -> a.getId()).collect(Collectors.toList());

		return projectService.findDownProject(stringTimeToOptionalLong(OpStartTime),subject.getCurrentUserId(),organizationIds);
	}

	public Optional<Long> stringTimeToOptionalLong(Optional<String> time) {
		return time.map(r -> {
			SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM");
			Date parse = null;
			Long data = null;
			try {
				parse = sdf.parse(r);
			} catch (ParseException e) {
				e.printStackTrace();
			}
			if (parse != null) {
				data = parse.getTime();
			}
			return data;
		});
	}

	/**
	 * 通过projectIds查找memberIds
	 * @param context
	 * @param subject
	 * @return
	 */
	@RequestMapping(value = "/contact-member-ids", method = RequestMethod.GET)
	@Param(name = "projectIds", type = String.class,required=true)
	@JSON("*")
	@Permitted
	public String[] findMemberIds(RequestContext context){
		return projectService.findMemberIdsByProjectIds(context.getString("projectIds").split(","));
	}


//	/**
//	 * 提醒需求方
//	 *
//	 * @param requestContext
//	 * @param subject
//	 * @return
//	 */
//	@RequestMapping(value = "/message", method = RequestMethod.PUT)
//	@Param(name = "ids", type = String.class, required = true)
//	@Param(name = "message", type = String.class, required = true)
//	@JSON("*")
//	public int message(RequestContext requestContext, Subject<Member> subject) {
////		com.zxy.product.human.entity.Organization organization = memberService
////				.getCompanyOrganizationWithLevel2ByMemberId(subject.getCurrentUserId());
//		String member = subject.getCurrentUserId();
//		String idsStr = requestContext.get("ids", String.class);
//		String message = requestContext.get("message", String.class);
//		int  num = messageRecordService.insert(member,idsStr,message,3,"1");
//		return 1;
//	}

	private String getDecryptParam(Optional<String> param) {
		if (param.isPresent()) {
			try {
				return Encrypt.Decrypt(param.get(), aesKey);
			} catch (Exception e) {
				e.printStackTrace();
				return null;
			}
		}
		return null;
	}

	private String getAesEncryptParam(Optional<String> param) {
		if (param.isPresent()) {
			try {
				return Encrypt.aesEncrypt(param.get(), aesKey);
			} catch (Exception e) {
				e.printStackTrace();
				return null;
			}
		}
		return null;
	}

}
