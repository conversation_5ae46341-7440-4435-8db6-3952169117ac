package com.zxy.product.train.web.controller;

import com.google.common.collect.ImmutableMap;
import com.zxy.common.restful.RequestContext;
import com.zxy.common.restful.annotation.Param;
import com.zxy.common.restful.json.JSON;
import com.zxy.common.restful.security.Permitted;
import com.zxy.common.restful.security.Subject;
import com.zxy.product.system.entity.Member;
import com.zxy.product.train.api.SmartCampusService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

@Controller
@RequestMapping("/smart-campus")
public class SmartCampusController {

    private static final Logger LOGGER = LoggerFactory.getLogger(SmartCampusController.class);

    private SmartCampusService smartCampusService;


    @Autowired
    public void setSmartCampusService(SmartCampusService smartCampusService) {
        this.smartCampusService = smartCampusService;
    }


    /**
     * 智慧校园入校报到数据推送接口
     */
    @RequestMapping(value = "/enrollment-registration", method = RequestMethod.POST)
    @Param(name = "classId", required = true)
    @Param(name = "memberId", required = true)
    @Param(name = "registerTime", type = Long.class, required = true)
    @JSON("*")
    public Map<String, String> register(RequestContext context) {
        String classId = context.getString("classId");
        String memberId = context.getString("memberId");
        Long registerTime = context.getLong("registerTime");
        return smartCampusService.register(classId, memberId, registerTime);
    }


    /**
     * 智慧校园学员考勤接口
     */
    @RequestMapping(value = "/student-attendance", method = RequestMethod.POST)
    @Param(name = "classId", required = true)
    @Param(name = "memberId", required = true)
    @Param(name = "attendanceTime", type = Long.class, required = true)
    @JSON("*")
    public Map<String, String> studentAttendance(RequestContext context) {
        String classId = context.getString("classId");
        String memberId = context.getString("memberId");
        Long attendanceTime = context.getLong("attendanceTime");
        return smartCampusService.studentAttendance(classId, memberId, attendanceTime);
    }


    @RequestMapping(value = "/jump-link", method = RequestMethod.POST)
    @Permitted
    @Param(name = "currentTimestamp", type = Long.class,required = true)
    @Param(name = "classId", required = true)
    @JSON("*")
    public Map<String,Object> jumpLink(RequestContext context, Subject<Member> subject){
        Long currentTimestamp = context.getLong("currentTimestamp");
        String classId = context.getString("classId");
        String userName = subject.getCurrentUser().getName();
        String intranetIp = "************";
        String ip = getIpAddr(context.getRequest());
        // 看当前是否是内网ip
        boolean bearerNetwork = intranetIp.equals(ip);
        return smartCampusService.getJumpLink(currentTimestamp,classId,userName,bearerNetwork);
    }
    /**
     *
     * 培训班满意度补偿接口
     * */
    @RequestMapping(value = "/satisfaction-compensate", method = RequestMethod.POST)
    @Param(name = "ids")
    @JSON("*")
    public Map<String,String> satisfactionCompensation(RequestContext context){

        String[] ids = context.getString("ids").split(",");
        smartCampusService.pushOverallSatisfaction(Arrays.asList(ids));
        return ImmutableMap.of("success", "ok");
    }


    /**
     *
     * 培训班新增餐厅字段推送-历史数据补偿
     * */
    @RequestMapping(value = "/field-compensate", method = RequestMethod.POST)
    @Param(name = "ids")
    @JSON("*")
    public Map<String,String> fieldCompensation(RequestContext context){

        String[] ids = context.getString("ids").split(",");
        if (ids.length == 0){
            return ImmutableMap.of("result", "ids is null");
        }
        smartCampusService.fieldCompensation(Arrays.asList(ids));
        return ImmutableMap.of("result", "success");
    }

    /**
     *
     * 培训班新增地址，班主任字段推送-历史数据补偿
     * */
    @RequestMapping(value = "/field-compensate2", method = RequestMethod.POST)
    @Param(name = "ids")
    @JSON("*")
    public Map<String,String> fieldCompensation2(RequestContext context){

        String[] ids = context.getString("ids").split(",");
        if (ids.length == 0){
            return ImmutableMap.of("result", "ids is null");
        }
        smartCampusService.fieldCompensation2(Arrays.asList(ids));
        return ImmutableMap.of("result", "success");
    }

    /**
     * 班级结束同步到智慧教务系统
     *
     * @param requestContext
     * @return
     */
    @RequestMapping(value = "/finish-class", method = RequestMethod.POST)
    @Param(name = "classId", type = String.class, required = true)
    @JSON("success,message")
    public Map<String, Object> finishClass(RequestContext requestContext) {
        String classId = requestContext.get("classId", String.class);
        LOGGER.info("接收到班级结束请求，classId: {}", classId);

        return smartCampusService.finishClass(classId);
    }

    /**
     * 取得真实地址IP(优先取x-forwarded-for)
     *
     * @param request
     * @return
     */
    private String getIpAddr(HttpServletRequest request) {
        //此方式用于nginx服务器参数设置
        String ip = request.getHeader("x-forwarded-for");
        if (ip != null && ip.length() > 0 && !"unknown".equalsIgnoreCase(ip)) {
            return ip.split(",")[0];
        }
        if (request.getHeader("X-Real-IP") != null) {
            return request.getHeader("X-Real-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        return ip;
    }
}
