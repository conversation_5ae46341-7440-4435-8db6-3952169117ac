/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.records;


import com.zxy.product.train.jooq.tables.ClassInfo;
import com.zxy.product.train.jooq.tables.interfaces.IClassInfo;
import org.jooq.Record1;
import org.jooq.impl.UpdatableRecordImpl;

import javax.annotation.Generated;
import java.sql.Timestamp;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ClassInfoRecord extends UpdatableRecordImpl<ClassInfoRecord> implements IClassInfo {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>train.t_class_info.f_id</code>. 表id
     */
    @Override
    public void setId(String value) {
        set(0, value);
    }

    /**
     * Getter for <code>train.t_class_info.f_id</code>. 表id
     */
    @Override
    public String getId() {
        return (String) get(0);
    }

    /**
     * Setter for <code>train.t_class_info.f_project_id</code>. 计划id
     */
    @Override
    public void setProjectId(String value) {
        set(1, value);
    }

    /**
     * Getter for <code>train.t_class_info.f_project_id</code>. 计划id
     */
    @Override
    public String getProjectId() {
        return (String) get(1);
    }

    /**
     * Setter for <code>train.t_class_info.f_class_teacher_phone</code>. 班主任电话
     */
    @Override
    public void setClassTeacherPhone(String value) {
        set(2, value);
    }

    /**
     * Getter for <code>train.t_class_info.f_class_teacher_phone</code>. 班主任电话
     */
    @Override
    public String getClassTeacherPhone() {
        return (String) get(2);
    }

    /**
     * Setter for <code>train.t_class_info.f_class_teacher</code>. 班主任
     */
    @Override
    public void setClassTeacher(String value) {
        set(3, value);
    }

    /**
     * Getter for <code>train.t_class_info.f_class_teacher</code>. 班主任
     */
    @Override
    public String getClassTeacher() {
        return (String) get(3);
    }

    /**
     * Setter for <code>train.t_class_info.f_arrive_date</code>. 报到日
     */
    @Override
    public void setArriveDate(Long value) {
        set(4, value);
    }

    /**
     * Getter for <code>train.t_class_info.f_arrive_date</code>. 报到日
     */
    @Override
    public Long getArriveDate() {
        return (Long) get(4);
    }

    /**
     * Setter for <code>train.t_class_info.f_return_date</code>. 返程日
     */
    @Override
    public void setReturnDate(Long value) {
        set(5, value);
    }

    /**
     * Getter for <code>train.t_class_info.f_return_date</code>. 返程日
     */
    @Override
    public Long getReturnDate() {
        return (Long) get(5);
    }

    /**
     * Setter for <code>train.t_class_info.f_is_outside</code>. 是否外部举办（0否，1是）
     */
    @Override
    public void setIsOutside(Integer value) {
        set(6, value);
    }

    /**
     * Getter for <code>train.t_class_info.f_is_outside</code>. 是否外部举办（0否，1是）
     */
    @Override
    public Integer getIsOutside() {
        return (Integer) get(6);
    }

    /**
     * Setter for <code>train.t_class_info.f_survey_type</code>. 需求调研方式
     */
    @Override
    public void setSurveyType(String value) {
        set(7, value);
    }

    /**
     * Getter for <code>train.t_class_info.f_survey_type</code>. 需求调研方式
     */
    @Override
    public String getSurveyType() {
        return (String) get(7);
    }

    /**
     * Setter for <code>train.t_class_info.f_target</code>. 培训目标
     */
    @Override
    public void setTarget(String value) {
        set(8, value);
    }

    /**
     * Getter for <code>train.t_class_info.f_target</code>. 培训目标
     */
    @Override
    public String getTarget() {
        return (String) get(8);
    }

    /**
     * Setter for <code>train.t_class_info.f_class_info_type</code>. 班级类别
     */
    @Override
    public void setClassInfoType(String value) {
        set(9, value);
    }

    /**
     * Getter for <code>train.t_class_info.f_class_info_type</code>. 班级类别
     */
    @Override
    public String getClassInfoType() {
        return (String) get(9);
    }

    /**
     * Setter for <code>train.t_class_info.f_student_type</code>. 人员类别
     */
    @Override
    public void setStudentType(String value) {
        set(10, value);
    }

    /**
     * Getter for <code>train.t_class_info.f_student_type</code>. 人员类别
     */
    @Override
    public String getStudentType() {
        return (String) get(10);
    }

    /**
     * Setter for <code>train.t_class_info.f_simple_type</code>. 补贴类型
     */
    @Override
    public void setSimpleType(String value) {
        set(11, value);
    }

    /**
     * Getter for <code>train.t_class_info.f_simple_type</code>. 补贴类型
     */
    @Override
    public String getSimpleType() {
        return (String) get(11);
    }

    /**
     * Setter for <code>train.t_class_info.f_is_plan</code>. 是否计划内
     */
    @Override
    public void setIsPlan(Integer value) {
        set(12, value);
    }

    /**
     * Getter for <code>train.t_class_info.f_is_plan</code>. 是否计划内
     */
    @Override
    public Integer getIsPlan() {
        return (Integer) get(12);
    }

    /**
     * Setter for <code>train.t_class_info.f_status</code>. 班级状态（1未实施、2实施中、3已实施）
     */
    @Override
    public void setStatus(Integer value) {
        set(13, value);
    }

    /**
     * Getter for <code>train.t_class_info.f_status</code>. 班级状态（1未实施、2实施中、3已实施）
     */
    @Override
    public Integer getStatus() {
        return (Integer) get(13);
    }

    /**
     * Setter for <code>train.t_class_info.f_confirm</code>. 是否提交（0否 1是）
     */
    @Override
    public void setConfirm(Integer value) {
        set(14, value);
    }

    /**
     * Getter for <code>train.t_class_info.f_confirm</code>. 是否提交（0否 1是）
     */
    @Override
    public Integer getConfirm() {
        return (Integer) get(14);
    }

    /**
     * Setter for <code>train.t_class_info.f_group_id</code>. 分组id
     */
    @Override
    public void setGroupId(String value) {
        set(15, value);
    }

    /**
     * Getter for <code>train.t_class_info.f_group_id</code>. 分组id
     */
    @Override
    public String getGroupId() {
        return (String) get(15);
    }

    /**
     * Setter for <code>train.t_class_info.f_short_name</code>. 短名称
     */
    @Override
    public void setShortName(String value) {
        set(16, value);
    }

    /**
     * Getter for <code>train.t_class_info.f_short_name</code>. 短名称
     */
    @Override
    public String getShortName() {
        return (String) get(16);
    }

    /**
     * Setter for <code>train.t_class_info.f_group_order</code>. 分组排序
     */
    @Override
    public void setGroupOrder(Integer value) {
        set(17, value);
    }

    /**
     * Getter for <code>train.t_class_info.f_group_order</code>. 分组排序
     */
    @Override
    public Integer getGroupOrder() {
        return (Integer) get(17);
    }

    /**
     * Setter for <code>train.t_class_info.f_create_time</code>. 创建时间
     */
    @Override
    public void setCreateTime(Long value) {
        set(18, value);
    }

    /**
     * Getter for <code>train.t_class_info.f_create_time</code>. 创建时间
     */
    @Override
    public Long getCreateTime() {
        return (Long) get(18);
    }

    /**
     * Setter for <code>train.t_class_info.f_create_member</code>. 创建人
     */
    @Override
    public void setCreateMember(String value) {
        set(19, value);
    }

    /**
     * Getter for <code>train.t_class_info.f_create_member</code>. 创建人
     */
    @Override
    public String getCreateMember() {
        return (String) get(19);
    }

    /**
     * Setter for <code>train.t_class_info.f_delete_flag</code>. 删除标记（0未删除，1已删除）
     */
    @Override
    public void setDeleteFlag(Integer value) {
        set(20, value);
    }

    /**
     * Getter for <code>train.t_class_info.f_delete_flag</code>. 删除标记（0未删除，1已删除）
     */
    @Override
    public Integer getDeleteFlag() {
        return (Integer) get(20);
    }

    /**
     * Setter for <code>train.t_class_info.f_implementation_year</code>. 实施年
     */
    @Override
    public void setImplementationYear(Integer value) {
        set(21, value);
    }

    /**
     * Getter for <code>train.t_class_info.f_implementation_year</code>. 实施年
     */
    @Override
    public Integer getImplementationYear() {
        return (Integer) get(21);
    }

    /**
     * Setter for <code>train.t_class_info.f_implementation_month</code>. 实施月
     */
    @Override
    public void setImplementationMonth(Integer value) {
        set(22, value);
    }

    /**
     * Getter for <code>train.t_class_info.f_implementation_month</code>. 实施月
     */
    @Override
    public Integer getImplementationMonth() {
        return (Integer) get(22);
    }

    /**
     * Setter for <code>train.t_class_info.f_member_satisfaction</code>. 学员满意率
     */
    @Override
    public void setMemberSatisfaction(Double value) {
        set(23, value);
    }

    /**
     * Getter for <code>train.t_class_info.f_member_satisfaction</code>. 学员满意率
     */
    @Override
    public Double getMemberSatisfaction() {
        return (Double) get(23);
    }

    /**
     * Setter for <code>train.t_class_info.f_class_satisfaction</code>. 课程满意率
     */
    @Override
    public void setClassSatisfaction(Double value) {
        set(24, value);
    }

    /**
     * Getter for <code>train.t_class_info.f_class_satisfaction</code>. 课程满意率
     */
    @Override
    public Double getClassSatisfaction() {
        return (Double) get(24);
    }

    /**
     * Setter for <code>train.t_class_info.f_trainee_num</code>. 实际参训人数
     */
    @Override
    public void setTraineeNum(Integer value) {
        set(25, value);
    }

    /**
     * Getter for <code>train.t_class_info.f_trainee_num</code>. 实际参训人数
     */
    @Override
    public Integer getTraineeNum() {
        return (Integer) get(25);
    }

    /**
     * Setter for <code>train.t_class_info.f_submit_num</code>. 满意度问卷提交数
     */
    @Override
    public void setSubmitNum(Integer value) {
        set(26, value);
    }

    /**
     * Getter for <code>train.t_class_info.f_submit_num</code>. 满意度问卷提交数
     */
    @Override
    public Integer getSubmitNum() {
        return (Integer) get(26);
    }

    /**
     * Setter for <code>train.t_class_info.f_notice</code>. 是否发布 1：已发布
     */
    @Override
    public void setNotice(Integer value) {
        set(27, value);
    }

    /**
     * Getter for <code>train.t_class_info.f_notice</code>. 是否发布 1：已发布
     */
    @Override
    public Integer getNotice() {
        return (Integer) get(27);
    }

    /**
     * Setter for <code>train.t_class_info.f_resource_status</code>. 预定资源状态
     */
    @Override
    public void setResourceStatus(Integer value) {
        set(28, value);
    }

    /**
     * Getter for <code>train.t_class_info.f_resource_status</code>. 预定资源状态
     */
    @Override
    public Integer getResourceStatus() {
        return (Integer) get(28);
    }

    /**
     * Setter for <code>train.t_class_info.f_totality_satisfied</code>. 总体满意度
     */
    @Override
    public void setTotalitySatisfied(Double value) {
        set(29, value);
    }

    /**
     * Getter for <code>train.t_class_info.f_totality_satisfied</code>. 总体满意度
     */
    @Override
    public Double getTotalitySatisfied() {
        return (Double) get(29);
    }

    /**
     * Setter for <code>train.t_class_info.f_course_satisfied</code>. 课程满意度
     */
    @Override
    public void setCourseSatisfied(Double value) {
        set(30, value);
    }

    /**
     * Getter for <code>train.t_class_info.f_course_satisfied</code>. 课程满意度
     */
    @Override
    public Double getCourseSatisfied() {
        return (Double) get(30);
    }

    /**
     * Setter for <code>train.t_class_info.f_organization_id</code>. 需求单位
     */
    @Override
    public void setOrganizationId(String value) {
        set(31, value);
    }

    /**
     * Getter for <code>train.t_class_info.f_organization_id</code>. 需求单位
     */
    @Override
    public String getOrganizationId() {
        return (String) get(31);
    }

    /**
     * Setter for <code>train.t_class_info.f_project_source</code>. 培训来源 1：集团级 2：省级
     */
    @Override
    public void setProjectSource(Integer value) {
        set(32, value);
    }

    /**
     * Getter for <code>train.t_class_info.f_project_source</code>. 培训来源 1：集团级 2：省级
     */
    @Override
    public Integer getProjectSource() {
        return (Integer) get(32);
    }

    /**
     * Setter for <code>train.t_class_info.f_sort</code>. 排序
     */
    @Override
    public void setSort(Integer value) {
        set(33, value);
    }

    /**
     * Getter for <code>train.t_class_info.f_sort</code>. 排序
     */
    @Override
    public Integer getSort() {
        return (Integer) get(33);
    }

    /**
     * Setter for <code>train.t_class_info.f_is_overproof</code>. 课酬是否超标：0,全部超标；1，单门课酬超标；2，课酬总额超标
     */
    @Override
    public void setIsOverproof(Integer value) {
        set(34, value);
    }

    /**
     * Getter for <code>train.t_class_info.f_is_overproof</code>. 课酬是否超标：0,全部超标；1，单门课酬超标；2，课酬总额超标
     */
    @Override
    public Integer getIsOverproof() {
        return (Integer) get(34);
    }

    /**
     * Setter for <code>train.t_class_info.f_four_degrees_submit_num</code>. 四度问卷提交数
     */
    @Override
    public void setFourDegreesSubmitNum(Integer value) {
        set(35, value);
    }

    /**
     * Getter for <code>train.t_class_info.f_four_degrees_submit_num</code>. 四度问卷提交数
     */
    @Override
    public Integer getFourDegreesSubmitNum() {
        return (Integer) get(35);
    }

    /**
     * Setter for <code>train.t_class_info.f_ability_submit_num</code>. 能力习得问卷提交数
     */
    @Override
    public void setAbilitySubmitNum(Integer value) {
        set(36, value);
    }

    /**
     * Getter for <code>train.t_class_info.f_ability_submit_num</code>. 能力习得问卷提交数
     */
    @Override
    public Integer getAbilitySubmitNum() {
        return (Integer) get(36);
    }

    /**
     * Setter for <code>train.t_class_info.f_superior_leadership_submit_num</code>. 上级领导提交数
     */
    @Override
    public void setSuperiorLeadershipSubmitNum(Integer value) {
        set(37, value);
    }

    /**
     * Getter for <code>train.t_class_info.f_superior_leadership_submit_num</code>. 上级领导提交数
     */
    @Override
    public Integer getSuperiorLeadershipSubmitNum() {
        return (Integer) get(37);
    }

    /**
     * Setter for <code>train.t_class_info.f_questionnaire_status</code>. 问卷状态
     */
    @Override
    public void setQuestionnaireStatus(Integer value) {
        set(38, value);
    }

    /**
     * Getter for <code>train.t_class_info.f_questionnaire_status</code>. 问卷状态
     */
    @Override
    public Integer getQuestionnaireStatus() {
        return (Integer) get(38);
    }

    /**
     * Setter for <code>train.t_class_info.f_course_salary</code>. 是否确认课酬
     */
    @Override
    public void setCourseSalary(Integer value) {
        set(39, value);
    }

    /**
     * Getter for <code>train.t_class_info.f_course_salary</code>. 是否确认课酬
     */
    @Override
    public Integer getCourseSalary() {
        return (Integer) get(39);
    }

    /**
     * Setter for <code>train.t_class_info.f_special_class</code>. 是否为特殊班级(0为正常，1为特殊)
     */
    @Override
    public void setSpecialClass(Integer value) {
        set(40, value);
    }

    /**
     * Getter for <code>train.t_class_info.f_special_class</code>. 是否为特殊班级(0为正常，1为特殊)
     */
    @Override
    public Integer getSpecialClass() {
        return (Integer) get(40);
    }

    /**
     * Setter for <code>train.t_class_info.f_class_level</code>. 培训班级别【null,0：普通班级；1：高管班】
     */
    @Override
    public void setClassLevel(Integer value) {
        set(41, value);
    }

    /**
     * Getter for <code>train.t_class_info.f_class_level</code>. 培训班级别【null,0：普通班级；1：高管班】
     */
    @Override
    public Integer getClassLevel() {
        return (Integer) get(41);
    }

    /**
     * Setter for <code>train.t_class_info.f_view</code>. 是否展示师资评价 0不展示 1展示
     */
    @Override
    public void setView(Integer value) {
        set(42, value);
    }

    /**
     * Getter for <code>train.t_class_info.f_view</code>. 是否展示师资评价 0不展示 1展示
     */
    @Override
    public Integer getView() {
        return (Integer) get(42);
    }

    /**
     * Setter for <code>train.t_class_info.f_modify_date</code>. 修改时间
     */
    @Override
    public void setModifyDate(Timestamp value) {
        set(43, value);
    }

    /**
     * Getter for <code>train.t_class_info.f_modify_date</code>. 修改时间
     */
    @Override
    public Timestamp getModifyDate() {
        return (Timestamp) get(43);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Record1<String> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(IClassInfo from) {
        setId(from.getId());
        setProjectId(from.getProjectId());
        setClassTeacherPhone(from.getClassTeacherPhone());
        setClassTeacher(from.getClassTeacher());
        setArriveDate(from.getArriveDate());
        setReturnDate(from.getReturnDate());
        setIsOutside(from.getIsOutside());
        setSurveyType(from.getSurveyType());
        setTarget(from.getTarget());
        setClassInfoType(from.getClassInfoType());
        setStudentType(from.getStudentType());
        setSimpleType(from.getSimpleType());
        setIsPlan(from.getIsPlan());
        setStatus(from.getStatus());
        setConfirm(from.getConfirm());
        setGroupId(from.getGroupId());
        setShortName(from.getShortName());
        setGroupOrder(from.getGroupOrder());
        setCreateTime(from.getCreateTime());
        setCreateMember(from.getCreateMember());
        setDeleteFlag(from.getDeleteFlag());
        setImplementationYear(from.getImplementationYear());
        setImplementationMonth(from.getImplementationMonth());
        setMemberSatisfaction(from.getMemberSatisfaction());
        setClassSatisfaction(from.getClassSatisfaction());
        setTraineeNum(from.getTraineeNum());
        setSubmitNum(from.getSubmitNum());
        setNotice(from.getNotice());
        setResourceStatus(from.getResourceStatus());
        setTotalitySatisfied(from.getTotalitySatisfied());
        setCourseSatisfied(from.getCourseSatisfied());
        setOrganizationId(from.getOrganizationId());
        setProjectSource(from.getProjectSource());
        setSort(from.getSort());
        setIsOverproof(from.getIsOverproof());
        setFourDegreesSubmitNum(from.getFourDegreesSubmitNum());
        setAbilitySubmitNum(from.getAbilitySubmitNum());
        setSuperiorLeadershipSubmitNum(from.getSuperiorLeadershipSubmitNum());
        setQuestionnaireStatus(from.getQuestionnaireStatus());
        setCourseSalary(from.getCourseSalary());
        setSpecialClass(from.getSpecialClass());
        setClassLevel(from.getClassLevel());
        setView(from.getView());
        setModifyDate(from.getModifyDate());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends IClassInfo> E into(E into) {
        into.from(this);
        return into;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached ClassInfoRecord
     */
    public ClassInfoRecord() {
        super(ClassInfo.CLASS_INFO);
    }

    /**
     * Create a detached, initialised ClassInfoRecord
     */
    public ClassInfoRecord(String id, String projectId, String classTeacherPhone, String classTeacher, Long arriveDate, Long returnDate, Integer isOutside, String surveyType, String target, String classInfoType, String studentType, String simpleType, Integer isPlan, Integer status, Integer confirm, String groupId, String shortName, Integer groupOrder, Long createTime, String createMember, Integer deleteFlag, Integer implementationYear, Integer implementationMonth, Double memberSatisfaction, Double classSatisfaction, Integer traineeNum, Integer submitNum, Integer notice, Integer resourceStatus, Double totalitySatisfied, Double courseSatisfied, String organizationId, Integer projectSource, Integer sort, Integer isOverproof, Integer fourDegreesSubmitNum, Integer abilitySubmitNum, Integer superiorLeadershipSubmitNum, Integer questionnaireStatus, Integer courseSalary, Integer specialClass, Integer classLevel, Integer view, Timestamp modifyDate) {
        super(ClassInfo.CLASS_INFO);

        set(0, id);
        set(1, projectId);
        set(2, classTeacherPhone);
        set(3, classTeacher);
        set(4, arriveDate);
        set(5, returnDate);
        set(6, isOutside);
        set(7, surveyType);
        set(8, target);
        set(9, classInfoType);
        set(10, studentType);
        set(11, simpleType);
        set(12, isPlan);
        set(13, status);
        set(14, confirm);
        set(15, groupId);
        set(16, shortName);
        set(17, groupOrder);
        set(18, createTime);
        set(19, createMember);
        set(20, deleteFlag);
        set(21, implementationYear);
        set(22, implementationMonth);
        set(23, memberSatisfaction);
        set(24, classSatisfaction);
        set(25, traineeNum);
        set(26, submitNum);
        set(27, notice);
        set(28, resourceStatus);
        set(29, totalitySatisfied);
        set(30, courseSatisfied);
        set(31, organizationId);
        set(32, projectSource);
        set(33, sort);
        set(34, isOverproof);
        set(35, fourDegreesSubmitNum);
        set(36, abilitySubmitNum);
        set(37, superiorLeadershipSubmitNum);
        set(38, questionnaireStatus);
        set(39, courseSalary);
        set(40, specialClass);
        set(41, classLevel);
        set(42, view);
        set(43, modifyDate);
    }

    public boolean fromPojo(Object source) {
        if (!(source instanceof com.zxy.product.train.jooq.tables.pojos.ClassInfoEntity)) {
            return false;
        }
        com.zxy.product.train.jooq.tables.pojos.ClassInfoEntity pojo = (com.zxy.product.train.jooq.tables.pojos.ClassInfoEntity)source;
        pojo.into(this);
        return true;
    }
}
