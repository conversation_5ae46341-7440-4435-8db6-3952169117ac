/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.records;


import com.zxy.product.train.jooq.tables.Project;
import com.zxy.product.train.jooq.tables.interfaces.IProject;

import java.sql.Timestamp;

import javax.annotation.Generated;

import org.jooq.Record1;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ProjectRecord extends UpdatableRecordImpl<ProjectRecord> implements IProject {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>train.t_project.f_id</code>. 主键
     */
    @Override
    public void setId(String value) {
        set(0, value);
    }

    /**
     * Getter for <code>train.t_project.f_id</code>. 主键
     */
    @Override
    public String getId() {
        return (String) get(0);
    }

    /**
     * Setter for <code>train.t_project.f_name</code>. 计划名称
     */
    @Override
    public void setName(String value) {
        set(1, value);
    }

    /**
     * Getter for <code>train.t_project.f_name</code>. 计划名称
     */
    @Override
    public String getName() {
        return (String) get(1);
    }

    /**
     * Setter for <code>train.t_project.f_code</code>. MIS编码
     */
    @Override
    public void setCode(String value) {
        set(2, value);
    }

    /**
     * Getter for <code>train.t_project.f_code</code>. MIS编码
     */
    @Override
    public String getCode() {
        return (String) get(2);
    }

    /**
     * Setter for <code>train.t_project.f_contact_member_id</code>. 需求单位联系人
     */
    @Override
    public void setContactMemberId(String value) {
        set(3, value);
    }

    /**
     * Getter for <code>train.t_project.f_contact_member_id</code>. 需求单位联系人
     */
    @Override
    public String getContactMemberId() {
        return (String) get(3);
    }

    /**
     * Setter for <code>train.t_project.f_organization_id</code>. 需求单位
     */
    @Override
    public void setOrganizationId(String value) {
        set(4, value);
    }

    /**
     * Getter for <code>train.t_project.f_organization_id</code>. 需求单位
     */
    @Override
    public String getOrganizationId() {
        return (String) get(4);
    }

    /**
     * Setter for <code>train.t_project.f_contact_phone</code>. 联系电话
     */
    @Override
    public void setContactPhone(String value) {
        set(5, value);
    }

    /**
     * Getter for <code>train.t_project.f_contact_phone</code>. 联系电话
     */
    @Override
    public String getContactPhone() {
        return (String) get(5);
    }

    /**
     * Setter for <code>train.t_project.f_contact_email</code>. 联系邮箱
     */
    @Override
    public void setContactEmail(String value) {
        set(6, value);
    }

    /**
     * Getter for <code>train.t_project.f_contact_email</code>. 联系邮箱
     */
    @Override
    public String getContactEmail() {
        return (String) get(6);
    }

    /**
     * Setter for <code>train.t_project.f_year</code>. 计划年份
     */
    @Override
    public void setYear(Integer value) {
        set(7, value);
    }

    /**
     * Getter for <code>train.t_project.f_year</code>. 计划年份
     */
    @Override
    public Integer getYear() {
        return (Integer) get(7);
    }

    /**
     * Setter for <code>train.t_project.f_month</code>. 计划月份
     */
    @Override
    public void setMonth(Integer value) {
        set(8, value);
    }

    /**
     * Getter for <code>train.t_project.f_month</code>. 计划月份
     */
    @Override
    public Integer getMonth() {
        return (Integer) get(8);
    }

    /**
     * Setter for <code>train.t_project.f_amount</code>. 计划人数
     */
    @Override
    public void setAmount(Integer value) {
        set(9, value);
    }

    /**
     * Getter for <code>train.t_project.f_amount</code>. 计划人数
     */
    @Override
    public Integer getAmount() {
        return (Integer) get(9);
    }

    /**
     * Setter for <code>train.t_project.f_object</code>. 培训对象
     */
    @Override
    public void setObject(String value) {
        set(10, value);
    }

    /**
     * Getter for <code>train.t_project.f_object</code>. 培训对象
     */
    @Override
    public String getObject() {
        return (String) get(10);
    }

    /**
     * Setter for <code>train.t_project.f_days</code>. 计划培训天数
     */
    @Override
    public void setDays(Integer value) {
        set(11, value);
    }

    /**
     * Getter for <code>train.t_project.f_days</code>. 计划培训天数
     */
    @Override
    public Integer getDays() {
        return (Integer) get(11);
    }

    /**
     * Setter for <code>train.t_project.f_type_id</code>. 培训类型
     */
    @Override
    public void setTypeId(String value) {
        set(12, value);
    }

    /**
     * Getter for <code>train.t_project.f_type_id</code>. 培训类型
     */
    @Override
    public String getTypeId() {
        return (String) get(12);
    }

    /**
     * Setter for <code>train.t_project.f_address</code>. 培训地点
     */
    @Override
    public void setAddress(String value) {
        set(13, value);
    }

    /**
     * Getter for <code>train.t_project.f_address</code>. 培训地点
     */
    @Override
    public String getAddress() {
        return (String) get(13);
    }

    /**
     * Setter for <code>train.t_project.f_status</code>. 状态（1待预定2待审核3同意申请4资源已满5不同意）
     */
    @Override
    public void setStatus(Integer value) {
        set(14, value);
    }

    /**
     * Getter for <code>train.t_project.f_status</code>. 状态（1待预定2待审核3同意申请4资源已满5不同意）
     */
    @Override
    public Integer getStatus() {
        return (Integer) get(14);
    }

    /**
     * Setter for <code>train.t_project.f_create_time</code>. 创建时间
     */
    @Override
    public void setCreateTime(Long value) {
        set(15, value);
    }

    /**
     * Getter for <code>train.t_project.f_create_time</code>. 创建时间
     */
    @Override
    public Long getCreateTime() {
        return (Long) get(15);
    }

    /**
     * Setter for <code>train.t_project.f_create_member</code>. 创建人ID
     */
    @Override
    public void setCreateMember(String value) {
        set(16, value);
    }

    /**
     * Getter for <code>train.t_project.f_create_member</code>. 创建人ID
     */
    @Override
    public String getCreateMember() {
        return (String) get(16);
    }

    /**
     * Setter for <code>train.t_project.f_delete_flag</code>. 删除标记（0未删除1已删除）
     */
    @Override
    public void setDeleteFlag(Integer value) {
        set(17, value);
    }

    /**
     * Getter for <code>train.t_project.f_delete_flag</code>. 删除标记（0未删除1已删除）
     */
    @Override
    public Integer getDeleteFlag() {
        return (Integer) get(17);
    }

    /**
     * Setter for <code>train.t_project.f_student_status</code>. 判断是否是学员端提交审核 0否 1是
     */
    @Override
    public void setStudentStatus(Integer value) {
        set(18, value);
    }

    /**
     * Getter for <code>train.t_project.f_student_status</code>. 判断是否是学员端提交审核 0否 1是
     */
    @Override
    public Integer getStudentStatus() {
        return (Integer) get(18);
    }

    /**
     * Setter for <code>train.t_project.f_cost</code>. 费用类型
     */
    @Override
    public void setCost(String value) {
        set(19, value);
    }

    /**
     * Getter for <code>train.t_project.f_cost</code>. 费用类型
     */
    @Override
    public String getCost() {
        return (String) get(19);
    }

    /**
     * Setter for <code>train.t_project.f_survey_type</code>. 需求调研方式
     */
    @Override
    public void setSurveyType(String value) {
        set(20, value);
    }

    /**
     * Getter for <code>train.t_project.f_survey_type</code>. 需求调研方式
     */
    @Override
    public String getSurveyType() {
        return (String) get(20);
    }

    /**
     * Setter for <code>train.t_project.f_target</code>. 培训目标
     */
    @Override
    public void setTarget(String value) {
        set(21, value);
    }

    /**
     * Getter for <code>train.t_project.f_target</code>. 培训目标
     */
    @Override
    public String getTarget() {
        return (String) get(21);
    }

    /**
     * Setter for <code>train.t_project.f_temp</code>.
     */
    @Override
    public void setTemp(String value) {
        set(22, value);
    }

    /**
     * Getter for <code>train.t_project.f_temp</code>.
     */
    @Override
    public String getTemp() {
        return (String) get(22);
    }

    /**
     * Setter for <code>train.t_project.f_is_party_cadre</code>. 是否是党干部培训班 0:否 1:是
     */
    @Override
    public void setIsPartyCadre(Integer value) {
        set(23, value);
    }

    /**
     * Getter for <code>train.t_project.f_is_party_cadre</code>. 是否是党干部培训班 0:否 1:是
     */
    @Override
    public Integer getIsPartyCadre() {
        return (Integer) get(23);
    }

    /**
     * Setter for <code>train.t_project.f_modify_date</code>. 修改时间
     */
    @Override
    public void setModifyDate(Timestamp value) {
        set(24, value);
    }

    /**
     * Getter for <code>train.t_project.f_modify_date</code>. 修改时间
     */
    @Override
    public Timestamp getModifyDate() {
        return (Timestamp) get(24);
    }

    /**
     * Setter for <code>train.t_project.f_reservation_member</code>. 预定人id
     */
    @Override
    public void setReservationMember(String value) {
        set(25, value);
    }

    /**
     * Getter for <code>train.t_project.f_reservation_member</code>. 预定人id
     */
    @Override
    public String getReservationMember() {
        return (String) get(25);
    }

    /**
     * Setter for <code>train.t_project.f_reservation_time</code>. 预定时间
     */
    @Override
    public void setReservationTime(Long value) {
        set(26, value);
    }

    /**
     * Getter for <code>train.t_project.f_reservation_time</code>. 预定时间
     */
    @Override
    public Long getReservationTime() {
        return (Long) get(26);
    }

    /**
     * Setter for <code>train.t_project.f_is_manual_finish</code>. 是否手动结束：0-否，1-是
     */
    @Override
    public void setIsManualFinish(Integer value) {
        set(27, value);
    }

    /**
     * Getter for <code>train.t_project.f_is_manual_finish</code>. 是否手动结束：0-否，1-是
     */
    @Override
    public Integer getIsManualFinish() {
        return (Integer) get(27);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Record1<String> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(IProject from) {
        setId(from.getId());
        setName(from.getName());
        setCode(from.getCode());
        setContactMemberId(from.getContactMemberId());
        setOrganizationId(from.getOrganizationId());
        setContactPhone(from.getContactPhone());
        setContactEmail(from.getContactEmail());
        setYear(from.getYear());
        setMonth(from.getMonth());
        setAmount(from.getAmount());
        setObject(from.getObject());
        setDays(from.getDays());
        setTypeId(from.getTypeId());
        setAddress(from.getAddress());
        setStatus(from.getStatus());
        setCreateTime(from.getCreateTime());
        setCreateMember(from.getCreateMember());
        setDeleteFlag(from.getDeleteFlag());
        setStudentStatus(from.getStudentStatus());
        setCost(from.getCost());
        setSurveyType(from.getSurveyType());
        setTarget(from.getTarget());
        setTemp(from.getTemp());
        setIsPartyCadre(from.getIsPartyCadre());
        setModifyDate(from.getModifyDate());
        setReservationMember(from.getReservationMember());
        setReservationTime(from.getReservationTime());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends IProject> E into(E into) {
        into.from(this);
        return into;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached ProjectRecord
     */
    public ProjectRecord() {
        super(Project.PROJECT);
    }

    /**
     * Create a detached, initialised ProjectRecord
     */
    public ProjectRecord(String id, String name, String code, String contactMemberId, String organizationId, String contactPhone, String contactEmail, Integer year, Integer month, Integer amount, String object, Integer days, String typeId, String address, Integer status, Long createTime, String createMember, Integer deleteFlag, Integer studentStatus, String cost, String surveyType, String target, String temp, Integer isPartyCadre, Timestamp modifyDate, String reservationMember, Long reservationTime, Integer isManualFinish) {
        super(Project.PROJECT);

        set(0, id);
        set(1, name);
        set(2, code);
        set(3, contactMemberId);
        set(4, organizationId);
        set(5, contactPhone);
        set(6, contactEmail);
        set(7, year);
        set(8, month);
        set(9, amount);
        set(10, object);
        set(11, days);
        set(12, typeId);
        set(13, address);
        set(14, status);
        set(15, createTime);
        set(16, createMember);
        set(17, deleteFlag);
        set(18, studentStatus);
        set(19, cost);
        set(20, surveyType);
        set(21, target);
        set(22, temp);
        set(23, isPartyCadre);
        set(24, modifyDate);
        set(25, reservationMember);
        set(26, reservationTime);
        set(27, isManualFinish);
    }

    public boolean fromPojo(Object source) {
        if (!(source instanceof com.zxy.product.train.jooq.tables.pojos.ProjectEntity)) {
            return false;
        }
        com.zxy.product.train.jooq.tables.pojos.ProjectEntity pojo = (com.zxy.product.train.jooq.tables.pojos.ProjectEntity)source;
        pojo.into(this);
        return true;
    }
}
