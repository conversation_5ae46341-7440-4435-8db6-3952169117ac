/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.records;


import com.zxy.product.train.jooq.tables.ClassPopMangement;
import com.zxy.product.train.jooq.tables.interfaces.IClassPopMangement;
import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record6;
import org.jooq.Row6;
import org.jooq.impl.UpdatableRecordImpl;

import javax.annotation.Generated;


/**
 * 学员管理弹窗管理
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ClassPopMangementRecord extends UpdatableRecordImpl<ClassPopMangementRecord> implements Record6<String, String, Integer, Integer, String, Long>, IClassPopMangement {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>train.t_class_pop_mangement.f_id</code>. 表id
     */
    @Override
    public void setId(String value) {
        set(0, value);
    }

    /**
     * Getter for <code>train.t_class_pop_mangement.f_id</code>. 表id
     */
    @Override
    public String getId() {
        return (String) get(0);
    }

    /**
     * Setter for <code>train.t_class_pop_mangement.f_member_id</code>. 人员id
     */
    @Override
    public void setMemberId(String value) {
        set(1, value);
    }

    /**
     * Getter for <code>train.t_class_pop_mangement.f_member_id</code>. 人员id
     */
    @Override
    public String getMemberId() {
        return (String) get(1);
    }

    /**
     * Setter for <code>train.t_class_pop_mangement.f_type</code>. 班务人员类型：0.管理员 1.班主任
     */
    @Override
    public void setType(Integer value) {
        set(2, value);
    }

    /**
     * Getter for <code>train.t_class_pop_mangement.f_type</code>. 班务人员类型：0.管理员 1.班主任
     */
    @Override
    public Integer getType() {
        return (Integer) get(2);
    }

    /**
     * Setter for <code>train.t_class_pop_mangement.f_flag</code>. 弹窗设置 0 弹出 1 关闭
     */
    @Override
    public void setFlag(Integer value) {
        set(3, value);
    }

    /**
     * Getter for <code>train.t_class_pop_mangement.f_flag</code>. 弹窗设置 0 弹出 1 关闭
     */
    @Override
    public Integer getFlag() {
        return (Integer) get(3);
    }

    /**
     * Setter for <code>train.t_class_pop_mangement.f_class_id</code>. 班级id
     */
    @Override
    public void setClassId(String value) {
        set(4, value);
    }

    /**
     * Getter for <code>train.t_class_pop_mangement.f_class_id</code>. 班级id
     */
    @Override
    public String getClassId() {
        return (String) get(4);
    }

    /**
     * Setter for <code>train.t_class_pop_mangement.f_create_time</code>. 创建时间
     */
    @Override
    public void setCreateTime(Long value) {
        set(5, value);
    }

    /**
     * Getter for <code>train.t_class_pop_mangement.f_create_time</code>. 创建时间
     */
    @Override
    public Long getCreateTime() {
        return (Long) get(5);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Record1<String> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record6 type implementation
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Row6<String, String, Integer, Integer, String, Long> fieldsRow() {
        return (Row6) super.fieldsRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Row6<String, String, Integer, Integer, String, Long> valuesRow() {
        return (Row6) super.valuesRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field1() {
        return ClassPopMangement.CLASS_POP_MANGEMENT.ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field2() {
        return ClassPopMangement.CLASS_POP_MANGEMENT.MEMBER_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field3() {
        return ClassPopMangement.CLASS_POP_MANGEMENT.TYPE;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field4() {
        return ClassPopMangement.CLASS_POP_MANGEMENT.FLAG;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field5() {
        return ClassPopMangement.CLASS_POP_MANGEMENT.CLASS_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Long> field6() {
        return ClassPopMangement.CLASS_POP_MANGEMENT.CREATE_TIME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value1() {
        return getId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value2() {
        return getMemberId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value3() {
        return getType();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value4() {
        return getFlag();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value5() {
        return getClassId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Long value6() {
        return getCreateTime();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ClassPopMangementRecord value1(String value) {
        setId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ClassPopMangementRecord value2(String value) {
        setMemberId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ClassPopMangementRecord value3(Integer value) {
        setType(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ClassPopMangementRecord value4(Integer value) {
        setFlag(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ClassPopMangementRecord value5(String value) {
        setClassId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ClassPopMangementRecord value6(Long value) {
        setCreateTime(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ClassPopMangementRecord values(String value1, String value2, Integer value3, Integer value4, String value5, Long value6) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        return this;
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(IClassPopMangement from) {
        setId(from.getId());
        setMemberId(from.getMemberId());
        setType(from.getType());
        setFlag(from.getFlag());
        setClassId(from.getClassId());
        setCreateTime(from.getCreateTime());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends IClassPopMangement> E into(E into) {
        into.from(this);
        return into;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached ClassPopMangementRecord
     */
    public ClassPopMangementRecord() {
        super(ClassPopMangement.CLASS_POP_MANGEMENT);
    }

    /**
     * Create a detached, initialised ClassPopMangementRecord
     */
    public ClassPopMangementRecord(String id, String memberId, Integer type, Integer flag, String classId, Long createTime) {
        super(ClassPopMangement.CLASS_POP_MANGEMENT);

        set(0, id);
        set(1, memberId);
        set(2, type);
        set(3, flag);
        set(4, classId);
        set(5, createTime);
    }

    public boolean fromPojo(Object source) {
        if (!(source instanceof com.zxy.product.train.jooq.tables.pojos.ClassPopMangementEntity)) {
            return false;
        }
        com.zxy.product.train.jooq.tables.pojos.ClassPopMangementEntity pojo = (com.zxy.product.train.jooq.tables.pojos.ClassPopMangementEntity)source;
        pojo.into(this);
        return true;
    }
}
