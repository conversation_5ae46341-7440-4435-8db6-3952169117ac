/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq;


import com.zxy.product.train.jooq.tables.*;
import com.zxy.product.train.jooq.tables.records.*;
import org.jooq.UniqueKey;
import org.jooq.impl.AbstractKeys;

import javax.annotation.Generated;


/**
 * A class modelling foreign key relationships between tables of the <code>train</code>
 * schema
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class Keys {

    // -------------------------------------------------------------------------
    // IDENTITY definitions
    // -------------------------------------------------------------------------


    // -------------------------------------------------------------------------
    // UNIQUE and PRIMARY KEY definitions
    // -------------------------------------------------------------------------
    public static final UniqueKey<StudyTeamActivityPhotosRecord> KEY_T_STUDY_TEAM_ACTIVITY_PHOTOS_PRIMARY = UniqueKeys0.KEY_T_STUDY_TEAM_ACTIVITY_PHOTOS_PRIMARY;
    public static final UniqueKey<AlbumRecord> KEY_T_ALBUM_PRIMARY = UniqueKeys0.KEY_T_ALBUM_PRIMARY;
    public static final UniqueKey<AudienceItemRecord> KEY_T_AUDIENCE_ITEM_PRIMARY = UniqueKeys0.KEY_T_AUDIENCE_ITEM_PRIMARY;
    public static final UniqueKey<AudienceMemberRecord> KEY_T_AUDIENCE_MEMBER_PRIMARY = UniqueKeys0.KEY_T_AUDIENCE_MEMBER_PRIMARY;
    public static final UniqueKey<AudienceObjectRecord> KEY_T_AUDIENCE_OBJECT_PRIMARY = UniqueKeys0.KEY_T_AUDIENCE_OBJECT_PRIMARY;
    public static final UniqueKey<BusRecord> KEY_T_BUS_PRIMARY = UniqueKeys0.KEY_T_BUS_PRIMARY;
    public static final UniqueKey<BusDetailRecord> KEY_T_BUS_DETAIL_PRIMARY = UniqueKeys0.KEY_T_BUS_DETAIL_PRIMARY;
    public static final UniqueKey<BusOptionRecord> KEY_T_BUS_OPTION_PRIMARY = UniqueKeys0.KEY_T_BUS_OPTION_PRIMARY;
    public static final UniqueKey<CenterConfigurationRecord> KEY_T_CENTER_CONFIGURATION_PRIMARY = UniqueKeys0.KEY_T_CENTER_CONFIGURATION_PRIMARY;
    public static final UniqueKey<ClassroomConfigurationRecord> KEY_T_CLASSROOM_CONFIGURATION_PRIMARY = UniqueKeys0.KEY_T_CLASSROOM_CONFIGURATION_PRIMARY;
    public static final UniqueKey<ClassstaffClassRecord> KEY_T_CLASSSTAFF_CLASS_PRIMARY = UniqueKeys0.KEY_T_CLASSSTAFF_CLASS_PRIMARY;
    public static final UniqueKey<ClassstaffConfigurationRecord> KEY_T_CLASSSTAFF_CONFIGURATION_PRIMARY = UniqueKeys0.KEY_T_CLASSSTAFF_CONFIGURATION_PRIMARY;
    public static final UniqueKey<ClassBusinessProgressRecord> KEY_T_CLASS_BUSINESS_PROGRESS_PRIMARY = UniqueKeys0.KEY_T_CLASS_BUSINESS_PROGRESS_PRIMARY;
    public static final UniqueKey<ClassCourseHistoryRecord> KEY_T_CLASS_COURSE_HISTORY_PRIMARY = UniqueKeys0.KEY_T_CLASS_COURSE_HISTORY_PRIMARY;
    public static final UniqueKey<ClassDetailRecord> KEY_T_CLASS_DETAIL_PRIMARY = UniqueKeys0.KEY_T_CLASS_DETAIL_PRIMARY;
    public static final UniqueKey<ClassEvaluateRecord> KEY_T_CLASS_EVALUATE_PRIMARY = UniqueKeys0.KEY_T_CLASS_EVALUATE_PRIMARY;
    public static final UniqueKey<ClassGroupRecord> KEY_T_CLASS_GROUP_PRIMARY = UniqueKeys0.KEY_T_CLASS_GROUP_PRIMARY;
    public static final UniqueKey<ClassHistoryRecord> KEY_T_CLASS_HISTORY_PRIMARY = UniqueKeys0.KEY_T_CLASS_HISTORY_PRIMARY;
    public static final UniqueKey<ClassInfoRecord> KEY_T_CLASS_INFO_PRIMARY = UniqueKeys0.KEY_T_CLASS_INFO_PRIMARY;
    public static final UniqueKey<ClassOfflineCourseRecord> KEY_T_CLASS_OFFLINE_COURSE_PRIMARY = UniqueKeys0.KEY_T_CLASS_OFFLINE_COURSE_PRIMARY;
    public static final UniqueKey<ClassOnlineCourseRecord> KEY_T_CLASS_ONLINE_COURSE_PRIMARY = UniqueKeys0.KEY_T_CLASS_ONLINE_COURSE_PRIMARY;
    public static final UniqueKey<ClassQuestionnaireTotalRecord> KEY_T_CLASS_QUESTIONNAIRE_TOTAL_PRIMARY = UniqueKeys0.KEY_T_CLASS_QUESTIONNAIRE_TOTAL_PRIMARY;
    public static final UniqueKey<ClassQuotaRecord> KEY_T_CLASS_QUOTA_PRIMARY = UniqueKeys0.KEY_T_CLASS_QUOTA_PRIMARY;
    public static final UniqueKey<ClassQuotaDetailRecord> KEY_T_CLASS_QUOTA_DETAIL_PRIMARY = UniqueKeys0.KEY_T_CLASS_QUOTA_DETAIL_PRIMARY;
    public static final UniqueKey<ClassRequiredRecord> KEY_T_CLASS_REQUIRED_PRIMARY = UniqueKeys0.KEY_T_CLASS_REQUIRED_PRIMARY;
    public static final UniqueKey<ClassRequiredThemeRecord> KEY_T_CLASS_REQUIRED_THEME_PRIMARY = UniqueKeys0.KEY_T_CLASS_REQUIRED_THEME_PRIMARY;
    public static final UniqueKey<ClassResourceRecord> KEY_T_CLASS_RESOURCE_PRIMARY = UniqueKeys0.KEY_T_CLASS_RESOURCE_PRIMARY;
    public static final UniqueKey<ClassSignupInfoRecord> KEY_T_CLASS_SIGNUP_INFO_PRIMARY = UniqueKeys0.KEY_T_CLASS_SIGNUP_INFO_PRIMARY;
    public static final UniqueKey<ClassStatisticsRecord> KEY_T_CLASS_STATISTICS_PRIMARY = UniqueKeys0.KEY_T_CLASS_STATISTICS_PRIMARY;
    public static final UniqueKey<ClassThemeRecord> KEY_T_CLASS_THEME_PRIMARY = UniqueKeys0.KEY_T_CLASS_THEME_PRIMARY;
    public static final UniqueKey<ClassTwoBringsRecord> KEY_T_CLASS_TWO_BRINGS_PRIMARY = UniqueKeys0.KEY_T_CLASS_TWO_BRINGS_PRIMARY;
    public static final UniqueKey<CollectingCourseRecord> KEY_T_COLLECTING_COURSE_PRIMARY = UniqueKeys0.KEY_T_COLLECTING_COURSE_PRIMARY;
    public static final UniqueKey<CollectionProgrammeConfigRecord> KEY_T_COLLECTION_PROGRAMME_CONFIG_PRIMARY = UniqueKeys0.KEY_T_COLLECTION_PROGRAMME_CONFIG_PRIMARY;
    public static final UniqueKey<CollectionProgrammeCourseRecord> KEY_T_COLLECTION_PROGRAMME_COURSE_PRIMARY = UniqueKeys0.KEY_T_COLLECTION_PROGRAMME_COURSE_PRIMARY;
    public static final UniqueKey<CollegeTeachingRecord> KEY_T_COLLEGE_TEACHING_PRIMARY = UniqueKeys0.KEY_T_COLLEGE_TEACHING_PRIMARY;
    public static final UniqueKey<ConfigruationHistoryRecord> KEY_T_CONFIGRUATION_HISTORY_PRIMARY = UniqueKeys0.KEY_T_CONFIGRUATION_HISTORY_PRIMARY;
    public static final UniqueKey<ConfigurationRecord> KEY_T_CONFIGURATION_PRIMARY = UniqueKeys0.KEY_T_CONFIGURATION_PRIMARY;
    public static final UniqueKey<ConfigurationValueRecord> KEY_T_CONFIGURATION_VALUE_PRIMARY = UniqueKeys0.KEY_T_CONFIGURATION_VALUE_PRIMARY;
    public static final UniqueKey<CorporateSegmentRelationRecord> KEY_T_CORPORATE_SEGMENT_RELATION_PRIMARY = UniqueKeys0.KEY_T_CORPORATE_SEGMENT_RELATION_PRIMARY;
    public static final UniqueKey<CourseAttachRecord> KEY_T_COURSE_ATTACH_PRIMARY = UniqueKeys0.KEY_T_COURSE_ATTACH_PRIMARY;
    public static final UniqueKey<CourseAttributeRecord> KEY_T_COURSE_ATTRIBUTE_PRIMARY = UniqueKeys0.KEY_T_COURSE_ATTRIBUTE_PRIMARY;
    public static final UniqueKey<CourseCategoryRecord> KEY_T_COURSE_CATEGORY_PRIMARY = UniqueKeys0.KEY_T_COURSE_CATEGORY_PRIMARY;
    public static final UniqueKey<CourseInfoRecord> KEY_T_COURSE_INFO_PRIMARY = UniqueKeys0.KEY_T_COURSE_INFO_PRIMARY;
    public static final UniqueKey<CourseSalaryRecord> KEY_T_COURSE_SALARY_PRIMARY = UniqueKeys0.KEY_T_COURSE_SALARY_PRIMARY;
    public static final UniqueKey<CourseTeachingActivitiesRecord> KEY_T_COURSE_TEACHING_ACTIVITIES_PRIMARY = UniqueKeys0.KEY_T_COURSE_TEACHING_ACTIVITIES_PRIMARY;
    public static final UniqueKey<DemandSideOrganizationRecord> KEY_T_DEMAND_SIDE_ORGANIZATION_PRIMARY = UniqueKeys0.KEY_T_DEMAND_SIDE_ORGANIZATION_PRIMARY;
    public static final UniqueKey<DimensionRecord> KEY_T_DIMENSION_PRIMARY = UniqueKeys0.KEY_T_DIMENSION_PRIMARY;
    public static final UniqueKey<DimensionQuestionRecord> KEY_T_DIMENSION_QUESTION_PRIMARY = UniqueKeys0.KEY_T_DIMENSION_QUESTION_PRIMARY;
    public static final UniqueKey<EvaluateRecord> KEY_T_EVALUATE_PRIMARY = UniqueKeys0.KEY_T_EVALUATE_PRIMARY;
    public static final UniqueKey<F2fCourseRecord> KEY_T_F2F_COURSE_PRIMARY = UniqueKeys0.KEY_T_F2F_COURSE_PRIMARY;
    public static final UniqueKey<F2fCourseLibraryRecord> KEY_T_F2F_COURSE_LIBRARY_PRIMARY = UniqueKeys0.KEY_T_F2F_COURSE_LIBRARY_PRIMARY;
    public static final UniqueKey<GrantDetailRecord> KEY_T_GRANT_DETAIL_PRIMARY = UniqueKeys0.KEY_T_GRANT_DETAIL_PRIMARY;
    public static final UniqueKey<GroupConfigurationRecord> KEY_T_GROUP_CONFIGURATION_PRIMARY = UniqueKeys0.KEY_T_GROUP_CONFIGURATION_PRIMARY;
    public static final UniqueKey<GroupConfigurationValueRecord> KEY_T_GROUP_CONFIGURATION_VALUE_PRIMARY = UniqueKeys0.KEY_T_GROUP_CONFIGURATION_VALUE_PRIMARY;
    public static final UniqueKey<HomeLecturerRecord> KEY_T_HOME_LECTURER_PRIMARY = UniqueKeys0.KEY_T_HOME_LECTURER_PRIMARY;
    public static final UniqueKey<JobRecord> KEY_T_JOB_PRIMARY = UniqueKeys0.KEY_T_JOB_PRIMARY;
    public static final UniqueKey<LabelRecord> KEY_T_LABEL_PRIMARY = UniqueKeys0.KEY_T_LABEL_PRIMARY;
    public static final UniqueKey<LecturerRecord> KEY_T_LECTURER_PRIMARY = UniqueKeys0.KEY_T_LECTURER_PRIMARY;
    public static final UniqueKey<LecturerAdeptCourseRecord> KEY_T_LECTURER_ADEPT_COURSE_PRIMARY = UniqueKeys0.KEY_T_LECTURER_ADEPT_COURSE_PRIMARY;
    public static final UniqueKey<LecturerAttributeRecord> KEY_T_LECTURER_ATTRIBUTE_PRIMARY = UniqueKeys0.KEY_T_LECTURER_ATTRIBUTE_PRIMARY;
    public static final UniqueKey<LecturerCourseConfigRecord> KEY_T_LECTURER_COURSE_CONFIG_PRIMARY = UniqueKeys0.KEY_T_LECTURER_COURSE_CONFIG_PRIMARY;
    public static final UniqueKey<LecturerLabelRecord> KEY_T_LECTURER_LABEL_PRIMARY = UniqueKeys0.KEY_T_LECTURER_LABEL_PRIMARY;
    public static final UniqueKey<LecturerReleaseAuthorityRecord> KEY_T_LECTURER_RELEASE_AUTHORITY_PRIMARY = UniqueKeys0.KEY_T_LECTURER_RELEASE_AUTHORITY_PRIMARY;
    public static final UniqueKey<LecturerThumbsUpRecord> KEY_T_LECTURER_THUMBS_UP_PRIMARY = UniqueKeys0.KEY_T_LECTURER_THUMBS_UP_PRIMARY;
    public static final UniqueKey<LevelRecord> KEY_T_LEVEL_PRIMARY = UniqueKeys0.KEY_T_LEVEL_PRIMARY;
    public static final UniqueKey<LimitConfigurationRecord> KEY_T_LIMIT_CONFIGURATION_PRIMARY = UniqueKeys0.KEY_T_LIMIT_CONFIGURATION_PRIMARY;
    public static final UniqueKey<LimitDefaultConfigurationRecord> KEY_T_LIMIT_DEFAULT_CONFIGURATION_PRIMARY = UniqueKeys0.KEY_T_LIMIT_DEFAULT_CONFIGURATION_PRIMARY;
    public static final UniqueKey<MemberRecord> KEY_T_MEMBER_PRIMARY = UniqueKeys0.KEY_T_MEMBER_PRIMARY;
    public static final UniqueKey<MemberConfigRecord> KEY_T_MEMBER_CONFIG_PRIMARY = UniqueKeys0.KEY_T_MEMBER_CONFIG_PRIMARY;
    public static final UniqueKey<MessageDetailRecord> KEY_T_MESSAGE_DETAIL_PRIMARY = UniqueKeys0.KEY_T_MESSAGE_DETAIL_PRIMARY;
    public static final UniqueKey<MessageRecordRecord> KEY_T_MESSAGE_RECORD_PRIMARY = UniqueKeys0.KEY_T_MESSAGE_RECORD_PRIMARY;
    public static final UniqueKey<OrganizationRecord> KEY_T_ORGANIZATION_PRIMARY = UniqueKeys0.KEY_T_ORGANIZATION_PRIMARY;
    public static final UniqueKey<OrganizationDetailRecord> KEY_T_ORGANIZATION_DETAIL_PRIMARY = UniqueKeys0.KEY_T_ORGANIZATION_DETAIL_PRIMARY;
    public static final UniqueKey<OrganizationTeachingRecord> KEY_T_ORGANIZATION_TEACHING_PRIMARY = UniqueKeys0.KEY_T_ORGANIZATION_TEACHING_PRIMARY;
    public static final UniqueKey<OtherTeachingRecord> KEY_T_OTHER_TEACHING_PRIMARY = UniqueKeys0.KEY_T_OTHER_TEACHING_PRIMARY;
    public static final UniqueKey<PccwResultRecord> KEY_T_PCCW_RESULT_PRIMARY = UniqueKeys0.KEY_T_PCCW_RESULT_PRIMARY;
    public static final UniqueKey<PositionRecord> KEY_T_POSITION_PRIMARY = UniqueKeys0.KEY_T_POSITION_PRIMARY;
    public static final UniqueKey<ProjectRecord> KEY_T_PROJECT_PRIMARY = UniqueKeys0.KEY_T_PROJECT_PRIMARY;
    public static final UniqueKey<ProjectApprovalRecord> KEY_T_PROJECT_APPROVAL_PRIMARY = UniqueKeys0.KEY_T_PROJECT_APPROVAL_PRIMARY;
    public static final UniqueKey<ProjectHistoryRecord> KEY_T_PROJECT_HISTORY_PRIMARY = UniqueKeys0.KEY_T_PROJECT_HISTORY_PRIMARY;
    public static final UniqueKey<ProjectOccupyRecord> KEY_T_PROJECT_OCCUPY_PRIMARY = UniqueKeys0.KEY_T_PROJECT_OCCUPY_PRIMARY;
    public static final UniqueKey<QuestionRecord> KEY_T_QUESTION_PRIMARY = UniqueKeys0.KEY_T_QUESTION_PRIMARY;
    public static final UniqueKey<QuestionnaireQuestionTypeRecord> KEY_T_QUESTIONNAIRE_QUESTION_TYPE_PRIMARY = UniqueKeys0.KEY_T_QUESTIONNAIRE_QUESTION_TYPE_PRIMARY;
    public static final UniqueKey<QuestionAttrRecord> KEY_T_QUESTION_ATTR_PRIMARY = UniqueKeys0.KEY_T_QUESTION_ATTR_PRIMARY;
    public static final UniqueKey<RedPavilionRecord> KEY_T_RED_PAVILION_PRIMARY = UniqueKeys0.KEY_T_RED_PAVILION_PRIMARY;

    public static final UniqueKey<ResearchAnswerRecordRecord> KEY_T_RESEARCH_ANSWER_RECORD_PRIMARY = UniqueKeys0.KEY_T_RESEARCH_ANSWER_RECORD_PRIMARY;
    public static final UniqueKey<ResearchQuestionaryRecord> KEY_T_RESEARCH_QUESTIONARY_PRIMARY = UniqueKeys0.KEY_T_RESEARCH_QUESTIONARY_PRIMARY;
    public static final UniqueKey<ResearchRecordRecord> KEY_T_RESEARCH_RECORD_PRIMARY = UniqueKeys0.KEY_T_RESEARCH_RECORD_PRIMARY;
    public static final UniqueKey<SalaryHistoryRecord> KEY_T_SALARY_HISTORY_PRIMARY = UniqueKeys0.KEY_T_SALARY_HISTORY_PRIMARY;
    public static final UniqueKey<SettlementRecord> KEY_T_SETTLEMENT_PRIMARY = UniqueKeys0.KEY_T_SETTLEMENT_PRIMARY;
    public static final UniqueKey<SettlementConfigurationRecord> KEY_T_SETTLEMENT_CONFIGURATION_PRIMARY = UniqueKeys0.KEY_T_SETTLEMENT_CONFIGURATION_PRIMARY;
    public static final UniqueKey<SettlementConfigurationValueRecord> KEY_T_SETTLEMENT_CONFIGURATION_VALUE_PRIMARY = UniqueKeys0.KEY_T_SETTLEMENT_CONFIGURATION_VALUE_PRIMARY;
    public static final UniqueKey<SignRecord> KEY_T_SIGN_PRIMARY = UniqueKeys0.KEY_T_SIGN_PRIMARY;
    public static final UniqueKey<SignDetailRecord> KEY_T_SIGN_DETAIL_PRIMARY = UniqueKeys0.KEY_T_SIGN_DETAIL_PRIMARY;
    public static final UniqueKey<SignLeaveRecord> KEY_T_SIGN_LEAVE_PRIMARY = UniqueKeys0.KEY_T_SIGN_LEAVE_PRIMARY;
    public static final UniqueKey<StudentDetailRecord> KEY_T_STUDENT_DETAIL_PRIMARY = UniqueKeys0.KEY_T_STUDENT_DETAIL_PRIMARY;
    public static final UniqueKey<StudentDetailTotalRecord> KEY_T_STUDENT_DETAIL_TOTAL_PRIMARY = UniqueKeys0.KEY_T_STUDENT_DETAIL_TOTAL_PRIMARY;
    public static final UniqueKey<StudentDetailTotalSortRecord> KEY_T_STUDENT_DETAIL_TOTAL_SORT_PRIMARY = UniqueKeys0.KEY_T_STUDENT_DETAIL_TOTAL_SORT_PRIMARY;
    public static final UniqueKey<StudentHistoryRecord> KEY_T_STUDENT_HISTORY_PRIMARY = UniqueKeys0.KEY_T_STUDENT_HISTORY_PRIMARY;
    public static final UniqueKey<TaskRecord> KEY_T_TASK_PRIMARY = UniqueKeys0.KEY_T_TASK_PRIMARY;
    public static final UniqueKey<TaskApprovalRecord> KEY_T_TASK_APPROVAL_PRIMARY = UniqueKeys0.KEY_T_TASK_APPROVAL_PRIMARY;
    public static final UniqueKey<TaskAttachRecord> KEY_T_TASK_ATTACH_PRIMARY = UniqueKeys0.KEY_T_TASK_ATTACH_PRIMARY;
    public static final UniqueKey<TaskMemberRecord> KEY_T_TASK_MEMBER_PRIMARY = UniqueKeys0.KEY_T_TASK_MEMBER_PRIMARY;
    public static final UniqueKey<TaskReviewerRecord> KEY_T_TASK_REVIEWER_PRIMARY = UniqueKeys0.KEY_T_TASK_REVIEWER_PRIMARY;
    public static final UniqueKey<TraineeRecord> KEY_T_TRAINEE_PRIMARY = UniqueKeys0.KEY_T_TRAINEE_PRIMARY;
    public static final UniqueKey<TraineeRecord> KEY_T_TRAINEE_UNIQUE_T_TRAIN_SECTION_P_MEMBER_TYPE_SECTION = UniqueKeys0.KEY_T_TRAINEE_UNIQUE_T_TRAIN_SECTION_P_MEMBER_TYPE_SECTION;
    public static final UniqueKey<TraineeGroupRecord> KEY_T_TRAINEE_GROUP_PRIMARY = UniqueKeys0.KEY_T_TRAINEE_GROUP_PRIMARY;
    public static final UniqueKey<StudyTeamRecord> KEY_T_STUDY_TEAM_PRIMARY = UniqueKeys0.KEY_T_STUDY_TEAM_PRIMARY;
    public static final UniqueKey<StudyTeamActivityRecord> KEY_T_STUDY_TEAM_ACTIVITY_PRIMARY = UniqueKeys0.KEY_T_STUDY_TEAM_ACTIVITY_PRIMARY;
    public static final UniqueKey<StudyTeamActivityTaskRecord> KEY_T_STUDY_TEAM_ACTIVITY_TASK_PRIMARY = UniqueKeys0.KEY_T_STUDY_TEAM_ACTIVITY_TASK_PRIMARY;
    public static final UniqueKey<StudyTeamMemberRecord> KEY_T_STUDY_TEAM_MEMBER_PRIMARY = UniqueKeys0.KEY_T_STUDY_TEAM_MEMBER_PRIMARY;
    public static final UniqueKey<StudyTeamMemberSignRecord> KEY_T_STUDY_TEAM_MEMBER_SIGN_PRIMARY = UniqueKeys0.KEY_T_STUDY_TEAM_MEMBER_SIGN_PRIMARY;
    public static final UniqueKey<StudyTeamMemberSignLogRecord> KEY_T_STUDY_TEAM_MEMBER_SIGN_LOG_PRIMARY = UniqueKeys0.KEY_T_STUDY_TEAM_MEMBER_SIGN_LOG_PRIMARY;
    public static final UniqueKey<StudyTeamAchievementRecord> KEY_T_STUDY_TEAM_ACHIEVEMENT_PRIMARY = UniqueKeys0.KEY_T_STUDY_TEAM_ACHIEVEMENT_PRIMARY;
    public static final UniqueKey<StudyTeamAchievementPraiseRecord> KEY_T_STUDY_TEAM_ACHIEVEMENT_PRAISE_PRIMARY = UniqueKeys0.KEY_T_STUDY_TEAM_ACHIEVEMENT_PRAISE_PRIMARY;
    public static final UniqueKey<StudyTeamAchievementReplyRecord> KEY_T_STUDY_TEAM_ACHIEVEMENT_REPLY_PRIMARY = UniqueKeys0.KEY_T_STUDY_TEAM_ACHIEVEMENT_REPLY_PRIMARY;
    public static final UniqueKey<StudyTeamActivityAttachmentRecord> KEY_T_STUDY_TEAM_ACTIVITY_ATTACHMENT_PRIMARY = UniqueKeys0.KEY_T_STUDY_TEAM_ACTIVITY_ATTACHMENT_PRIMARY;
    public static final UniqueKey<SettlementMemberQuantityRecord> KEY_T_SETTLEMENT_MEMBER_QUANTITY_PRIMARY = UniqueKeys0.KEY_T_SETTLEMENT_MEMBER_QUANTITY_PRIMARY;
    public static final UniqueKey<StudyTeamLeaderConfirmDetailRecord> KEY_T_STUDY_TEAM_LEADER_CONFIRM_DETAIL_PRIMARY = UniqueKeys0.KEY_T_STUDY_TEAM_LEADER_CONFIRM_DETAIL_PRIMARY;
    public static final UniqueKey<DeleteDataTrainRecord> KEY_T_DELETE_DATA_TRAIN_PRIMARY = UniqueKeys0.KEY_T_DELETE_DATA_TRAIN_PRIMARY;
    public static final UniqueKey<ClassGradesProjectRecord> KEY_T_CLASS_GRADES_PROJECT_PRIMARY = UniqueKeys0.KEY_T_CLASS_GRADES_PROJECT_PRIMARY;
    public static final UniqueKey<ClassGradesProjectMemberRecord> KEY_T_CLASS_GRADES_PROJECT_MEMBER_PRIMARY = UniqueKeys0.KEY_T_CLASS_GRADES_PROJECT_MEMBER_PRIMARY;
    public static final UniqueKey<TrainingTypeRecord> KEY_T_TRAINING_TYPE_PRIMARY = UniqueKeys0.KEY_T_TRAINING_TYPE_PRIMARY;
    public static final UniqueKey<SolutionConfigurationRecord> KEY_T_SOLUTION_CONFIGURATION_PRIMARY = UniqueKeys0.KEY_T_SOLUTION_CONFIGURATION_PRIMARY;
    public static final UniqueKey<TrainProjectRecord> KEY_T_TRAIN_PROJECT_PRIMARY = UniqueKeys0.KEY_T_TRAIN_PROJECT_PRIMARY;
    public static final UniqueKey<PlanningImplementationRecord> KEY_T_PLANNING_IMPLEMENTATION_PRIMARY = UniqueKeys0.KEY_T_PLANNING_IMPLEMENTATION_PRIMARY;
    public static final UniqueKey<PlanningImplementationRelatedRecord> KEY_T_PLANNING_IMPLEMENTATION_RELATED_PRIMARY = UniqueKeys0.KEY_T_PLANNING_IMPLEMENTATION_RELATED_PRIMARY;

    public static final UniqueKey<TrainChatGroupRecord> KEY_T_TRAIN_CHAT_GROUP_PRIMARY = UniqueKeys0.KEY_T_TRAIN_CHAT_GROUP_PRIMARY;

    public static final UniqueKey<TrainChatGroupInfoRecord> KEY_T_TRAIN_CHAT_GROUP_INFO_PRIMARY = UniqueKeys0.KEY_T_TRAIN_CHAT_GROUP_INFO_PRIMARY;

    public static final UniqueKey<ClassResearchSatisfactionRecord> KEY_T_CLASS_RESEARCH_SATISFACTION_PRIMARY = UniqueKeys0.KEY_T_CLASS_RESEARCH_SATISFACTION_PRIMARY;
    public static final UniqueKey<AcademicStatisticsInfoRecord> KEY_T_ACADEMIC_STATISTICS_INFO_PRIMARY = UniqueKeys0.KEY_T_ACADEMIC_STATISTICS_INFO_PRIMARY;
    public static final UniqueKey<StudyReportTrain_2025Record> KEY_T_STUDY_REPORT_TRAIN_2025_PRIMARY = UniqueKeys0.KEY_T_STUDY_REPORT_TRAIN_2025_PRIMARY;
    public static final UniqueKey<StudyReportTrain_2026Record> KEY_T_STUDY_REPORT_TRAIN_2026_PRIMARY = UniqueKeys0.KEY_T_STUDY_REPORT_TRAIN_2026_PRIMARY;
    public static final UniqueKey<StudyReportTrain_2027Record> KEY_T_STUDY_REPORT_TRAIN_2027_PRIMARY = UniqueKeys0.KEY_T_STUDY_REPORT_TRAIN_2027_PRIMARY;
    public static final UniqueKey<StudyReportTrain_2028Record> KEY_T_STUDY_REPORT_TRAIN_2028_PRIMARY = UniqueKeys0.KEY_T_STUDY_REPORT_TRAIN_2028_PRIMARY;
    public static final UniqueKey<StudyReportTrain_2029Record> KEY_T_STUDY_REPORT_TRAIN_2029_PRIMARY = UniqueKeys0.KEY_T_STUDY_REPORT_TRAIN_2029_PRIMARY;
    public static final UniqueKey<ClassPopMangementRecord> KEY_T_CLASS_POP_MANGEMENT_PRIMARY = UniqueKeys0.KEY_T_CLASS_POP_MANGEMENT_PRIMARY;
    // -------------------------------------------------------------------------
    // FOREIGN KEY definitions
    // -------------------------------------------------------------------------


    // -------------------------------------------------------------------------
    // [#1459] distribute members to avoid static initialisers > 64kb
    // -------------------------------------------------------------------------

    private static class UniqueKeys0 extends AbstractKeys {
        public static final UniqueKey<StudyTeamActivityPhotosRecord> KEY_T_STUDY_TEAM_ACTIVITY_PHOTOS_PRIMARY = createUniqueKey(StudyTeamActivityPhotos.STUDY_TEAM_ACTIVITY_PHOTOS, "KEY_t_study_team_activity_photos_PRIMARY", StudyTeamActivityPhotos.STUDY_TEAM_ACTIVITY_PHOTOS.ID);
        public static final UniqueKey<AlbumRecord> KEY_T_ALBUM_PRIMARY = createUniqueKey(Album.ALBUM, "KEY_t_album_PRIMARY", Album.ALBUM.ID);
        public static final UniqueKey<AudienceItemRecord> KEY_T_AUDIENCE_ITEM_PRIMARY = createUniqueKey(AudienceItem.AUDIENCE_ITEM, "KEY_t_audience_item_PRIMARY", AudienceItem.AUDIENCE_ITEM.ID);
        public static final UniqueKey<AudienceMemberRecord> KEY_T_AUDIENCE_MEMBER_PRIMARY = createUniqueKey(AudienceMember.AUDIENCE_MEMBER, "KEY_t_audience_member_PRIMARY", AudienceMember.AUDIENCE_MEMBER.ID);
        public static final UniqueKey<AudienceObjectRecord> KEY_T_AUDIENCE_OBJECT_PRIMARY = createUniqueKey(AudienceObject.AUDIENCE_OBJECT, "KEY_t_audience_object_PRIMARY", AudienceObject.AUDIENCE_OBJECT.ID);
        public static final UniqueKey<BusRecord> KEY_T_BUS_PRIMARY = createUniqueKey(Bus.BUS, "KEY_t_bus_PRIMARY", Bus.BUS.ID);
        public static final UniqueKey<BusDetailRecord> KEY_T_BUS_DETAIL_PRIMARY = createUniqueKey(BusDetail.BUS_DETAIL, "KEY_t_bus_detail_PRIMARY", BusDetail.BUS_DETAIL.ID);
        public static final UniqueKey<BusOptionRecord> KEY_T_BUS_OPTION_PRIMARY = createUniqueKey(BusOption.BUS_OPTION, "KEY_t_bus_option_PRIMARY", BusOption.BUS_OPTION.ID);
        public static final UniqueKey<CenterConfigurationRecord> KEY_T_CENTER_CONFIGURATION_PRIMARY = createUniqueKey(CenterConfiguration.CENTER_CONFIGURATION, "KEY_t_center_configuration_PRIMARY", CenterConfiguration.CENTER_CONFIGURATION.ID);
        public static final UniqueKey<ClassroomConfigurationRecord> KEY_T_CLASSROOM_CONFIGURATION_PRIMARY = createUniqueKey(ClassroomConfiguration.CLASSROOM_CONFIGURATION, "KEY_t_classroom_configuration_PRIMARY", ClassroomConfiguration.CLASSROOM_CONFIGURATION.ID);
        public static final UniqueKey<ClassstaffClassRecord> KEY_T_CLASSSTAFF_CLASS_PRIMARY = createUniqueKey(ClassstaffClass.CLASSSTAFF_CLASS, "KEY_t_classstaff_class_PRIMARY", ClassstaffClass.CLASSSTAFF_CLASS.ID);
        public static final UniqueKey<ClassstaffConfigurationRecord> KEY_T_CLASSSTAFF_CONFIGURATION_PRIMARY = createUniqueKey(ClassstaffConfiguration.CLASSSTAFF_CONFIGURATION, "KEY_t_classstaff_configuration_PRIMARY", ClassstaffConfiguration.CLASSSTAFF_CONFIGURATION.ID);
        public static final UniqueKey<ClassBusinessProgressRecord> KEY_T_CLASS_BUSINESS_PROGRESS_PRIMARY = createUniqueKey(ClassBusinessProgress.CLASS_BUSINESS_PROGRESS, "KEY_t_class_business_progress_PRIMARY", ClassBusinessProgress.CLASS_BUSINESS_PROGRESS.ID);
        public static final UniqueKey<ClassCourseHistoryRecord> KEY_T_CLASS_COURSE_HISTORY_PRIMARY = createUniqueKey(ClassCourseHistory.CLASS_COURSE_HISTORY, "KEY_t_class_course_history_PRIMARY", ClassCourseHistory.CLASS_COURSE_HISTORY.ID);
        public static final UniqueKey<ClassDetailRecord> KEY_T_CLASS_DETAIL_PRIMARY = createUniqueKey(ClassDetail.CLASS_DETAIL, "KEY_t_class_detail_PRIMARY", ClassDetail.CLASS_DETAIL.ID);
        public static final UniqueKey<ClassEvaluateRecord> KEY_T_CLASS_EVALUATE_PRIMARY = createUniqueKey(ClassEvaluate.CLASS_EVALUATE, "KEY_t_class_evaluate_PRIMARY", ClassEvaluate.CLASS_EVALUATE.ID);
        public static final UniqueKey<ClassGroupRecord> KEY_T_CLASS_GROUP_PRIMARY = createUniqueKey(ClassGroup.CLASS_GROUP, "KEY_t_class_group_PRIMARY", ClassGroup.CLASS_GROUP.ID);
        public static final UniqueKey<ClassHistoryRecord> KEY_T_CLASS_HISTORY_PRIMARY = createUniqueKey(ClassHistory.CLASS_HISTORY, "KEY_t_class_history_PRIMARY", ClassHistory.CLASS_HISTORY.ID);
        public static final UniqueKey<ClassInfoRecord> KEY_T_CLASS_INFO_PRIMARY = createUniqueKey(ClassInfo.CLASS_INFO, "KEY_t_class_info_PRIMARY", ClassInfo.CLASS_INFO.ID);
        public static final UniqueKey<ClassOfflineCourseRecord> KEY_T_CLASS_OFFLINE_COURSE_PRIMARY = createUniqueKey(ClassOfflineCourse.CLASS_OFFLINE_COURSE, "KEY_t_class_offline_course_PRIMARY", ClassOfflineCourse.CLASS_OFFLINE_COURSE.ID);
        public static final UniqueKey<ClassOnlineCourseRecord> KEY_T_CLASS_ONLINE_COURSE_PRIMARY = createUniqueKey(ClassOnlineCourse.CLASS_ONLINE_COURSE, "KEY_t_class_online_course_PRIMARY", ClassOnlineCourse.CLASS_ONLINE_COURSE.ID);
        public static final UniqueKey<ClassQuestionnaireTotalRecord> KEY_T_CLASS_QUESTIONNAIRE_TOTAL_PRIMARY = createUniqueKey(ClassQuestionnaireTotal.CLASS_QUESTIONNAIRE_TOTAL, "KEY_t_class_questionnaire_total_PRIMARY", ClassQuestionnaireTotal.CLASS_QUESTIONNAIRE_TOTAL.ID);
        public static final UniqueKey<ClassQuotaRecord> KEY_T_CLASS_QUOTA_PRIMARY = createUniqueKey(ClassQuota.CLASS_QUOTA, "KEY_t_class_quota_PRIMARY", ClassQuota.CLASS_QUOTA.ID);
        public static final UniqueKey<ClassQuotaDetailRecord> KEY_T_CLASS_QUOTA_DETAIL_PRIMARY = createUniqueKey(ClassQuotaDetail.CLASS_QUOTA_DETAIL, "KEY_t_class_quota_detail_PRIMARY", ClassQuotaDetail.CLASS_QUOTA_DETAIL.ID);
        public static final UniqueKey<ClassRequiredRecord> KEY_T_CLASS_REQUIRED_PRIMARY = createUniqueKey(ClassRequired.CLASS_REQUIRED, "KEY_t_class_required_PRIMARY", ClassRequired.CLASS_REQUIRED.ID);
        public static final UniqueKey<ClassRequiredThemeRecord> KEY_T_CLASS_REQUIRED_THEME_PRIMARY = createUniqueKey(ClassRequiredTheme.CLASS_REQUIRED_THEME, "KEY_t_class_required_theme_PRIMARY", ClassRequiredTheme.CLASS_REQUIRED_THEME.ID);
        public static final UniqueKey<ClassResourceRecord> KEY_T_CLASS_RESOURCE_PRIMARY = createUniqueKey(ClassResource.CLASS_RESOURCE, "KEY_t_class_resource_PRIMARY", ClassResource.CLASS_RESOURCE.ID);
        public static final UniqueKey<ClassSignupInfoRecord> KEY_T_CLASS_SIGNUP_INFO_PRIMARY = createUniqueKey(ClassSignupInfo.CLASS_SIGNUP_INFO, "KEY_t_class_signup_info_PRIMARY", ClassSignupInfo.CLASS_SIGNUP_INFO.ID);
        public static final UniqueKey<ClassStatisticsRecord> KEY_T_CLASS_STATISTICS_PRIMARY = createUniqueKey(ClassStatistics.CLASS_STATISTICS, "KEY_t_class_statistics_PRIMARY", ClassStatistics.CLASS_STATISTICS.ID);
        public static final UniqueKey<ClassThemeRecord> KEY_T_CLASS_THEME_PRIMARY = createUniqueKey(ClassTheme.CLASS_THEME, "KEY_t_class_theme_PRIMARY", ClassTheme.CLASS_THEME.ID);
        public static final UniqueKey<ClassTwoBringsRecord> KEY_T_CLASS_TWO_BRINGS_PRIMARY = createUniqueKey(ClassTwoBrings.CLASS_TWO_BRINGS, "KEY_t_class_two_brings_PRIMARY", ClassTwoBrings.CLASS_TWO_BRINGS.ID);
        public static final UniqueKey<CollectingCourseRecord> KEY_T_COLLECTING_COURSE_PRIMARY = createUniqueKey(CollectingCourse.COLLECTING_COURSE, "KEY_t_collecting_course_PRIMARY", CollectingCourse.COLLECTING_COURSE.ID);
        public static final UniqueKey<CollectionProgrammeConfigRecord> KEY_T_COLLECTION_PROGRAMME_CONFIG_PRIMARY = createUniqueKey(CollectionProgrammeConfig.COLLECTION_PROGRAMME_CONFIG, "KEY_t_collection_programme_config_PRIMARY", CollectionProgrammeConfig.COLLECTION_PROGRAMME_CONFIG.ID);
        public static final UniqueKey<CollectionProgrammeCourseRecord> KEY_T_COLLECTION_PROGRAMME_COURSE_PRIMARY = createUniqueKey(CollectionProgrammeCourse.COLLECTION_PROGRAMME_COURSE, "KEY_t_collection_programme_course_PRIMARY", CollectionProgrammeCourse.COLLECTION_PROGRAMME_COURSE.ID);
        public static final UniqueKey<CollegeTeachingRecord> KEY_T_COLLEGE_TEACHING_PRIMARY = createUniqueKey(CollegeTeaching.COLLEGE_TEACHING, "KEY_t_college_teaching_PRIMARY", CollegeTeaching.COLLEGE_TEACHING.ID);
        public static final UniqueKey<ConfigruationHistoryRecord> KEY_T_CONFIGRUATION_HISTORY_PRIMARY = createUniqueKey(ConfigruationHistory.CONFIGRUATION_HISTORY, "KEY_t_configruation_history_PRIMARY", ConfigruationHistory.CONFIGRUATION_HISTORY.ID);
        public static final UniqueKey<ConfigurationRecord> KEY_T_CONFIGURATION_PRIMARY = createUniqueKey(Configuration.CONFIGURATION, "KEY_t_configuration_PRIMARY", Configuration.CONFIGURATION.ID);
        public static final UniqueKey<ConfigurationValueRecord> KEY_T_CONFIGURATION_VALUE_PRIMARY = createUniqueKey(ConfigurationValue.CONFIGURATION_VALUE, "KEY_t_configuration_value_PRIMARY", ConfigurationValue.CONFIGURATION_VALUE.ID);
        public static final UniqueKey<CorporateSegmentRelationRecord> KEY_T_CORPORATE_SEGMENT_RELATION_PRIMARY = createUniqueKey(CorporateSegmentRelation.CORPORATE_SEGMENT_RELATION, "KEY_t_corporate_segment_relation_PRIMARY", CorporateSegmentRelation.CORPORATE_SEGMENT_RELATION.ID);
        public static final UniqueKey<CourseAttachRecord> KEY_T_COURSE_ATTACH_PRIMARY = createUniqueKey(CourseAttach.COURSE_ATTACH, "KEY_t_course_attach_PRIMARY", CourseAttach.COURSE_ATTACH.ID);
        public static final UniqueKey<CourseAttributeRecord> KEY_T_COURSE_ATTRIBUTE_PRIMARY = createUniqueKey(CourseAttribute.COURSE_ATTRIBUTE, "KEY_t_course_attribute_PRIMARY", CourseAttribute.COURSE_ATTRIBUTE.ID);
        public static final UniqueKey<CourseCategoryRecord> KEY_T_COURSE_CATEGORY_PRIMARY = createUniqueKey(CourseCategory.COURSE_CATEGORY, "KEY_t_course_category_PRIMARY", CourseCategory.COURSE_CATEGORY.ID);
        public static final UniqueKey<CourseInfoRecord> KEY_T_COURSE_INFO_PRIMARY = createUniqueKey(CourseInfo.COURSE_INFO, "KEY_t_course_info_PRIMARY", CourseInfo.COURSE_INFO.ID);
        public static final UniqueKey<CourseSalaryRecord> KEY_T_COURSE_SALARY_PRIMARY = createUniqueKey(CourseSalary.COURSE_SALARY, "KEY_t_course_salary_PRIMARY", CourseSalary.COURSE_SALARY.ID);
        public static final UniqueKey<CourseTeachingActivitiesRecord> KEY_T_COURSE_TEACHING_ACTIVITIES_PRIMARY = createUniqueKey(CourseTeachingActivities.COURSE_TEACHING_ACTIVITIES, "KEY_t_course_teaching_activities_PRIMARY", CourseTeachingActivities.COURSE_TEACHING_ACTIVITIES.ID);
        public static final UniqueKey<DemandSideOrganizationRecord> KEY_T_DEMAND_SIDE_ORGANIZATION_PRIMARY = createUniqueKey(DemandSideOrganization.DEMAND_SIDE_ORGANIZATION, "KEY_t_demand_side_organization_PRIMARY", DemandSideOrganization.DEMAND_SIDE_ORGANIZATION.ID);
        public static final UniqueKey<DimensionRecord> KEY_T_DIMENSION_PRIMARY = createUniqueKey(Dimension.DIMENSION, "KEY_t_dimension_PRIMARY", Dimension.DIMENSION.ID);
        public static final UniqueKey<DimensionQuestionRecord> KEY_T_DIMENSION_QUESTION_PRIMARY = createUniqueKey(DimensionQuestion.DIMENSION_QUESTION, "KEY_t_dimension_question_PRIMARY", DimensionQuestion.DIMENSION_QUESTION.ID);
        public static final UniqueKey<EvaluateRecord> KEY_T_EVALUATE_PRIMARY = createUniqueKey(Evaluate.EVALUATE, "KEY_t_evaluate_PRIMARY", Evaluate.EVALUATE.ID);
        public static final UniqueKey<F2fCourseRecord> KEY_T_F2F_COURSE_PRIMARY = createUniqueKey(F2fCourse.F2F_COURSE, "KEY_t_f2f_course_PRIMARY", F2fCourse.F2F_COURSE.ID);
        public static final UniqueKey<F2fCourseLibraryRecord> KEY_T_F2F_COURSE_LIBRARY_PRIMARY = createUniqueKey(F2fCourseLibrary.F2F_COURSE_LIBRARY, "KEY_t_f2f_course_library_PRIMARY", F2fCourseLibrary.F2F_COURSE_LIBRARY.ID);
        public static final UniqueKey<GrantDetailRecord> KEY_T_GRANT_DETAIL_PRIMARY = createUniqueKey(GrantDetail.GRANT_DETAIL, "KEY_t_grant_detail_PRIMARY", GrantDetail.GRANT_DETAIL.ID);
        public static final UniqueKey<GroupConfigurationRecord> KEY_T_GROUP_CONFIGURATION_PRIMARY = createUniqueKey(GroupConfiguration.GROUP_CONFIGURATION, "KEY_t_group_configuration_PRIMARY", GroupConfiguration.GROUP_CONFIGURATION.ID);
        public static final UniqueKey<GroupConfigurationValueRecord> KEY_T_GROUP_CONFIGURATION_VALUE_PRIMARY = createUniqueKey(GroupConfigurationValue.GROUP_CONFIGURATION_VALUE, "KEY_t_group_configuration_value_PRIMARY", GroupConfigurationValue.GROUP_CONFIGURATION_VALUE.ID);
        public static final UniqueKey<HomeLecturerRecord> KEY_T_HOME_LECTURER_PRIMARY = createUniqueKey(HomeLecturer.HOME_LECTURER, "KEY_t_home_lecturer_PRIMARY", HomeLecturer.HOME_LECTURER.ID);
        public static final UniqueKey<JobRecord> KEY_T_JOB_PRIMARY = createUniqueKey(Job.JOB, "KEY_t_job_PRIMARY", Job.JOB.ID);
        public static final UniqueKey<LabelRecord> KEY_T_LABEL_PRIMARY = createUniqueKey(Label.LABEL, "KEY_t_label_PRIMARY", Label.LABEL.ID);
        public static final UniqueKey<LecturerRecord> KEY_T_LECTURER_PRIMARY = createUniqueKey(Lecturer.LECTURER, "KEY_t_lecturer_PRIMARY", Lecturer.LECTURER.ID);
        public static final UniqueKey<LecturerAdeptCourseRecord> KEY_T_LECTURER_ADEPT_COURSE_PRIMARY = createUniqueKey(LecturerAdeptCourse.LECTURER_ADEPT_COURSE, "KEY_t_lecturer_adept_course_PRIMARY", LecturerAdeptCourse.LECTURER_ADEPT_COURSE.ID);
        public static final UniqueKey<LecturerAttributeRecord> KEY_T_LECTURER_ATTRIBUTE_PRIMARY = createUniqueKey(LecturerAttribute.LECTURER_ATTRIBUTE, "KEY_t_lecturer_attribute_PRIMARY", LecturerAttribute.LECTURER_ATTRIBUTE.ID);
        public static final UniqueKey<LecturerCourseConfigRecord> KEY_T_LECTURER_COURSE_CONFIG_PRIMARY = createUniqueKey(LecturerCourseConfig.LECTURER_COURSE_CONFIG, "KEY_t_lecturer_course_config_PRIMARY", LecturerCourseConfig.LECTURER_COURSE_CONFIG.ID);
        public static final UniqueKey<LecturerLabelRecord> KEY_T_LECTURER_LABEL_PRIMARY = createUniqueKey(LecturerLabel.LECTURER_LABEL, "KEY_t_lecturer_label_PRIMARY", LecturerLabel.LECTURER_LABEL.ID);
        public static final UniqueKey<LecturerReleaseAuthorityRecord> KEY_T_LECTURER_RELEASE_AUTHORITY_PRIMARY = createUniqueKey(LecturerReleaseAuthority.LECTURER_RELEASE_AUTHORITY, "KEY_t_lecturer_release_authority_PRIMARY", LecturerReleaseAuthority.LECTURER_RELEASE_AUTHORITY.ID);
        public static final UniqueKey<LecturerThumbsUpRecord> KEY_T_LECTURER_THUMBS_UP_PRIMARY = createUniqueKey(LecturerThumbsUp.LECTURER_THUMBS_UP, "KEY_t_lecturer_thumbs_up_PRIMARY", LecturerThumbsUp.LECTURER_THUMBS_UP.ID);
        public static final UniqueKey<LevelRecord> KEY_T_LEVEL_PRIMARY = createUniqueKey(Level.LEVEL, "KEY_t_level_PRIMARY", Level.LEVEL.ID);
        public static final UniqueKey<LimitConfigurationRecord> KEY_T_LIMIT_CONFIGURATION_PRIMARY = createUniqueKey(LimitConfiguration.LIMIT_CONFIGURATION, "KEY_t_limit_configuration_PRIMARY", LimitConfiguration.LIMIT_CONFIGURATION.ID);
        public static final UniqueKey<LimitDefaultConfigurationRecord> KEY_T_LIMIT_DEFAULT_CONFIGURATION_PRIMARY = createUniqueKey(LimitDefaultConfiguration.LIMIT_DEFAULT_CONFIGURATION, "KEY_t_limit_default_configuration_PRIMARY", LimitDefaultConfiguration.LIMIT_DEFAULT_CONFIGURATION.ID);
        public static final UniqueKey<MemberRecord> KEY_T_MEMBER_PRIMARY = createUniqueKey(Member.MEMBER, "KEY_t_member_PRIMARY", Member.MEMBER.ID);
        public static final UniqueKey<MemberConfigRecord> KEY_T_MEMBER_CONFIG_PRIMARY = createUniqueKey(MemberConfig.MEMBER_CONFIG, "KEY_t_member_config_PRIMARY", MemberConfig.MEMBER_CONFIG.ID);
        public static final UniqueKey<MessageDetailRecord> KEY_T_MESSAGE_DETAIL_PRIMARY = createUniqueKey(MessageDetail.MESSAGE_DETAIL, "KEY_t_message_detail_PRIMARY", MessageDetail.MESSAGE_DETAIL.ID);
        public static final UniqueKey<MessageRecordRecord> KEY_T_MESSAGE_RECORD_PRIMARY = createUniqueKey(MessageRecord.MESSAGE_RECORD, "KEY_t_message_record_PRIMARY", MessageRecord.MESSAGE_RECORD.ID);
        public static final UniqueKey<OrganizationRecord> KEY_T_ORGANIZATION_PRIMARY = createUniqueKey(Organization.ORGANIZATION, "KEY_t_organization_PRIMARY", Organization.ORGANIZATION.ID);
        public static final UniqueKey<OrganizationDetailRecord> KEY_T_ORGANIZATION_DETAIL_PRIMARY = createUniqueKey(OrganizationDetail.ORGANIZATION_DETAIL, "KEY_t_organization_detail_PRIMARY", OrganizationDetail.ORGANIZATION_DETAIL.ID);
        public static final UniqueKey<OrganizationTeachingRecord> KEY_T_ORGANIZATION_TEACHING_PRIMARY = createUniqueKey(OrganizationTeaching.ORGANIZATION_TEACHING, "KEY_t_organization_teaching_PRIMARY", OrganizationTeaching.ORGANIZATION_TEACHING.ID);
        public static final UniqueKey<OtherTeachingRecord> KEY_T_OTHER_TEACHING_PRIMARY = createUniqueKey(OtherTeaching.OTHER_TEACHING, "KEY_t_other_teaching_PRIMARY", OtherTeaching.OTHER_TEACHING.ID);
        public static final UniqueKey<PccwResultRecord> KEY_T_PCCW_RESULT_PRIMARY = createUniqueKey(PccwResult.PCCW_RESULT, "KEY_t_pccw_result_PRIMARY", PccwResult.PCCW_RESULT.ID);
        public static final UniqueKey<PositionRecord> KEY_T_POSITION_PRIMARY = createUniqueKey(Position.POSITION, "KEY_t_position_PRIMARY", Position.POSITION.ID);
        public static final UniqueKey<ProjectRecord> KEY_T_PROJECT_PRIMARY = createUniqueKey(Project.PROJECT, "KEY_t_project_PRIMARY", Project.PROJECT.ID);
        public static final UniqueKey<ProjectApprovalRecord> KEY_T_PROJECT_APPROVAL_PRIMARY = createUniqueKey(ProjectApproval.PROJECT_APPROVAL, "KEY_t_project_approval_PRIMARY", ProjectApproval.PROJECT_APPROVAL.ID);
        public static final UniqueKey<ProjectHistoryRecord> KEY_T_PROJECT_HISTORY_PRIMARY = createUniqueKey(ProjectHistory.PROJECT_HISTORY, "KEY_t_project_history_PRIMARY", ProjectHistory.PROJECT_HISTORY.ID);
        public static final UniqueKey<ProjectOccupyRecord> KEY_T_PROJECT_OCCUPY_PRIMARY = createUniqueKey(ProjectOccupy.PROJECT_OCCUPY, "KEY_t_project_occupy_PRIMARY", ProjectOccupy.PROJECT_OCCUPY.ID);
        public static final UniqueKey<QuestionRecord> KEY_T_QUESTION_PRIMARY = createUniqueKey(Question.QUESTION, "KEY_t_question_PRIMARY", Question.QUESTION.ID);
        public static final UniqueKey<QuestionnaireQuestionTypeRecord> KEY_T_QUESTIONNAIRE_QUESTION_TYPE_PRIMARY = createUniqueKey(QuestionnaireQuestionType.QUESTIONNAIRE_QUESTION_TYPE, "KEY_t_questionnaire_question_type_PRIMARY", QuestionnaireQuestionType.QUESTIONNAIRE_QUESTION_TYPE.ID);
        public static final UniqueKey<QuestionAttrRecord> KEY_T_QUESTION_ATTR_PRIMARY = createUniqueKey(QuestionAttr.QUESTION_ATTR, "KEY_t_question_attr_PRIMARY", QuestionAttr.QUESTION_ATTR.ID);
        public static final UniqueKey<RedPavilionRecord> KEY_T_RED_PAVILION_PRIMARY = createUniqueKey(RedPavilion.RED_PAVILION, "KEY_t_red_pavilion_PRIMARY", RedPavilion.RED_PAVILION.ID);
        public static final UniqueKey<ResearchAnswerRecordRecord> KEY_T_RESEARCH_ANSWER_RECORD_PRIMARY = createUniqueKey(ResearchAnswerRecord.RESEARCH_ANSWER_RECORD, "KEY_t_research_answer_record_PRIMARY", ResearchAnswerRecord.RESEARCH_ANSWER_RECORD.ID);
        public static final UniqueKey<ResearchQuestionaryRecord> KEY_T_RESEARCH_QUESTIONARY_PRIMARY = createUniqueKey(ResearchQuestionary.RESEARCH_QUESTIONARY, "KEY_t_research_questionary_PRIMARY", ResearchQuestionary.RESEARCH_QUESTIONARY.ID);
        public static final UniqueKey<ResearchRecordRecord> KEY_T_RESEARCH_RECORD_PRIMARY = createUniqueKey(ResearchRecord.RESEARCH_RECORD, "KEY_t_research_record_PRIMARY", ResearchRecord.RESEARCH_RECORD.ID);
        public static final UniqueKey<SalaryHistoryRecord> KEY_T_SALARY_HISTORY_PRIMARY = createUniqueKey(SalaryHistory.SALARY_HISTORY, "KEY_t_salary_history_PRIMARY", SalaryHistory.SALARY_HISTORY.ID);
        public static final UniqueKey<SettlementRecord> KEY_T_SETTLEMENT_PRIMARY = createUniqueKey(Settlement.SETTLEMENT, "KEY_t_settlement_PRIMARY", Settlement.SETTLEMENT.ID);
        public static final UniqueKey<SettlementConfigurationRecord> KEY_T_SETTLEMENT_CONFIGURATION_PRIMARY = createUniqueKey(SettlementConfiguration.SETTLEMENT_CONFIGURATION, "KEY_t_settlement_configuration_PRIMARY", SettlementConfiguration.SETTLEMENT_CONFIGURATION.ID);
        public static final UniqueKey<SettlementConfigurationValueRecord> KEY_T_SETTLEMENT_CONFIGURATION_VALUE_PRIMARY = createUniqueKey(SettlementConfigurationValue.SETTLEMENT_CONFIGURATION_VALUE, "KEY_t_settlement_configuration_value_PRIMARY", SettlementConfigurationValue.SETTLEMENT_CONFIGURATION_VALUE.ID);
        public static final UniqueKey<SignRecord> KEY_T_SIGN_PRIMARY = createUniqueKey(Sign.SIGN, "KEY_t_sign_PRIMARY", Sign.SIGN.ID);
        public static final UniqueKey<SignDetailRecord> KEY_T_SIGN_DETAIL_PRIMARY = createUniqueKey(SignDetail.SIGN_DETAIL, "KEY_t_sign_detail_PRIMARY", SignDetail.SIGN_DETAIL.ID);
        public static final UniqueKey<SignLeaveRecord> KEY_T_SIGN_LEAVE_PRIMARY = createUniqueKey(SignLeave.SIGN_LEAVE, "KEY_t_sign_leave_PRIMARY", SignLeave.SIGN_LEAVE.ID);
        public static final UniqueKey<StudentDetailRecord> KEY_T_STUDENT_DETAIL_PRIMARY = createUniqueKey(StudentDetail.STUDENT_DETAIL, "KEY_t_student_detail_PRIMARY", StudentDetail.STUDENT_DETAIL.ID);
        public static final UniqueKey<StudentDetailTotalRecord> KEY_T_STUDENT_DETAIL_TOTAL_PRIMARY = createUniqueKey(StudentDetailTotal.STUDENT_DETAIL_TOTAL, "KEY_t_student_detail_total_PRIMARY", StudentDetailTotal.STUDENT_DETAIL_TOTAL.ID);
        public static final UniqueKey<StudentDetailTotalSortRecord> KEY_T_STUDENT_DETAIL_TOTAL_SORT_PRIMARY = createUniqueKey(StudentDetailTotalSort.STUDENT_DETAIL_TOTAL_SORT, "KEY_t_student_detail_total_sort_PRIMARY", StudentDetailTotalSort.STUDENT_DETAIL_TOTAL_SORT.ID);
        public static final UniqueKey<StudentHistoryRecord> KEY_T_STUDENT_HISTORY_PRIMARY = createUniqueKey(StudentHistory.STUDENT_HISTORY, "KEY_t_student_history_PRIMARY", StudentHistory.STUDENT_HISTORY.ID);
        public static final UniqueKey<TaskRecord> KEY_T_TASK_PRIMARY = createUniqueKey(Task.TASK, "KEY_t_task_PRIMARY", Task.TASK.ID);
        public static final UniqueKey<TaskApprovalRecord> KEY_T_TASK_APPROVAL_PRIMARY = createUniqueKey(TaskApproval.TASK_APPROVAL, "KEY_t_task_approval_PRIMARY", TaskApproval.TASK_APPROVAL.ID);
        public static final UniqueKey<TaskAttachRecord> KEY_T_TASK_ATTACH_PRIMARY = createUniqueKey(TaskAttach.TASK_ATTACH, "KEY_t_task_attach_PRIMARY", TaskAttach.TASK_ATTACH.ID);
        public static final UniqueKey<TaskMemberRecord> KEY_T_TASK_MEMBER_PRIMARY = createUniqueKey(TaskMember.TASK_MEMBER, "KEY_t_task_member_PRIMARY", TaskMember.TASK_MEMBER.ID);
        public static final UniqueKey<TaskReviewerRecord> KEY_T_TASK_REVIEWER_PRIMARY = createUniqueKey(TaskReviewer.TASK_REVIEWER, "KEY_t_task_reviewer_PRIMARY", TaskReviewer.TASK_REVIEWER.ID);
        public static final UniqueKey<TraineeRecord> KEY_T_TRAINEE_PRIMARY = createUniqueKey(Trainee.TRAINEE, "KEY_t_trainee_PRIMARY", Trainee.TRAINEE.ID);
        public static final UniqueKey<TraineeRecord> KEY_T_TRAINEE_UNIQUE_T_TRAIN_SECTION_P_MEMBER_TYPE_SECTION = createUniqueKey(Trainee.TRAINEE, "KEY_t_trainee_unique_t_train_section_p_member_type_section", Trainee.TRAINEE.MEMBER_ID, Trainee.TRAINEE.CLASS_ID, Trainee.TRAINEE.TYPE);
        public static final UniqueKey<TraineeGroupRecord> KEY_T_TRAINEE_GROUP_PRIMARY = createUniqueKey(TraineeGroup.TRAINEE_GROUP, "KEY_t_trainee_group_PRIMARY", TraineeGroup.TRAINEE_GROUP.ID);
        public static final UniqueKey<StudyTeamRecord> KEY_T_STUDY_TEAM_PRIMARY = createUniqueKey(StudyTeam.STUDY_TEAM, "KEY_t_study_team_PRIMARY", StudyTeam.STUDY_TEAM.ID);
        public static final UniqueKey<StudyTeamActivityRecord> KEY_T_STUDY_TEAM_ACTIVITY_PRIMARY = createUniqueKey(StudyTeamActivity.STUDY_TEAM_ACTIVITY, "KEY_t_study_team_activity_PRIMARY", StudyTeamActivity.STUDY_TEAM_ACTIVITY.ID);
        public static final UniqueKey<StudyTeamActivityTaskRecord> KEY_T_STUDY_TEAM_ACTIVITY_TASK_PRIMARY = createUniqueKey(StudyTeamActivityTask.STUDY_TEAM_ACTIVITY_TASK, "KEY_t_study_team_activity_task_PRIMARY", StudyTeamActivityTask.STUDY_TEAM_ACTIVITY_TASK.ID);
        public static final UniqueKey<StudyTeamMemberRecord> KEY_T_STUDY_TEAM_MEMBER_PRIMARY = createUniqueKey(StudyTeamMember.STUDY_TEAM_MEMBER, "KEY_t_study_team_member_PRIMARY", StudyTeamMember.STUDY_TEAM_MEMBER.ID);
        public static final UniqueKey<StudyTeamMemberSignRecord> KEY_T_STUDY_TEAM_MEMBER_SIGN_PRIMARY = createUniqueKey(StudyTeamMemberSign.STUDY_TEAM_MEMBER_SIGN, "KEY_t_study_team_member_sign_PRIMARY", StudyTeamMemberSign.STUDY_TEAM_MEMBER_SIGN.ID);
        public static final UniqueKey<StudyTeamMemberSignLogRecord> KEY_T_STUDY_TEAM_MEMBER_SIGN_LOG_PRIMARY = createUniqueKey(StudyTeamMemberSignLog.STUDY_TEAM_MEMBER_SIGN_LOG, "KEY_t_study_team_member_sign_log_PRIMARY", StudyTeamMemberSignLog.STUDY_TEAM_MEMBER_SIGN_LOG.ID);
        public static final UniqueKey<StudyTeamAchievementRecord> KEY_T_STUDY_TEAM_ACHIEVEMENT_PRIMARY = createUniqueKey(StudyTeamAchievement.STUDY_TEAM_ACHIEVEMENT, "KEY_t_study_team_achievement_PRIMARY", StudyTeamAchievement.STUDY_TEAM_ACHIEVEMENT.ID);
        public static final UniqueKey<StudyTeamAchievementPraiseRecord> KEY_T_STUDY_TEAM_ACHIEVEMENT_PRAISE_PRIMARY = createUniqueKey(StudyTeamAchievementPraise.STUDY_TEAM_ACHIEVEMENT_PRAISE, "KEY_t_study_team_achievement_praise_PRIMARY", StudyTeamAchievementPraise.STUDY_TEAM_ACHIEVEMENT_PRAISE.ID);
        public static final UniqueKey<StudyTeamAchievementReplyRecord> KEY_T_STUDY_TEAM_ACHIEVEMENT_REPLY_PRIMARY = createUniqueKey(StudyTeamAchievementReply.STUDY_TEAM_ACHIEVEMENT_REPLY, "KEY_t_study_team_achievement_reply_PRIMARY", StudyTeamAchievementReply.STUDY_TEAM_ACHIEVEMENT_REPLY.ID);
        public static final UniqueKey<StudyTeamActivityAttachmentRecord> KEY_T_STUDY_TEAM_ACTIVITY_ATTACHMENT_PRIMARY = createUniqueKey(StudyTeamActivityAttachment.STUDY_TEAM_ACTIVITY_ATTACHMENT, "KEY_t_study_team_activity_attachment_PRIMARY", StudyTeamActivityAttachment.STUDY_TEAM_ACTIVITY_ATTACHMENT.ID);
        public static final UniqueKey<SettlementMemberQuantityRecord> KEY_T_SETTLEMENT_MEMBER_QUANTITY_PRIMARY = createUniqueKey(SettlementMemberQuantity.SETTLEMENT_MEMBER_QUANTITY, "KEY_t_settlement_member_quantity_PRIMARY", SettlementMemberQuantity.SETTLEMENT_MEMBER_QUANTITY.ID);
        public static final UniqueKey<StudyTeamLeaderConfirmDetailRecord> KEY_T_STUDY_TEAM_LEADER_CONFIRM_DETAIL_PRIMARY = createUniqueKey(StudyTeamLeaderConfirmDetail.STUDY_TEAM_LEADER_CONFIRM_DETAIL, "KEY_t_study_team_leader_confirm_detail_PRIMARY", StudyTeamLeaderConfirmDetail.STUDY_TEAM_LEADER_CONFIRM_DETAIL.ID);
        public static final UniqueKey<DeleteDataTrainRecord> KEY_T_DELETE_DATA_TRAIN_PRIMARY = createUniqueKey(DeleteDataTrain.DELETE_DATA_TRAIN, "KEY_t_delete_data_train_PRIMARY", DeleteDataTrain.DELETE_DATA_TRAIN.ID);
        public static final UniqueKey<ClassGradesProjectRecord> KEY_T_CLASS_GRADES_PROJECT_PRIMARY = createUniqueKey(ClassGradesProject.CLASS_GRADES_PROJECT, "KEY_t_class_grades_project_PRIMARY", ClassGradesProject.CLASS_GRADES_PROJECT.ID);
        public static final UniqueKey<ClassGradesProjectMemberRecord> KEY_T_CLASS_GRADES_PROJECT_MEMBER_PRIMARY = createUniqueKey(ClassGradesProjectMember.CLASS_GRADES_PROJECT_MEMBER, "KEY_t_class_grades_project_member_PRIMARY", ClassGradesProjectMember.CLASS_GRADES_PROJECT_MEMBER.ID);
        public static final UniqueKey<TrainingTypeRecord> KEY_T_TRAINING_TYPE_PRIMARY = createUniqueKey(TrainingType.TRAINING_TYPE, "KEY_t_training_type_PRIMARY", TrainingType.TRAINING_TYPE.ID);
        public static final UniqueKey<SolutionConfigurationRecord> KEY_T_SOLUTION_CONFIGURATION_PRIMARY = createUniqueKey(SolutionConfiguration.SOLUTION_CONFIGURATION, "KEY_t_solution_configuration_PRIMARY", SolutionConfiguration.SOLUTION_CONFIGURATION.ID);
        public static final UniqueKey<TrainProjectRecord> KEY_T_TRAIN_PROJECT_PRIMARY = createUniqueKey(TrainProject.TRAIN_PROJECT, "KEY_t_train_project_PRIMARY", TrainProject.TRAIN_PROJECT.ID);
        public static final UniqueKey<PlanningImplementationRecord> KEY_T_PLANNING_IMPLEMENTATION_PRIMARY = createUniqueKey(PlanningImplementation.PLANNING_IMPLEMENTATION, "KEY_t_planning_implementation_PRIMARY", PlanningImplementation.PLANNING_IMPLEMENTATION.ID);
        public static final UniqueKey<PlanningImplementationRelatedRecord> KEY_T_PLANNING_IMPLEMENTATION_RELATED_PRIMARY = createUniqueKey(PlanningImplementationRelated.PLANNING_IMPLEMENTATION_RELATED, "KEY_t_planning_implementation_related_PRIMARY", PlanningImplementationRelated.PLANNING_IMPLEMENTATION_RELATED.ID);
        public static final UniqueKey<TrainChatGroupRecord> KEY_T_TRAIN_CHAT_GROUP_PRIMARY = createUniqueKey(TrainChatGroup.TRAIN_CHAT_GROUP, "KEY_t_train_chat_group_PRIMARY", TrainChatGroup.TRAIN_CHAT_GROUP.ID);

        public static final UniqueKey<TrainChatGroupInfoRecord> KEY_T_TRAIN_CHAT_GROUP_INFO_PRIMARY = createUniqueKey(TrainChatGroupInfo.TRAIN_CHAT_GROUP_INFO, "KEY_t_train_chat_group_info_PRIMARY", TrainChatGroupInfo.TRAIN_CHAT_GROUP_INFO.ID);
        public static final UniqueKey<ClassResearchSatisfactionRecord> KEY_T_CLASS_RESEARCH_SATISFACTION_PRIMARY = createUniqueKey(ClassResearchSatisfaction.CLASS_RESEARCH_SATISFACTION, "KEY_t_class_research_satisfaction_PRIMARY", ClassResearchSatisfaction.CLASS_RESEARCH_SATISFACTION.ID);
        public static final UniqueKey<AcademicStatisticsInfoRecord> KEY_T_ACADEMIC_STATISTICS_INFO_PRIMARY = createUniqueKey(AcademicStatisticsInfo.ACADEMIC_STATISTICS_INFO, "KEY_t_academic_statistics_info_PRIMARY", AcademicStatisticsInfo.ACADEMIC_STATISTICS_INFO.ID);
        public static final UniqueKey<StudyReportTrain_2025Record> KEY_T_STUDY_REPORT_TRAIN_2025_PRIMARY = createUniqueKey(StudyReportTrain_2025.STUDY_REPORT_TRAIN_2025, "KEY_t_study_report_train_2025_PRIMARY", StudyReportTrain_2025.STUDY_REPORT_TRAIN_2025.ID);
        public static final UniqueKey<StudyReportTrain_2026Record> KEY_T_STUDY_REPORT_TRAIN_2026_PRIMARY = createUniqueKey(StudyReportTrain_2026.STUDY_REPORT_TRAIN_2026, "KEY_t_study_report_train_2026_PRIMARY", StudyReportTrain_2026.STUDY_REPORT_TRAIN_2026.ID);
        public static final UniqueKey<StudyReportTrain_2027Record> KEY_T_STUDY_REPORT_TRAIN_2027_PRIMARY = createUniqueKey(StudyReportTrain_2027.STUDY_REPORT_TRAIN_2027, "KEY_t_study_report_train_2027_PRIMARY", StudyReportTrain_2027.STUDY_REPORT_TRAIN_2027.ID);
        public static final UniqueKey<StudyReportTrain_2028Record> KEY_T_STUDY_REPORT_TRAIN_2028_PRIMARY = createUniqueKey(StudyReportTrain_2028.STUDY_REPORT_TRAIN_2028, "KEY_t_study_report_train_2028_PRIMARY", StudyReportTrain_2028.STUDY_REPORT_TRAIN_2028.ID);
        public static final UniqueKey<StudyReportTrain_2029Record> KEY_T_STUDY_REPORT_TRAIN_2029_PRIMARY = createUniqueKey(StudyReportTrain_2029.STUDY_REPORT_TRAIN_2029, "KEY_t_study_report_train_2029_PRIMARY", StudyReportTrain_2029.STUDY_REPORT_TRAIN_2029.ID);
        public static final UniqueKey<ClassPopMangementRecord> KEY_T_CLASS_POP_MANGEMENT_PRIMARY = createUniqueKey(ClassPopMangement.CLASS_POP_MANGEMENT, "KEY_t_class_pop_mangement_PRIMARY", ClassPopMangement.CLASS_POP_MANGEMENT.ID);
    }
}
