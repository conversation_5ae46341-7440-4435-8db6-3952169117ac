/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.pojos;


import com.zxy.common.base.entity.BaseEntity;
import com.zxy.product.train.jooq.tables.interfaces.IProjectApproval;

import javax.annotation.Generated;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ProjectApprovalEntity extends BaseEntity implements IProjectApproval {

    private static final long serialVersionUID = 1L;

    private String  projectId;
    private Integer status;
    private String  suggestion;
    private String  approvalMember;
    private String  createMember;
    private Integer deleteFlag;
    private Long    approvalTime;

    public ProjectApprovalEntity() {}

    public ProjectApprovalEntity(ProjectApprovalEntity value) {
        this.projectId = value.projectId;
        this.status = value.status;
        this.suggestion = value.suggestion;
        this.approvalMember = value.approvalMember;
        this.createMember = value.createMember;
        this.deleteFlag = value.deleteFlag;
        this.approvalTime = value.approvalTime;
    }

    public ProjectApprovalEntity(
        String  id,
        String  projectId,
        Integer status,
        String  suggestion,
        String  approvalMember,
        Long    createTime,
        String  createMember,
        Integer deleteFlag,
        Long    approvalTime
    ) {
        super.setId(id);
        this.projectId = projectId;
        this.status = status;
        this.suggestion = suggestion;
        this.approvalMember = approvalMember;
        super.setCreateTime(createTime);
        this.createMember = createMember;
        this.deleteFlag = deleteFlag;
        this.approvalTime = approvalTime;
    }

    @Override
    public String getId() {
        return super.getId();
    }

    @Override
    public void setId(String id) {
        super.setId(id);
    }

    @Override
    public String getProjectId() {
        return this.projectId;
    }

    @Override
    public void setProjectId(String projectId) {
        this.projectId = projectId;
    }

    @Override
    public Integer getStatus() {
        return this.status;
    }

    @Override
    public void setStatus(Integer status) {
        this.status = status;
    }

    @Override
    public String getSuggestion() {
        return this.suggestion;
    }

    @Override
    public void setSuggestion(String suggestion) {
        this.suggestion = suggestion;
    }

    @Override
    public String getApprovalMember() {
        return this.approvalMember;
    }

    @Override
    public void setApprovalMember(String approvalMember) {
        this.approvalMember = approvalMember;
    }

    @Override
    public Long getCreateTime() {
        return super.getCreateTime();
    }

    @Override
    public void setCreateTime(Long createTime) {
        super.setCreateTime(createTime);
    }

    @Override
    public String getCreateMember() {
        return this.createMember;
    }

    @Override
    public void setCreateMember(String createMember) {
        this.createMember = createMember;
    }

    @Override
    public Integer getDeleteFlag() {
        return this.deleteFlag;
    }

    @Override
    public void setDeleteFlag(Integer deleteFlag) {
        this.deleteFlag = deleteFlag;
    }

    @Override
    public Long getApprovalTime() {
        return this.approvalTime;
    }

    @Override
    public void setApprovalTime(Long approvalTime) {
        this.approvalTime = approvalTime;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("ProjectApprovalEntity (");

        sb.append(getId());
        sb.append(", ").append(projectId);
        sb.append(", ").append(status);
        sb.append(", ").append(suggestion);
        sb.append(", ").append(approvalMember);
        sb.append(", ").append(getCreateTime());
        sb.append(", ").append(createMember);
        sb.append(", ").append(deleteFlag);
        sb.append(", ").append(approvalTime);

        sb.append(")");
        return sb.toString();
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(IProjectApproval from) {
        setId(from.getId());
        setProjectId(from.getProjectId());
        setStatus(from.getStatus());
        setSuggestion(from.getSuggestion());
        setApprovalMember(from.getApprovalMember());
        setCreateTime(from.getCreateTime());
        setCreateMember(from.getCreateMember());
        setDeleteFlag(from.getDeleteFlag());
        setApprovalTime(from.getApprovalTime());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends IProjectApproval> E into(E into) {
        into.from(this);
        return into;
    }

    public void forInsert() {
        this.setId(java.util.UUID.randomUUID().toString());
        this.setCreateTime(System.currentTimeMillis());
    }

    public static final <E extends ProjectApprovalEntity> org.jooq.RecordMapper<? extends org.jooq.Record, E> createMapper(Class<E> type) {
        return new org.jooq.RecordMapper<org.jooq.Record, E>() {
            @Override
            public E map(org.jooq.Record record) {
                com.zxy.product.train.jooq.tables.records.ProjectApprovalRecord r = new com.zxy.product.train.jooq.tables.records.ProjectApprovalRecord();
                org.jooq.Row row = record.fieldsRow();
                    if(row.indexOf(com.zxy.product.train.jooq.tables.ProjectApproval.PROJECT_APPROVAL.ID) > -1){
                        r.setValue(com.zxy.product.train.jooq.tables.ProjectApproval.PROJECT_APPROVAL.ID, record.getValue(com.zxy.product.train.jooq.tables.ProjectApproval.PROJECT_APPROVAL.ID));
                    }
                    if(row.indexOf(com.zxy.product.train.jooq.tables.ProjectApproval.PROJECT_APPROVAL.PROJECT_ID) > -1){
                        r.setValue(com.zxy.product.train.jooq.tables.ProjectApproval.PROJECT_APPROVAL.PROJECT_ID, record.getValue(com.zxy.product.train.jooq.tables.ProjectApproval.PROJECT_APPROVAL.PROJECT_ID));
                    }
                    if(row.indexOf(com.zxy.product.train.jooq.tables.ProjectApproval.PROJECT_APPROVAL.STATUS) > -1){
                        r.setValue(com.zxy.product.train.jooq.tables.ProjectApproval.PROJECT_APPROVAL.STATUS, record.getValue(com.zxy.product.train.jooq.tables.ProjectApproval.PROJECT_APPROVAL.STATUS));
                    }
                    if(row.indexOf(com.zxy.product.train.jooq.tables.ProjectApproval.PROJECT_APPROVAL.SUGGESTION) > -1){
                        r.setValue(com.zxy.product.train.jooq.tables.ProjectApproval.PROJECT_APPROVAL.SUGGESTION, record.getValue(com.zxy.product.train.jooq.tables.ProjectApproval.PROJECT_APPROVAL.SUGGESTION));
                    }
                    if(row.indexOf(com.zxy.product.train.jooq.tables.ProjectApproval.PROJECT_APPROVAL.APPROVAL_MEMBER) > -1){
                        r.setValue(com.zxy.product.train.jooq.tables.ProjectApproval.PROJECT_APPROVAL.APPROVAL_MEMBER, record.getValue(com.zxy.product.train.jooq.tables.ProjectApproval.PROJECT_APPROVAL.APPROVAL_MEMBER));
                    }
                    if(row.indexOf(com.zxy.product.train.jooq.tables.ProjectApproval.PROJECT_APPROVAL.CREATE_TIME) > -1){
                        r.setValue(com.zxy.product.train.jooq.tables.ProjectApproval.PROJECT_APPROVAL.CREATE_TIME, record.getValue(com.zxy.product.train.jooq.tables.ProjectApproval.PROJECT_APPROVAL.CREATE_TIME));
                    }
                    if(row.indexOf(com.zxy.product.train.jooq.tables.ProjectApproval.PROJECT_APPROVAL.CREATE_MEMBER) > -1){
                        r.setValue(com.zxy.product.train.jooq.tables.ProjectApproval.PROJECT_APPROVAL.CREATE_MEMBER, record.getValue(com.zxy.product.train.jooq.tables.ProjectApproval.PROJECT_APPROVAL.CREATE_MEMBER));
                    }
                    if(row.indexOf(com.zxy.product.train.jooq.tables.ProjectApproval.PROJECT_APPROVAL.DELETE_FLAG) > -1){
                        r.setValue(com.zxy.product.train.jooq.tables.ProjectApproval.PROJECT_APPROVAL.DELETE_FLAG, record.getValue(com.zxy.product.train.jooq.tables.ProjectApproval.PROJECT_APPROVAL.DELETE_FLAG));
                    }
                    if(row.indexOf(com.zxy.product.train.jooq.tables.ProjectApproval.PROJECT_APPROVAL.APPROVAL_TIME) > -1){
                        r.setValue(com.zxy.product.train.jooq.tables.ProjectApproval.PROJECT_APPROVAL.APPROVAL_TIME, record.getValue(com.zxy.product.train.jooq.tables.ProjectApproval.PROJECT_APPROVAL.APPROVAL_TIME));
                    }
                try {
                    E pojo = type.newInstance();
                    pojo.from(r);
                    return pojo;
                } catch (Exception e) {
                    e.printStackTrace(); // ignored
                }
                return null;
            }
        };
    }}
