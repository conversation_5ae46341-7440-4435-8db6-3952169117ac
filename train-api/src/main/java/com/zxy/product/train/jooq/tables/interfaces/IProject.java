/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.interfaces;


import java.io.Serializable;
import java.sql.Timestamp;

import javax.annotation.Generated;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface IProject extends Serializable {

    /**
     * Setter for <code>train.t_project.f_id</code>. 主键
     */
    public void setId(String value);

    /**
     * Getter for <code>train.t_project.f_id</code>. 主键
     */
    public String getId();

    /**
     * Setter for <code>train.t_project.f_name</code>. 计划名称
     */
    public void setName(String value);

    /**
     * Getter for <code>train.t_project.f_name</code>. 计划名称
     */
    public String getName();

    /**
     * Setter for <code>train.t_project.f_code</code>. MIS编码
     */
    public void setCode(String value);

    /**
     * Getter for <code>train.t_project.f_code</code>. MIS编码
     */
    public String getCode();

    /**
     * Setter for <code>train.t_project.f_contact_member_id</code>. 需求单位联系人
     */
    public void setContactMemberId(String value);

    /**
     * Getter for <code>train.t_project.f_contact_member_id</code>. 需求单位联系人
     */
    public String getContactMemberId();

    /**
     * Setter for <code>train.t_project.f_organization_id</code>. 需求单位
     */
    public void setOrganizationId(String value);

    /**
     * Getter for <code>train.t_project.f_organization_id</code>. 需求单位
     */
    public String getOrganizationId();

    /**
     * Setter for <code>train.t_project.f_contact_phone</code>. 联系电话
     */
    public void setContactPhone(String value);

    /**
     * Getter for <code>train.t_project.f_contact_phone</code>. 联系电话
     */
    public String getContactPhone();

    /**
     * Setter for <code>train.t_project.f_contact_email</code>. 联系邮箱
     */
    public void setContactEmail(String value);

    /**
     * Getter for <code>train.t_project.f_contact_email</code>. 联系邮箱
     */
    public String getContactEmail();

    /**
     * Setter for <code>train.t_project.f_year</code>. 计划年份
     */
    public void setYear(Integer value);

    /**
     * Getter for <code>train.t_project.f_year</code>. 计划年份
     */
    public Integer getYear();

    /**
     * Setter for <code>train.t_project.f_month</code>. 计划月份
     */
    public void setMonth(Integer value);

    /**
     * Getter for <code>train.t_project.f_month</code>. 计划月份
     */
    public Integer getMonth();

    /**
     * Setter for <code>train.t_project.f_amount</code>. 计划人数
     */
    public void setAmount(Integer value);

    /**
     * Getter for <code>train.t_project.f_amount</code>. 计划人数
     */
    public Integer getAmount();

    /**
     * Setter for <code>train.t_project.f_object</code>. 培训对象
     */
    public void setObject(String value);

    /**
     * Getter for <code>train.t_project.f_object</code>. 培训对象
     */
    public String getObject();

    /**
     * Setter for <code>train.t_project.f_days</code>. 计划培训天数
     */
    public void setDays(Integer value);

    /**
     * Getter for <code>train.t_project.f_days</code>. 计划培训天数
     */
    public Integer getDays();

    /**
     * Setter for <code>train.t_project.f_type_id</code>. 培训类型
     */
    public void setTypeId(String value);

    /**
     * Getter for <code>train.t_project.f_type_id</code>. 培训类型
     */
    public String getTypeId();

    /**
     * Setter for <code>train.t_project.f_address</code>. 培训地点
     */
    public void setAddress(String value);

    /**
     * Getter for <code>train.t_project.f_address</code>. 培训地点
     */
    public String getAddress();

    /**
     * Setter for <code>train.t_project.f_status</code>. 状态（1待预定2待审核3同意申请4资源已满5不同意）
     */
    public void setStatus(Integer value);

    /**
     * Getter for <code>train.t_project.f_status</code>. 状态（1待预定2待审核3同意申请4资源已满5不同意）
     */
    public Integer getStatus();

    /**
     * Setter for <code>train.t_project.f_create_time</code>. 创建时间
     */
    public void setCreateTime(Long value);

    /**
     * Getter for <code>train.t_project.f_create_time</code>. 创建时间
     */
    public Long getCreateTime();

    /**
     * Setter for <code>train.t_project.f_create_member</code>. 创建人ID
     */
    public void setCreateMember(String value);

    /**
     * Getter for <code>train.t_project.f_create_member</code>. 创建人ID
     */
    public String getCreateMember();

    /**
     * Setter for <code>train.t_project.f_delete_flag</code>. 删除标记（0未删除1已删除）
     */
    public void setDeleteFlag(Integer value);

    /**
     * Getter for <code>train.t_project.f_delete_flag</code>. 删除标记（0未删除1已删除）
     */
    public Integer getDeleteFlag();

    /**
     * Setter for <code>train.t_project.f_student_status</code>. 判断是否是学员端提交审核 0否 1是
     */
    public void setStudentStatus(Integer value);

    /**
     * Getter for <code>train.t_project.f_student_status</code>. 判断是否是学员端提交审核 0否 1是
     */
    public Integer getStudentStatus();

    /**
     * Setter for <code>train.t_project.f_cost</code>. 费用类型
     */
    public void setCost(String value);

    /**
     * Getter for <code>train.t_project.f_cost</code>. 费用类型
     */
    public String getCost();

    /**
     * Setter for <code>train.t_project.f_survey_type</code>. 需求调研方式
     */
    public void setSurveyType(String value);

    /**
     * Getter for <code>train.t_project.f_survey_type</code>. 需求调研方式
     */
    public String getSurveyType();

    /**
     * Setter for <code>train.t_project.f_target</code>. 培训目标
     */
    public void setTarget(String value);

    /**
     * Getter for <code>train.t_project.f_target</code>. 培训目标
     */
    public String getTarget();

    /**
     * Setter for <code>train.t_project.f_temp</code>.
     */
    public void setTemp(String value);

    /**
     * Getter for <code>train.t_project.f_temp</code>.
     */
    public String getTemp();

    /**
     * Setter for <code>train.t_project.f_is_party_cadre</code>. 是否是党干部培训班 0:否 1:是
     */
    public void setIsPartyCadre(Integer value);

    /**
     * Getter for <code>train.t_project.f_is_party_cadre</code>. 是否是党干部培训班 0:否 1:是
     */
    public Integer getIsPartyCadre();

    /**
     * Setter for <code>train.t_project.f_modify_date</code>. 修改时间
     */
    public void setModifyDate(Timestamp value);

    /**
     * Getter for <code>train.t_project.f_modify_date</code>. 修改时间
     */
    public Timestamp getModifyDate();

    /**
     * Setter for <code>train.t_project.f_reservation_member</code>. 预定人id
     */
    public void setReservationMember(String value);

    /**
     * Getter for <code>train.t_project.f_reservation_member</code>. 预定人id
     */
    public String getReservationMember();

    /**
     * Setter for <code>train.t_project.f_reservation_time</code>. 预定时间
     */
    public void setReservationTime(Long value);

    /**
     * Getter for <code>train.t_project.f_reservation_time</code>. 预定时间
     */
    public Long getReservationTime();

    /**
     * Setter for <code>train.t_project.f_is_manual_finish</code>. 是否手动结束：0-否，1-是
     */
    public void setIsManualFinish(Integer value);

    /**
     * Getter for <code>train.t_project.f_is_manual_finish</code>. 是否手动结束：0-否，1-是
     */
    public Integer getIsManualFinish();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface IProject
     */
    public void from(IProject from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface IProject
     */
    public <E extends IProject> E into(E into);
}
