package com.zxy.product.train.api;

import com.zxy.common.base.annotation.RemoteService;
import com.zxy.common.base.helper.PagedResult;
import com.zxy.product.train.entity.ClassstaffClass;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

/**
 * Created by 田聪 on 2017/2/16.
 */
@RemoteService
public interface ClassstaffClassService {

	/**
	 * 成员管理班务列表
	 * @param page				页码
	 * @param pageSize			每页行数
	 * @param classId			班级id
	 * @param memberName		员工编号
	 * @param memberFullName	员工姓名
	 * @return					分页数据
	 */
	@Transactional(readOnly = true,propagation = Propagation.SUPPORTS)
	PagedResult<ClassstaffClass> find(int page,int pageSize,String classId);

	/**
	 * 修改班务名称、排序、删除字段
	 * @param id				主键
	 * @param callName			称呼
	 * @param sort				排序
	 * @param delete			删除字段
	 * @return
	 */

	@Transactional(readOnly = true,propagation = Propagation.SUPPORTS)
	Integer findClassstaff(String memberId);

	@Transactional
	ClassstaffClass update(String id,Optional<String> callName,Optional<Integer> sort,Optional<Integer> delete);

	/**
	 * 查询已存在班务的memberIds
	 * @param classId		班级id
	 * @return
	 */
	@Transactional(readOnly = true,propagation = Propagation.SUPPORTS)
	String [] findMemberIds(String classId);

	/**
	 * 发送短信页面班务列表
	 * @param classId		班级id
	 * @return
	 */
	@Transactional(readOnly = true,propagation = Propagation.SUPPORTS)
	List<ClassstaffClass> findForMessage(String classId);

	/**
	 * 添加单个班务
	 * @param classId		班级id
	 * @param memberId		人员id
	 * @return
	 */
	int insert(String classId,String memberId);

	/**
	 * 多个培训班，多个班主任
	 * @param classIds
	 * @return
	 */
	@Transactional(readOnly = true,propagation = Propagation.SUPPORTS)
    List<ClassstaffClass> findByClassMember(List<String> classIds);

    /**
	 * 添加多个班务
	 * @param classId		班级id
	 * @param memberId		人员id的集合
	 * @return
	 */
	@Transactional
	int[] insertAll(String classId,List<String> memberIdList);

	/**
	 * 添加班主任
	 * @param classId		班级id
	 * @param memberId		人员id
	 * @return
	 */
	ClassstaffClass insertMasterTeacher(String classId,String memberId);

	/**
	 * 查询当前登录人是否为指定班级的班务人员
	 * @param classId
	 * @param currentUserId
	 * @return
	 */
    ClassstaffClass findstaffClassByClassId(String classId, String currentUserId);


	@Transactional(readOnly = true,propagation = Propagation.SUPPORTS)
	int countByClass(String classId);

	@Transactional(propagation = Propagation.SUPPORTS)
	int delTeacher(String classId,String memberId);

	/**
	 * 通过班级id和memberIds获取电话数组
	 * @param classId
	 * @param memberIds
	 * @return
	 */
	@Transactional(readOnly = true,propagation = Propagation.SUPPORTS)
	List<ClassstaffClass> findTepIds(String classId, String[] memberIds);

	@Transactional(readOnly = true,propagation = Propagation.SUPPORTS)
    boolean findclassstaffClass(String classId, String memberId);
}
