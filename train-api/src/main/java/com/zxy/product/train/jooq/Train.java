/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq;

import com.zxy.product.train.jooq.tables.*;
import org.jooq.Catalog;
import org.jooq.Table;
import org.jooq.impl.SchemaImpl;

import javax.annotation.Generated;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class Train extends SchemaImpl {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>train</code>
     */
    public static final Train TRAIN_SCHEMA = new Train();

    /**
     * The table <code>train.t_album</code>.
     */
    public final Album ALBUM = Album.ALBUM;

    /**
     * 受众项
     */
    public final AudienceItem AUDIENCE_ITEM = AudienceItem.AUDIENCE_ITEM;

    /**
     * 受众人表
     */
    public final AudienceMember AUDIENCE_MEMBER = AudienceMember.AUDIENCE_MEMBER;

    /**
     * 受众对象表
     */
    public final AudienceObject AUDIENCE_OBJECT = AudienceObject.AUDIENCE_OBJECT;

    /**
     * 班车表
     */
    public final Bus BUS = Bus.BUS;

    /**
     * 班车详情表
     */
    public final BusDetail BUS_DETAIL = BusDetail.BUS_DETAIL;

    /**
     * 班车选项主题表
     */
    public final BusOption BUS_OPTION = BusOption.BUS_OPTION;

    /**
     * The table <code>train.t_center_configuration</code>.
     */
    public final CenterConfiguration CENTER_CONFIGURATION = CenterConfiguration.CENTER_CONFIGURATION;

    /**
     * The table <code>train.t_classroom_configuration</code>.
     */
    public final ClassroomConfiguration CLASSROOM_CONFIGURATION = ClassroomConfiguration.CLASSROOM_CONFIGURATION;

    /**
     * 培训班级和班务人员关联表
     */
    public final ClassstaffClass CLASSSTAFF_CLASS = ClassstaffClass.CLASSSTAFF_CLASS;

    /**
     * The table <code>train.t_classstaff_configuration</code>.
     */
    public final ClassstaffConfiguration CLASSSTAFF_CONFIGURATION = ClassstaffConfiguration.CLASSSTAFF_CONFIGURATION;

    /**
     * The table <code>train.t_class_business_progress</code>.
     */
    public final ClassBusinessProgress CLASS_BUSINESS_PROGRESS = ClassBusinessProgress.CLASS_BUSINESS_PROGRESS;

    /**
     * 历史班级课程表
     */
    public final ClassCourseHistory CLASS_COURSE_HISTORY = ClassCourseHistory.CLASS_COURSE_HISTORY;

    /**
     * The table <code>train.t_class_detail</code>.
     */
    public final ClassDetail CLASS_DETAIL = ClassDetail.CLASS_DETAIL;

    /**
     * The table <code>train.t_class_evaluate</code>.
     */
    public final ClassEvaluate CLASS_EVALUATE = ClassEvaluate.CLASS_EVALUATE;

    /**
     * The table <code>train.t_class_group</code>.
     */
    public final ClassGroup CLASS_GROUP = ClassGroup.CLASS_GROUP;

    /**
     * 历史班级表
     */
    public final ClassHistory CLASS_HISTORY = ClassHistory.CLASS_HISTORY;

    /**
     * The table <code>train.t_class_info</code>.
     */
    public final ClassInfo CLASS_INFO = ClassInfo.CLASS_INFO;

    /**
     * The table <code>train.t_class_offline_course</code>.
     */
    public final ClassOfflineCourse CLASS_OFFLINE_COURSE = ClassOfflineCourse.CLASS_OFFLINE_COURSE;

    /**
     * The table <code>train.t_class_online_course</code>.
     */
    public final ClassOnlineCourse CLASS_ONLINE_COURSE = ClassOnlineCourse.CLASS_ONLINE_COURSE;

    /**
     * 新版满意度问卷数据表
     */
    public final ClassQuestionnaireTotal CLASS_QUESTIONNAIRE_TOTAL = ClassQuestionnaireTotal.CLASS_QUESTIONNAIRE_TOTAL;

    /**
     * The table <code>train.t_class_quota</code>.
     */
    public final ClassQuota CLASS_QUOTA = ClassQuota.CLASS_QUOTA;

    /**
     * The table <code>train.t_class_quota_detail</code>.
     */
    public final ClassQuotaDetail CLASS_QUOTA_DETAIL = ClassQuotaDetail.CLASS_QUOTA_DETAIL;

    /**
     * The table <code>train.t_class_required</code>.
     */
    public final ClassRequired CLASS_REQUIRED = ClassRequired.CLASS_REQUIRED;

    /**
     * The table <code>train.t_class_required_theme</code>.
     */
    public final ClassRequiredTheme CLASS_REQUIRED_THEME = ClassRequiredTheme.CLASS_REQUIRED_THEME;

    /**
     * The table <code>train.t_class_resource</code>.
     */
    public final ClassResource CLASS_RESOURCE = ClassResource.CLASS_RESOURCE;

    /**
     * The table <code>train.t_class_signup_info</code>.
     */
    public final ClassSignupInfo CLASS_SIGNUP_INFO = ClassSignupInfo.CLASS_SIGNUP_INFO;

    /**
     * The table <code>train.t_class_statistics</code>.
     */
    public final ClassStatistics CLASS_STATISTICS = ClassStatistics.CLASS_STATISTICS;

    /**
     * The table <code>train.t_class_theme</code>.
     */
    public final ClassTheme CLASS_THEME = ClassTheme.CLASS_THEME;

    /**
     * The table <code>train.t_class_two_brings</code>.
     */
    public final ClassTwoBrings CLASS_TWO_BRINGS = ClassTwoBrings.CLASS_TWO_BRINGS;

    /**
     * The table <code>train.t_collecting_course</code>.
     */
    public final CollectingCourse COLLECTING_COURSE = CollectingCourse.COLLECTING_COURSE;

    /**
     * 集采方案配置
     */
    public final CollectionProgrammeConfig COLLECTION_PROGRAMME_CONFIG = CollectionProgrammeConfig.COLLECTION_PROGRAMME_CONFIG;

    /**
     * 集采方案详解关联课程表
     */
    public final CollectionProgrammeCourse COLLECTION_PROGRAMME_COURSE = CollectionProgrammeCourse.COLLECTION_PROGRAMME_COURSE;

    /**
     * 学院授课记录表
     */
    public final CollegeTeaching COLLEGE_TEACHING = CollegeTeaching.COLLEGE_TEACHING;

    /**
     * 历史配置表
     */
    public final ConfigruationHistory CONFIGRUATION_HISTORY = ConfigruationHistory.CONFIGRUATION_HISTORY;

    /**
     * The table <code>train.t_configuration</code>.
     */
    public final Configuration CONFIGURATION = Configuration.CONFIGURATION;

    /**
     * The table <code>train.t_configuration_value</code>.
     */
    public final ConfigurationValue CONFIGURATION_VALUE = ConfigurationValue.CONFIGURATION_VALUE;

    /**
     * 公司段对应关系
     */
    public final CorporateSegmentRelation CORPORATE_SEGMENT_RELATION = CorporateSegmentRelation.CORPORATE_SEGMENT_RELATION;

    /**
     * The table <code>train.t_course_attach</code>.
     */
    public final CourseAttach COURSE_ATTACH = CourseAttach.COURSE_ATTACH;

    /**
     * 课程属性表
     */
    public final CourseAttribute COURSE_ATTRIBUTE = CourseAttribute.COURSE_ATTRIBUTE;

    /**
     * 课程目录表
     */
    public final CourseCategory COURSE_CATEGORY = CourseCategory.COURSE_CATEGORY;

    /**
     * 在线课程表
     */
    public final CourseInfo COURSE_INFO = CourseInfo.COURSE_INFO;

    /**
     * The table <code>train.t_course_salary</code>.
     */
    public final CourseSalary COURSE_SALARY = CourseSalary.COURSE_SALARY;

    /**
     * 教研教学活动配置
     */
    public final CourseTeachingActivities COURSE_TEACHING_ACTIVITIES = CourseTeachingActivities.COURSE_TEACHING_ACTIVITIES;

    /**
     * 需求方枚举表
     */
    public final DemandSideOrganization DEMAND_SIDE_ORGANIZATION = DemandSideOrganization.DEMAND_SIDE_ORGANIZATION;

    /**
     * The table <code>train.t_dimension</code>.
     */
    public final Dimension DIMENSION = Dimension.DIMENSION;

    /**
     * The table <code>train.t_dimension_question</code>.
     */
    public final DimensionQuestion DIMENSION_QUESTION = DimensionQuestion.DIMENSION_QUESTION;

    /**
     * The table <code>train.t_evaluate</code>.
     */
    public final Evaluate EVALUATE = Evaluate.EVALUATE;

    /**
     * 面授课程库表
     */
    public final F2fCourse F2F_COURSE = F2fCourse.F2F_COURSE;

    /**
     * 面授课程库表
     */
    public final F2fCourseLibrary F2F_COURSE_LIBRARY = F2fCourseLibrary.F2F_COURSE_LIBRARY;

    /**
     * The table <code>train.t_grant_detail</code>.
     */
    public final GrantDetail GRANT_DETAIL = GrantDetail.GRANT_DETAIL;

    /**
     * The table <code>train.t_group_configuration</code>.
     */
    public final GroupConfiguration GROUP_CONFIGURATION = GroupConfiguration.GROUP_CONFIGURATION;

    /**
     * The table <code>train.t_group_configuration_value</code>.
     */
    public final GroupConfigurationValue GROUP_CONFIGURATION_VALUE = GroupConfigurationValue.GROUP_CONFIGURATION_VALUE;

    /**
     * The table <code>train.t_home_lecturer</code>.
     */
    public final HomeLecturer HOME_LECTURER = HomeLecturer.HOME_LECTURER;

    /**
     * The table <code>train.t_job</code>.
     */
    public final Job JOB = Job.JOB;

    /**
     * The table <code>train.t_label</code>.
     */
    public final Label LABEL = Label.LABEL;

    /**
     * The table <code>train.t_lecturer</code>.
     */
    public final Lecturer LECTURER = Lecturer.LECTURER;

    /**
     * 讲师擅长课程列表
     */
    public final LecturerAdeptCourse LECTURER_ADEPT_COURSE = LecturerAdeptCourse.LECTURER_ADEPT_COURSE;

    /**
     * 讲师属性配置表
     */
    public final LecturerAttribute LECTURER_ATTRIBUTE = LecturerAttribute.LECTURER_ATTRIBUTE;

    /**
     * 讲师专业序列/讲师课程库课程分类配置
     */
    public final LecturerCourseConfig LECTURER_COURSE_CONFIG = LecturerCourseConfig.LECTURER_COURSE_CONFIG;

    /**
     * The table <code>train.t_lecturer_label</code>.
     */
    public final LecturerLabel LECTURER_LABEL = LecturerLabel.LECTURER_LABEL;

    /**
     * 讲师发布权限人员表
     */
    public final LecturerReleaseAuthority LECTURER_RELEASE_AUTHORITY = LecturerReleaseAuthority.LECTURER_RELEASE_AUTHORITY;

    /**
     * 讲师点赞记录
     */
    public final LecturerThumbsUp LECTURER_THUMBS_UP = LecturerThumbsUp.LECTURER_THUMBS_UP;

    /**
     * The table <code>train.t_level</code>.
     */
    public final Level LEVEL = Level.LEVEL;

    /**
     * The table <code>train.t_limit_configuration</code>.
     */
    public final LimitConfiguration LIMIT_CONFIGURATION = LimitConfiguration.LIMIT_CONFIGURATION;

    /**
     * The table <code>train.t_limit_default_configuration</code>.
     */
    public final LimitDefaultConfiguration LIMIT_DEFAULT_CONFIGURATION = LimitDefaultConfiguration.LIMIT_DEFAULT_CONFIGURATION;

    /**
     * The table <code>train.t_member</code>.
     */
    public final Member MEMBER = Member.MEMBER;

    /**
     * The table <code>train.t_member_config</code>.
     */
    public final MemberConfig MEMBER_CONFIG = MemberConfig.MEMBER_CONFIG;

    /**
     * The table <code>train.t_message_detail</code>.
     */
    public final MessageDetail MESSAGE_DETAIL = MessageDetail.MESSAGE_DETAIL;

    /**
     * The table <code>train.t_message_record</code>.
     */
    public final MessageRecord MESSAGE_RECORD = MessageRecord.MESSAGE_RECORD;

    /**
     * The table <code>train.t_organization</code>.
     */
    public final Organization ORGANIZATION = Organization.ORGANIZATION;

    /**
     * The table <code>train.t_organization_detail</code>.
     */
    public final OrganizationDetail ORGANIZATION_DETAIL = OrganizationDetail.ORGANIZATION_DETAIL;

    /**
     * 各单位授课记录表
     */
    public final OrganizationTeaching ORGANIZATION_TEACHING = OrganizationTeaching.ORGANIZATION_TEACHING;

    /**
     * 其他教学教研记录表
     */
    public final OtherTeaching OTHER_TEACHING = OtherTeaching.OTHER_TEACHING;

    /**
     * PCCW HR接口调用结果
     */
    public final PccwResult PCCW_RESULT = PccwResult.PCCW_RESULT;

    /**
     * The table <code>train.t_position</code>.
     */
    public final Position POSITION = Position.POSITION;

    /**
     * The table <code>train.t_project</code>.
     */
    public final Project PROJECT = Project.PROJECT;

    /**
     * The table <code>train.t_project_approval</code>.
     */
    public final ProjectApproval PROJECT_APPROVAL = ProjectApproval.PROJECT_APPROVAL;

    /**
     * 历史培训计划表
     */
    public final ProjectHistory PROJECT_HISTORY = ProjectHistory.PROJECT_HISTORY;

    /**
     * The table <code>train.t_project_occupy</code>.
     */
    public final ProjectOccupy PROJECT_OCCUPY = ProjectOccupy.PROJECT_OCCUPY;

    /**
     * The table <code>train.t_question</code>.
     */
    public final Question QUESTION = Question.QUESTION;

    /**
     * 培训班满意度问卷类型问题表
     */
    public final QuestionnaireQuestionType QUESTIONNAIRE_QUESTION_TYPE = QuestionnaireQuestionType.QUESTIONNAIRE_QUESTION_TYPE;

    /**
     * The table <code>train.t_question_attr</code>.
     */
    public final QuestionAttr QUESTION_ATTR = QuestionAttr.QUESTION_ATTR;


    /**
     * 红色展馆信息
     */
    public final RedPavilion RED_PAVILION = RedPavilion.RED_PAVILION;

    /**
     * The table <code>train.t_research_answer_record</code>.
     */
    public final ResearchAnswerRecord RESEARCH_ANSWER_RECORD = ResearchAnswerRecord.RESEARCH_ANSWER_RECORD;

    /**
     * The table <code>train.t_research_questionary</code>.
     */
    public final ResearchQuestionary RESEARCH_QUESTIONARY = ResearchQuestionary.RESEARCH_QUESTIONARY;

    /**
     * The table <code>train.t_research_record</code>.
     */
    public final ResearchRecord RESEARCH_RECORD = ResearchRecord.RESEARCH_RECORD;

    /**
     * 历史课酬表
     */
    public final SalaryHistory SALARY_HISTORY = SalaryHistory.SALARY_HISTORY;

    /**
     * The table <code>train.t_settlement</code>.
     */
    public final Settlement SETTLEMENT = Settlement.SETTLEMENT;

    /**
     * The table <code>train.t_settlement_configuration</code>.
     */
    public final SettlementConfiguration SETTLEMENT_CONFIGURATION = SettlementConfiguration.SETTLEMENT_CONFIGURATION;

    /**
     * The table <code>train.t_settlement_configuration_value</code>.
     */
    public final SettlementConfigurationValue SETTLEMENT_CONFIGURATION_VALUE = SettlementConfigurationValue.SETTLEMENT_CONFIGURATION_VALUE;

    /**
     * 签到表
     */
    public final Sign SIGN = Sign.SIGN;

    /**
     * 签到详情表
     */
    public final SignDetail SIGN_DETAIL = SignDetail.SIGN_DETAIL;

    /**
     * 请假表
     */
    public final SignLeave SIGN_LEAVE = SignLeave.SIGN_LEAVE;

    /**
     * The table <code>train.t_student_detail</code>.
     */
    public final StudentDetail STUDENT_DETAIL = StudentDetail.STUDENT_DETAIL;

    /**
     * The table <code>train.t_student_detail_total</code>.
     */
    public final StudentDetailTotal STUDENT_DETAIL_TOTAL = StudentDetailTotal.STUDENT_DETAIL_TOTAL;

    /**
     * 学员明细规则排序表
     */
    public final StudentDetailTotalSort STUDENT_DETAIL_TOTAL_SORT = StudentDetailTotalSort.STUDENT_DETAIL_TOTAL_SORT;

    /**
     * 历史学员表
     */
    public final StudentHistory STUDENT_HISTORY = StudentHistory.STUDENT_HISTORY;

    /**
     * 作业表
     */
    public final Task TASK = Task.TASK;

    /**
     * 作业审核表
     */
    public final TaskApproval TASK_APPROVAL = TaskApproval.TASK_APPROVAL;

    /**
     * 作业附件关联表
     */
    public final TaskAttach TASK_ATTACH = TaskAttach.TASK_ATTACH;

    /**
     * 用户提交作业详情表
     */
    public final TaskMember TASK_MEMBER = TaskMember.TASK_MEMBER;

    /**
     * 作业审核人关联表
     */
    public final TaskReviewer TASK_REVIEWER = TaskReviewer.TASK_REVIEWER;

    /**
     * 培训学员表
     */
    public final Trainee TRAINEE = Trainee.TRAINEE;

    /**
     * 培训学员分组表
     */
    public final TraineeGroup TRAINEE_GROUP = TraineeGroup.TRAINEE_GROUP;

    /**
     * 学习团队表
     */
    public final StudyTeam STUDY_TEAM = com.zxy.product.train.jooq.tables.StudyTeam.STUDY_TEAM;

    /**
     * 学习活动表
     */
    public final StudyTeamActivity STUDY_TEAM_ACTIVITY = com.zxy.product.train.jooq.tables.StudyTeamActivity.STUDY_TEAM_ACTIVITY;

    /**
     * 学习活动-任务关联表
     */
    public final StudyTeamActivityTask STUDY_TEAM_ACTIVITY_TASK = com.zxy.product.train.jooq.tables.StudyTeamActivityTask.STUDY_TEAM_ACTIVITY_TASK;

    /**
     * 学习团队成员表
     */
    public final StudyTeamMember STUDY_TEAM_MEMBER = com.zxy.product.train.jooq.tables.StudyTeamMember.STUDY_TEAM_MEMBER;

    /**
     * 学习团队成员签到表
     */
    public final StudyTeamMemberSign STUDY_TEAM_MEMBER_SIGN = com.zxy.product.train.jooq.tables.StudyTeamMemberSign.STUDY_TEAM_MEMBER_SIGN;

    /**
     * 学习团队成员签到表流水表
     */
    public final StudyTeamMemberSignLog STUDY_TEAM_MEMBER_SIGN_LOG = com.zxy.product.train.jooq.tables.StudyTeamMemberSignLog.STUDY_TEAM_MEMBER_SIGN_LOG;

    /**
     * 团队学习班-学习成果表
     */
    public final StudyTeamAchievement STUDY_TEAM_ACHIEVEMENT = com.zxy.product.train.jooq.tables.StudyTeamAchievement.STUDY_TEAM_ACHIEVEMENT;

    /**
     * 团队学习班-点赞明细表
     */
    public final StudyTeamAchievementPraise STUDY_TEAM_ACHIEVEMENT_PRAISE = com.zxy.product.train.jooq.tables.StudyTeamAchievementPraise.STUDY_TEAM_ACHIEVEMENT_PRAISE;

    /**
     * 团队学习班-学习成果回复表
     */
    public final StudyTeamAchievementReply STUDY_TEAM_ACHIEVEMENT_REPLY = com.zxy.product.train.jooq.tables.StudyTeamAchievementReply.STUDY_TEAM_ACHIEVEMENT_REPLY;

    /**
     * 团队学习班-活动资料表
     */
    public final StudyTeamActivityAttachment STUDY_TEAM_ACTIVITY_ATTACHMENT = com.zxy.product.train.jooq.tables.StudyTeamActivityAttachment.STUDY_TEAM_ACTIVITY_ATTACHMENT;

    /**
     * 团队学习班-活动相册资料表
     */
    public final StudyTeamActivityPhotos STUDY_TEAM_ACTIVITY_PHOTOS = com.zxy.product.train.jooq.tables.StudyTeamActivityPhotos.STUDY_TEAM_ACTIVITY_PHOTOS;

    /**
     * 培训班结算人数配置表
     */
    public final SettlementMemberQuantity SETTLEMENT_MEMBER_QUANTITY = com.zxy.product.train.jooq.tables.SettlementMemberQuantity.SETTLEMENT_MEMBER_QUANTITY;

    /**
     * 学习活动-课程领学人学习时间
     */
    public final StudyTeamLeaderConfirmDetail STUDY_TEAM_LEADER_CONFIRM_DETAIL = com.zxy.product.train.jooq.tables.StudyTeamLeaderConfirmDetail.STUDY_TEAM_LEADER_CONFIRM_DETAIL;

    /**
     * 删除记录表
     */
    public final DeleteDataTrain DELETE_DATA_TRAIN = com.zxy.product.train.jooq.tables.DeleteDataTrain.DELETE_DATA_TRAIN;

    /**
     * 培训类别表
     */
    public final TrainingType TRAINING_TYPE = com.zxy.product.train.jooq.tables.TrainingType.TRAINING_TYPE;


    /**
     * 智慧教务-配置情况
     */
    public final SolutionConfiguration SOLUTION_CONFIGURATION = com.zxy.product.train.jooq.tables.SolutionConfiguration.SOLUTION_CONFIGURATION;

    /**
     * 培训项目表
     */
    public final TrainProject TRAIN_PROJECT = com.zxy.product.train.jooq.tables.TrainProject.TRAIN_PROJECT;

    /**
     * 策划实施表
     */
    public final PlanningImplementation PLANNING_IMPLEMENTATION = com.zxy.product.train.jooq.tables.PlanningImplementation.PLANNING_IMPLEMENTATION;

    /**
     * 策划实施关联表
     */
    public final PlanningImplementationRelated PLANNING_IMPLEMENTATION_RELATED = com.zxy.product.train.jooq.tables.PlanningImplementationRelated.PLANNING_IMPLEMENTATION_RELATED;

    /**
     * 培训班群聊成员表
     */
    public final TrainChatGroup TRAIN_CHAT_GROUP = com.zxy.product.train.jooq.tables.TrainChatGroup.TRAIN_CHAT_GROUP;

    /**
     * 培训班群聊表
     */
    public final TrainChatGroupInfo TRAIN_CHAT_GROUP_INFO = com.zxy.product.train.jooq.tables.TrainChatGroupInfo.TRAIN_CHAT_GROUP_INFO;

    /**
     * 培训班课程问卷满意度结果表
     */
    public final ClassResearchSatisfaction CLASS_RESEARCH_SATISFACTION = com.zxy.product.train.jooq.tables.ClassResearchSatisfaction.CLASS_RESEARCH_SATISFACTION;

    public final AcademicStatisticsInfo ACADEMIC_STATISTICS_INFO = com.zxy.product.train.jooq.tables.AcademicStatisticsInfo.ACADEMIC_STATISTICS_INFO;

    /**
     * 学习报告-培训班学习表(学情报告月度统计使用)
     */
    public final StudyReportTrain_2025 STUDY_REPORT_TRAIN_2025 = com.zxy.product.train.jooq.tables.StudyReportTrain_2025.STUDY_REPORT_TRAIN_2025;

    /**
     * 学习报告-培训班学习表(学情报告月度统计使用)
     */
    public final StudyReportTrain_2026 STUDY_REPORT_TRAIN_2026 = com.zxy.product.train.jooq.tables.StudyReportTrain_2026.STUDY_REPORT_TRAIN_2026;

    /**
     * 学习报告-培训班学习表(学情报告月度统计使用)
     */
    public final StudyReportTrain_2027 STUDY_REPORT_TRAIN_2027 = com.zxy.product.train.jooq.tables.StudyReportTrain_2027.STUDY_REPORT_TRAIN_2027;

    /**
     * 学习报告-培训班学习表(学情报告月度统计使用)
     */
    public final StudyReportTrain_2028 STUDY_REPORT_TRAIN_2028 = com.zxy.product.train.jooq.tables.StudyReportTrain_2028.STUDY_REPORT_TRAIN_2028;

    /**
     * 学习报告-培训班学习表(学情报告月度统计使用)
     */
    public final StudyReportTrain_2029 STUDY_REPORT_TRAIN_2029 = com.zxy.product.train.jooq.tables.StudyReportTrain_2029.STUDY_REPORT_TRAIN_2029;
    /**
     * 学员管理弹窗管理
     */
    public final ClassPopMangement CLASS_POP_MANGEMENT = com.zxy.product.train.jooq.tables.ClassPopMangement.CLASS_POP_MANGEMENT;
    /**
     * No further instances allowed
     */
    private Train() {
        super("train", null);
    }


    /**
     * {@inheritDoc}
     */
    @Override
    public Catalog getCatalog() {
        return DefaultCatalog.DEFAULT_CATALOG;
    }

    @Override
    public final List<Table<?>> getTables() {
        List result = new ArrayList();
        result.addAll(getTables0());
        return result;
    }

    private final List<Table<?>> getTables0() {
        return Arrays.<Table<?>>asList(
            Album.ALBUM,
            AudienceItem.AUDIENCE_ITEM,
            AudienceMember.AUDIENCE_MEMBER,
            AudienceObject.AUDIENCE_OBJECT,
            Bus.BUS,
            BusDetail.BUS_DETAIL,
            BusOption.BUS_OPTION,
            CenterConfiguration.CENTER_CONFIGURATION,
            ClassroomConfiguration.CLASSROOM_CONFIGURATION,
            ClassstaffClass.CLASSSTAFF_CLASS,
            ClassstaffConfiguration.CLASSSTAFF_CONFIGURATION,
            ClassBusinessProgress.CLASS_BUSINESS_PROGRESS,
            ClassCourseHistory.CLASS_COURSE_HISTORY,
            ClassDetail.CLASS_DETAIL,
            ClassEvaluate.CLASS_EVALUATE,
            ClassGroup.CLASS_GROUP,
            ClassHistory.CLASS_HISTORY,
            ClassInfo.CLASS_INFO,
            ClassOfflineCourse.CLASS_OFFLINE_COURSE,
            ClassOnlineCourse.CLASS_ONLINE_COURSE,
            ClassQuestionnaireTotal.CLASS_QUESTIONNAIRE_TOTAL,
            ClassQuota.CLASS_QUOTA,
            ClassQuotaDetail.CLASS_QUOTA_DETAIL,
            ClassRequired.CLASS_REQUIRED,
            ClassRequiredTheme.CLASS_REQUIRED_THEME,
            ClassResource.CLASS_RESOURCE,
            ClassSignupInfo.CLASS_SIGNUP_INFO,
            ClassStatistics.CLASS_STATISTICS,
            ClassTheme.CLASS_THEME,
            ClassTwoBrings.CLASS_TWO_BRINGS,
            CollectingCourse.COLLECTING_COURSE,
            CollectionProgrammeConfig.COLLECTION_PROGRAMME_CONFIG,
            CollectionProgrammeCourse.COLLECTION_PROGRAMME_COURSE,
            CollegeTeaching.COLLEGE_TEACHING,
            ConfigruationHistory.CONFIGRUATION_HISTORY,
            Configuration.CONFIGURATION,
            ConfigurationValue.CONFIGURATION_VALUE,
            CorporateSegmentRelation.CORPORATE_SEGMENT_RELATION,
            CourseAttach.COURSE_ATTACH,
            CourseAttribute.COURSE_ATTRIBUTE,
            CourseCategory.COURSE_CATEGORY,
            CourseInfo.COURSE_INFO,
            CourseSalary.COURSE_SALARY,
            CourseTeachingActivities.COURSE_TEACHING_ACTIVITIES,
            DemandSideOrganization.DEMAND_SIDE_ORGANIZATION,
            Dimension.DIMENSION,
            DimensionQuestion.DIMENSION_QUESTION,
            Evaluate.EVALUATE,
            F2fCourse.F2F_COURSE,
            F2fCourseLibrary.F2F_COURSE_LIBRARY,
            GrantDetail.GRANT_DETAIL,
            GroupConfiguration.GROUP_CONFIGURATION,
            GroupConfigurationValue.GROUP_CONFIGURATION_VALUE,
            HomeLecturer.HOME_LECTURER,
            Job.JOB,
            Label.LABEL,
            Lecturer.LECTURER,
            LecturerAdeptCourse.LECTURER_ADEPT_COURSE,
            LecturerAttribute.LECTURER_ATTRIBUTE,
            LecturerCourseConfig.LECTURER_COURSE_CONFIG,
            LecturerLabel.LECTURER_LABEL,
            LecturerReleaseAuthority.LECTURER_RELEASE_AUTHORITY,
            LecturerThumbsUp.LECTURER_THUMBS_UP,
            Level.LEVEL,
            LimitConfiguration.LIMIT_CONFIGURATION,
            LimitDefaultConfiguration.LIMIT_DEFAULT_CONFIGURATION,
            Member.MEMBER,
            MemberConfig.MEMBER_CONFIG,
            MessageDetail.MESSAGE_DETAIL,
            MessageRecord.MESSAGE_RECORD,
            Organization.ORGANIZATION,
            OrganizationDetail.ORGANIZATION_DETAIL,
            OrganizationTeaching.ORGANIZATION_TEACHING,
            OtherTeaching.OTHER_TEACHING,
            PccwResult.PCCW_RESULT,
            Position.POSITION,
            Project.PROJECT,
            ProjectApproval.PROJECT_APPROVAL,
            ProjectHistory.PROJECT_HISTORY,
            ProjectOccupy.PROJECT_OCCUPY,
            Question.QUESTION,
            QuestionnaireQuestionType.QUESTIONNAIRE_QUESTION_TYPE,
            QuestionAttr.QUESTION_ATTR,
            RedPavilion.RED_PAVILION,
            ResearchAnswerRecord.RESEARCH_ANSWER_RECORD,
            ResearchQuestionary.RESEARCH_QUESTIONARY,
            ResearchRecord.RESEARCH_RECORD,
            SalaryHistory.SALARY_HISTORY,
            Settlement.SETTLEMENT,
            SettlementConfiguration.SETTLEMENT_CONFIGURATION,
            SettlementConfigurationValue.SETTLEMENT_CONFIGURATION_VALUE,
            Sign.SIGN,
            SignDetail.SIGN_DETAIL,
            SignLeave.SIGN_LEAVE,
            StudentDetail.STUDENT_DETAIL,
            StudentDetailTotal.STUDENT_DETAIL_TOTAL,
            StudentDetailTotalSort.STUDENT_DETAIL_TOTAL_SORT,
            StudentHistory.STUDENT_HISTORY,
            Task.TASK,
            TaskApproval.TASK_APPROVAL,
            TaskAttach.TASK_ATTACH,
            TaskMember.TASK_MEMBER,
            TaskReviewer.TASK_REVIEWER,
            Trainee.TRAINEE,
            TraineeGroup.TRAINEE_GROUP,
            StudyTeam.STUDY_TEAM,
            StudyTeamActivity.STUDY_TEAM_ACTIVITY,
            StudyTeamActivityTask.STUDY_TEAM_ACTIVITY_TASK,
            StudyTeamMember.STUDY_TEAM_MEMBER,
            StudyTeamMemberSign.STUDY_TEAM_MEMBER_SIGN,
            StudyTeamMemberSignLog.STUDY_TEAM_MEMBER_SIGN_LOG,
            StudyTeamAchievement.STUDY_TEAM_ACHIEVEMENT,
            StudyTeamAchievementPraise.STUDY_TEAM_ACHIEVEMENT_PRAISE,
            StudyTeamAchievementReply.STUDY_TEAM_ACHIEVEMENT_REPLY,
            StudyTeamActivityAttachment.STUDY_TEAM_ACTIVITY_ATTACHMENT,
            StudyTeamActivityPhotos.STUDY_TEAM_ACTIVITY_PHOTOS,
            SettlementMemberQuantity.SETTLEMENT_MEMBER_QUANTITY,
            StudyTeamLeaderConfirmDetail.STUDY_TEAM_LEADER_CONFIRM_DETAIL,
            DeleteDataTrain.DELETE_DATA_TRAIN,
            SolutionConfiguration.SOLUTION_CONFIGURATION,
            TrainingType.TRAINING_TYPE,
            ClassGradesProject.CLASS_GRADES_PROJECT,
            ClassGradesProjectMember.CLASS_GRADES_PROJECT_MEMBER,
            TrainProject.TRAIN_PROJECT,
            PlanningImplementation.PLANNING_IMPLEMENTATION,
            PlanningImplementationRelated.PLANNING_IMPLEMENTATION_RELATED,
            TrainChatGroup.TRAIN_CHAT_GROUP,
            TrainChatGroupInfo.TRAIN_CHAT_GROUP_INFO,
            ClassResearchSatisfaction.CLASS_RESEARCH_SATISFACTION,
            AcademicStatisticsInfo.ACADEMIC_STATISTICS_INFO,
            StudyReportTrain_2025.STUDY_REPORT_TRAIN_2025,
            StudyReportTrain_2026.STUDY_REPORT_TRAIN_2026,
            StudyReportTrain_2027.STUDY_REPORT_TRAIN_2027,
            StudyReportTrain_2028.STUDY_REPORT_TRAIN_2028,
            StudyReportTrain_2029.STUDY_REPORT_TRAIN_2029,
                ClassPopMangement.CLASS_POP_MANGEMENT
        );
    }
}
