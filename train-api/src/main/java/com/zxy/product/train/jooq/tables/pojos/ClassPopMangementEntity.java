/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.pojos;


import com.zxy.common.base.entity.BaseEntity;
import com.zxy.product.train.jooq.tables.interfaces.IClassPopMangement;

import javax.annotation.Generated;


/**
 * 学员管理弹窗管理
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ClassPopMangementEntity extends BaseEntity implements IClassPopMangement {

    private static final long serialVersionUID = 1L;

    private String memberId;
    private Integer   type;
    private Integer   flag;
    private String classId;

    public ClassPopMangementEntity() {}

    public ClassPopMangementEntity(ClassPopMangementEntity value) {
        this.memberId = value.memberId;
        this.type = value.type;
        this.flag = value.flag;
        this.classId = value.classId;
    }

    public ClassPopMangementEntity(
        String id,
        String memberId,
        Integer   type,
        Integer   flag,
        String classId,
        Long   createTime
    ) {
        super.setId(id);
        this.memberId = memberId;
        this.type = type;
        this.flag = flag;
        this.classId = classId;
        super.setCreateTime(createTime);
    }

    @Override
    public String getId() {
        return super.getId();
    }

    @Override
    public void setId(String id) {
        super.setId(id);
    }

    @Override
    public String getMemberId() {
        return this.memberId;
    }

    @Override
    public void setMemberId(String memberId) {
        this.memberId = memberId;
    }

    @Override
    public Integer getType() {
        return this.type;
    }

    @Override
    public void setType(Integer type) {
        this.type = type;
    }

    @Override
    public Integer getFlag() {
        return this.flag;
    }

    @Override
    public void setFlag(Integer flag) {
        this.flag = flag;
    }

    @Override
    public String getClassId() {
        return this.classId;
    }

    @Override
    public void setClassId(String classId) {
        this.classId = classId;
    }

    @Override
    public Long getCreateTime() {
        return super.getCreateTime();
    }

    @Override
    public void setCreateTime(Long createTime) {
        super.setCreateTime(createTime);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("ClassPopMangementEntity (");

        sb.append(getId());
        sb.append(", ").append(memberId);
        sb.append(", ").append(type);
        sb.append(", ").append(flag);
        sb.append(", ").append(classId);
        sb.append(", ").append(getCreateTime());

        sb.append(")");
        return sb.toString();
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(IClassPopMangement from) {
        setId(from.getId());
        setMemberId(from.getMemberId());
        setType(from.getType());
        setFlag(from.getFlag());
        setClassId(from.getClassId());
        setCreateTime(from.getCreateTime());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends IClassPopMangement> E into(E into) {
        into.from(this);
        return into;
    }

    public void forInsert() {
        this.setId(java.util.UUID.randomUUID().toString());
        this.setCreateTime(System.currentTimeMillis());
    }

    public static final <E extends ClassPopMangementEntity> org.jooq.RecordMapper<? extends org.jooq.Record, E> createMapper(Class<E> type) {
        return new org.jooq.RecordMapper<org.jooq.Record, E>() {
            @Override
            public E map(org.jooq.Record record) {
                com.zxy.product.train.jooq.tables.records.ClassPopMangementRecord r = new com.zxy.product.train.jooq.tables.records.ClassPopMangementRecord();
                org.jooq.Row row = record.fieldsRow();
                    if(row.indexOf(com.zxy.product.train.jooq.tables.ClassPopMangement.CLASS_POP_MANGEMENT.ID) > -1){
                        r.setValue(com.zxy.product.train.jooq.tables.ClassPopMangement.CLASS_POP_MANGEMENT.ID, record.getValue(com.zxy.product.train.jooq.tables.ClassPopMangement.CLASS_POP_MANGEMENT.ID));
                    }
                    if(row.indexOf(com.zxy.product.train.jooq.tables.ClassPopMangement.CLASS_POP_MANGEMENT.MEMBER_ID) > -1){
                        r.setValue(com.zxy.product.train.jooq.tables.ClassPopMangement.CLASS_POP_MANGEMENT.MEMBER_ID, record.getValue(com.zxy.product.train.jooq.tables.ClassPopMangement.CLASS_POP_MANGEMENT.MEMBER_ID));
                    }
                    if(row.indexOf(com.zxy.product.train.jooq.tables.ClassPopMangement.CLASS_POP_MANGEMENT.TYPE) > -1){
                        r.setValue(com.zxy.product.train.jooq.tables.ClassPopMangement.CLASS_POP_MANGEMENT.TYPE, record.getValue(com.zxy.product.train.jooq.tables.ClassPopMangement.CLASS_POP_MANGEMENT.TYPE));
                    }
                    if(row.indexOf(com.zxy.product.train.jooq.tables.ClassPopMangement.CLASS_POP_MANGEMENT.FLAG) > -1){
                        r.setValue(com.zxy.product.train.jooq.tables.ClassPopMangement.CLASS_POP_MANGEMENT.FLAG, record.getValue(com.zxy.product.train.jooq.tables.ClassPopMangement.CLASS_POP_MANGEMENT.FLAG));
                    }
                    if(row.indexOf(com.zxy.product.train.jooq.tables.ClassPopMangement.CLASS_POP_MANGEMENT.CLASS_ID) > -1){
                        r.setValue(com.zxy.product.train.jooq.tables.ClassPopMangement.CLASS_POP_MANGEMENT.CLASS_ID, record.getValue(com.zxy.product.train.jooq.tables.ClassPopMangement.CLASS_POP_MANGEMENT.CLASS_ID));
                    }
                    if(row.indexOf(com.zxy.product.train.jooq.tables.ClassPopMangement.CLASS_POP_MANGEMENT.CREATE_TIME) > -1){
                        r.setValue(com.zxy.product.train.jooq.tables.ClassPopMangement.CLASS_POP_MANGEMENT.CREATE_TIME, record.getValue(com.zxy.product.train.jooq.tables.ClassPopMangement.CLASS_POP_MANGEMENT.CREATE_TIME));
                    }
                try {
                    E pojo = type.newInstance();
                    pojo.from(r);
                    return pojo;
                } catch (Exception e) {
                    e.printStackTrace(); // ignored
                }
                return null;
            }
        };
    }}
