package com.zxy.product.train.entity;

import com.zxy.product.train.jooq.tables.pojos.ClassEvaluateEntity;

/**
 * 班级问卷安排
 * <AUTHOR>
 *
 */
public class ClassEvaluate extends ClassEvaluateEntity {

    private static final long serialVersionUID = 7829688417423984143L;

    public static final Integer TYPE_EXAM = 1;//考试
    public static final Integer TYPE_SURVEY = 2;//调研
    public static final Integer TYPE_EVALUATE = 3;//评估
    public static final Integer TYPE_EVA_STU = 4;//学员满意度评估
    public static final Integer TYPE_EVA_FDQ = 5;//四度问卷
    public static final Integer TYPE_EVA_AAQ = 6;//能力习得问卷
    public static final Integer TYPE_EVA_SLQ = 7;//领导问卷
    public static final Integer TYPE_EVA_STU_NEW = 8;//最新学员满意度评估

    public static final Integer DELETE_FALSE = 0;
    public static final Integer DELETE_TRUE = 1;
    public static final String MANYI = "manyi";
    public static final Integer NO_RELEASE = 0;
    public static final Integer RELEASS = 1;

    /** 是否为新增（0否 1是）*/
    public static final Integer IS_ADD_NO = 0;
    public static final Integer IS_ADD_YES = 1;


    public static Integer EVA_STU_DATE=20190512;//报道日期，如果是此后报到

    private Long arriveDate; //报到日
    private String detail; //为学员端的满意度问卷提供的参数
    private Long returnDate; //返程日
    private Double feedbackRate;
    private Double fdqDoubule;//四度问卷提交数
    private Double flqDoubule;//领导问卷提交数
    private Double aaqDoubule;//能力问卷提交数
    private String classInfoName;
    private boolean questionNum; //是否有人答卷
    private boolean isModifiable; //当前用户答卷

    private ResearchQuestionary researchQuestionary;

    private Float response;//反馈率

    private Integer evaluateStatus;//问卷参与状况
    private Integer score;//考试得分

    private Integer recorde; // 用于查询每个班级是否有人参加

    private Integer isPartyCadre;//是否是党干部进修班 0否 1是

    private Integer isEnsemble;//是否是整体满意度问卷  0周问卷 1总体问卷

    private Long examCreateTime; // 考试创建时间

    private Integer examedTimes; // 已考次数

    private Integer examAllowExamTimes; // 允许考试次数

    private Integer isOverByPassExam; // 是否考试及格才算完成

    private Integer view;

    public Integer getView() {
        return view;
    }

    public void setView(Integer view) {
        this.view = view;
    }

    public Long getExamCreateTime() {
        return examCreateTime;
    }

    public void setExamCreateTime(Long examCreateTime) {
        this.examCreateTime = examCreateTime;
    }

    public Integer getExamedTimes() {
        return examedTimes;
    }

    public void setExamedTimes(Integer examedTimes) {
        this.examedTimes = examedTimes;
    }

    public Integer getExamAllowExamTimes() {
        return examAllowExamTimes;
    }

    public void setExamAllowExamTimes(Integer examAllowExamTimes) {
        this.examAllowExamTimes = examAllowExamTimes;
    }

    public Integer getIsOverByPassExam() {
        return isOverByPassExam;
    }

    public void setIsOverByPassExam(Integer isOverByPassExam) {
        this.isOverByPassExam = isOverByPassExam;
    }

    public ResearchQuestionary getResearchQuestionary() {
		return researchQuestionary;
	}

	public void setResearchQuestionary(ResearchQuestionary researchQuestionary) {
		this.researchQuestionary = researchQuestionary;
	}

	public Float getResponse() {
		return response;
	}

	public void setResponse(Float response) {
		this.response = response;
	}

    public Double getFeedbackRate() {
        return feedbackRate;
    }

    public void setFeedbackRate(Double feedbackRate) {
        this.feedbackRate = feedbackRate;
    }

    public Long getArriveDate() {
        return arriveDate;
    }

    public void setArriveDate(Long arriveDate) {
        this.arriveDate = arriveDate;
    }

    public String getDetail() {
        return detail;
    }

    public void setDetail(String detail) {
        this.detail = detail;
    }

    public Long getReturnDate() {
        return returnDate;
    }

    public Double getFdqDoubule() {
        return fdqDoubule;
    }

    public void setFdqDoubule(Double fdqDoubule) {
        this.fdqDoubule = fdqDoubule;
    }

    public Double getFlqDoubule() {
        return flqDoubule;
    }

    public void setFlqDoubule(Double flqDoubule) {
        this.flqDoubule = flqDoubule;
    }

    public Double getAaqDoubule() {
        return aaqDoubule;
    }

    public void setAaqDoubule(Double aaqDoubule) {
        this.aaqDoubule = aaqDoubule;
    }

    public void setReturnDate(Long returnDate) {
        this.returnDate = returnDate;
    }

    public String getClassInfoName() {

        return classInfoName;
    }

    public void setClassInfoName(String classInfoName) {
        this.classInfoName = classInfoName;
    }

    public Integer getEvaluateStatus() {
        return evaluateStatus;
    }

    public void setEvaluateStatus(Integer evaluateStatus) {
        this.evaluateStatus = evaluateStatus;
    }

    public Integer getScore() {
        return score;
    }

    public void setScore(Integer score) {
        this.score = score;
    }

    public Integer getRecorde() {
        return recorde;
    }

    public void setRecorde(Integer recorde) {
        this.recorde = recorde;
    }

    public boolean isQuestionNum() {
        return questionNum;
    }

    public void setQuestionNum(boolean questionNum) {
        this.questionNum = questionNum;
    }

	public boolean isModifiable() {
		return isModifiable;
	}

	public void setModifiable(boolean isModifiable) {
		this.isModifiable = isModifiable;
	}

    public static long getSerialVersionUID() {
        return serialVersionUID;
    }

    public Integer getIsPartyCadre() {
        return isPartyCadre;
    }

    public void setIsPartyCadre(Integer isPartyCadre) {
        this.isPartyCadre = isPartyCadre;
    }

    public Integer getIsEnsemble() {
        return isEnsemble;
    }

    public void setIsEnsemble(Integer isEnsemble) {
        this.isEnsemble = isEnsemble;
    }
}
