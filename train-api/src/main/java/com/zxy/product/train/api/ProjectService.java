package com.zxy.product.train.api;

import com.zxy.common.base.annotation.RemoteService;
import com.zxy.common.base.helper.PagedResult;
import com.zxy.product.train.entity.Project;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;

/**
 * lulu by 田聪 on 2017/2/8.
 */
@RemoteService
public interface ProjectService {

	/**
	 * 按条件分页查询计划信息（后台计划列表）
	 *
	 * @param page
	 * @param pageSize
	 * @return
	 */
	// 分页参数
	// 参数： MIS编码、班级名称、主办部门、计划年份、计划月份、审核结果、组织id
	// 参数： 实施年份、实施月份、班级状态、
	// 参数： 当前登录用户id
	// 返回结果：id、培训班名称、MIS编码、需求方联系人、需求方单位、计划年份、计划月份、报到日、返程日、审核结果、实施状态、计划人数、计划天数、班主任
	@Transactional(readOnly = true,propagation = Propagation.SUPPORTS)
	PagedResult<Project> findGround(int page, int pageSize,Optional<String> code, Optional<String> className
	        , Optional<String> demandId,
			Optional<Integer> planYear, Optional<Integer> planMonth, Optional<Integer> auditStatus,
			Optional<String> organizationId, Optional<Integer> reachYear, Optional<Integer> reachMonth,
			Optional<Integer> classStatus, Map<String, Set<String>> grantOrganizationPathMap);


	/**
	 * 按条件分页查询计划信息（需求方）
	 *
	 * @param page
	 * @param pageSize
	 * @return
	 */
	// 分页参数
	// 参数： MIS编码、班级名称、主办部门、计划年份、计划月份、审核结果、组织id
	// 参数： 实施年份、实施月份、班级状态、
	// 参数： 当前登录用户id
	// 返回结果：id、培训班名称、MIS编码、需求方联系人、需求方单位、计划年份、计划月份、报到日、返程日、审核结果、实施状态、计划人数、计划天数、班主任
	@Transactional(readOnly = true,propagation = Propagation.SUPPORTS)
	PagedResult<Project> find(int page, int pageSize,Optional<String> code, Optional<String> className, Optional<String> demandId,
			Optional<Integer> planYear, Optional<Integer> planMonth, Optional<Integer> auditStatus,
			Optional<String> organizationId, Optional<Integer> reachYear, Optional<Integer> reachMonth,
			Optional<Integer> classStatus, Optional<String> memberId);

	/**
	 * 新增
	 * @param name			          班级名称
	 * @param code        		编码
	 * @param object            培训对象
	 * @param organizationId	需求单位
	 * @param contactMemberId   需求单位联系人
	 * @param contactPhone      联系电话
	 * @param contactEmail      联系邮箱
	 * @param year				计划年份
	 * @param month 			计划月份
	 * @param amount			计划人数
	 * @param days  			培训天数
	 * @param address			培训地点
	 * @param typeId			培训类型
	 * @return
	 */
	@Transactional
	Project insert(String name, Optional<String> code, String object, String organizationId, String contactMemberId,
			String contactPhone, Optional<String> contactEmail, Integer year, Integer month, Integer amount, Integer days,
			String address, String typeId,String cost, Optional<String> createMemberId);
	/**
	 * 根据ID查找
	 */
	@Transactional(readOnly = true,propagation = Propagation.SUPPORTS)
	Project get(String id);

	/**
     * 编码不能重复
     */
	@Transactional(readOnly = true,propagation = Propagation.SUPPORTS)
    int checkCode(String code, Optional<String> id);

    /**
	 * 根据ID更新
	 * @param name				班级名称
	 * @param code  			MIS编号
	 * @param contactEmail  	邮箱
	 * @param contactMemberId   需求单位联系人
	 * @param organizationId	需求单位
	 * @param contactPhone		联系电话
	 * @param year				计划年份
	 * @param month 			计划月份
	 * @param amount			计划人数
	 * @param object			培训对象
	 * @param days           	培训天数
	 * @param address   		培训地点
	 * @param typeId			培训类型
	 * @param isOutSide         是否外部举办
	 * @param surveyType        需求调研方式
	 * @param arriveDate        报道日
	 * @param returnDate        返程日
	 * @param target            培训目标
	 * @param status            状态
	 * @return
	 */
	@Transactional
	Project updateOne(String id, Optional<String> name, Optional<String> code, Optional<String> contactEmail, Optional<String> contactMemberId, Optional<String> organizationId,
	        Optional<String> contactPhone, Optional<Integer> year, Optional<Integer> month, Optional<Integer> amount, Optional<String> object, Optional<Integer> days,
	        Optional<String> address, Optional<String> typeId, Optional<Integer> isOutSide, Optional<String> surveyType, Optional<Long> arriveDate, Optional<Long> returnDate, Optional<String> target, Optional<Integer> status,
	        Optional<String> cost,Optional<Integer> beforeAmount,Optional<String> createMember);
	@Transactional
	Project updateNameAndCode(String id,String name,Optional<String> code);

	/**
	 * 根据ID更新
	 * @param name				班级名称
	 * @param code  			MIS编号
	 * @param contactEmail  	邮箱
	 * @param contactMemberId   需求单位联系人
	 * @param organizationId	需求单位
	 * @param contactPhone		联系电话
	 * @param year				计划年份
	 * @param month 			计划月份
	 * @param amount			计划人数
	 * @param object			培训对象
	 * @param days           	培训天数
	 * @param address   		培训地点
	 * @param typeId			培训类型
	 * @param isOutSide         是否外部举办
	 * @param surveyType        需求调研方式
	 * @param arriveDate        报道日
	 * @param returnDate        返程日
	 * @param target            培训目标
	 * @param status            状态
	 * @return
	 */
	@Transactional
	Project update(String id, Optional<String> name, Optional<String> code, Optional<String> contactEmail, Optional<String> contactMemberId, Optional<String> organizationId,
	        Optional<String> contactPhone, Optional<Integer> year, Optional<Integer> month, Optional<Integer> amount, Optional<String> object, Optional<Integer> days,
	        Optional<String> address, Optional<String> typeId, Optional<Integer> isOutSide, Optional<String> surveyType, Optional<Long> arriveDate, Optional<Long> returnDate, Optional<String> target, Optional<Integer> status,
	        Optional<String> cost,Optional<Integer> beforeAmount,Optional<String> createMember, Optional<Integer> resourceStatus);

	/**
	 * 根据ID预定/审核
	 * @param arriveDate		报到日
	 * @param returnDate        返程日
	 * @param isOutside   		培训方式(原是否外部举办)
	 * @param level			           培训级别
	 * @param address			学习地点
	 * @param studentType       人员类型
	 * @param simpleType   		补贴类型
	 * @param status			审核状态
	 * @param suggestion	           审核意见
	 * @return
	 */
	@Transactional
	Project approval(String id, Long arriveDate, Long returnDate,
			Integer isOutside, String address,
			Integer status, Optional<String> suggestion,Optional<String> surveyType,Optional<String> target,String stu, Optional<String> createMemberId, Optional<Integer> resourceStatus,Integer isPartyCadre);


	@Transactional
	Project approval(String id, Long arriveDate, Long returnDate, Integer isOutside,
					 String address, Integer status, Optional<String> suggestion, Optional<String> surveyType, Optional<String> target, String stas, Optional<String> createMemberId, Optional<Integer> resourceStatus, Integer isPartyCadre,
					 Optional<Integer> aduitStatus);

	/**
	 * 根据年份和月份查询在当月举办的培训班
	 * @param year     年份
	 * @param month    月份
	 * @return
	 */
	@Transactional(readOnly = true,propagation = Propagation.SUPPORTS)
	List<Project> findProjectByDate(Integer year, Integer month);

	@Transactional(readOnly = true,propagation = Propagation.SUPPORTS)
    Optional<Project> findByCode(String code);

	/**
	 * 用户是否为需求方
	 * @param memberId
	 * @return
	 */
	@Transactional(readOnly = true,propagation = Propagation.SUPPORTS)
    Integer findContract(String memberId);

	@Transactional
	int delete(String id);

	@Transactional(readOnly = true,propagation = Propagation.SUPPORTS)
	List<Project> findDownProject(Optional<Long> arriveDate,String memberId,List<String> organizationIds);

	@Transactional(readOnly = true,propagation = Propagation.SUPPORTS)
	List<Project> findDownYM(Integer year, Integer month);
	@Transactional
	void save(List<Project> projects);
	@Transactional
	List<Project> updateMember(String memberId);

	/**
     * add by acong 2018/01/19
     */
	@Transactional(readOnly = true,propagation = Propagation.SUPPORTS)
	String[] findMemberIdsByProjectIds(String[] projectIds);
	
	/**
     * add by lulu 2018/01/25通过需求方联系人ID查询联系电话
     */
	@Transactional(readOnly = true,propagation = Propagation.SUPPORTS)
	String[] findTepIdsByProjectIds(String[] projectIds);

	@Transactional(readOnly = true)
    Project getProjectNameAndOrgNameAndImplementationMonth(String classId);
}
