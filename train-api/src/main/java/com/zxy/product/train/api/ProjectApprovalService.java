package com.zxy.product.train.api;

import java.util.List;
import java.util.Optional;

import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.zxy.common.base.annotation.RemoteService;
import com.zxy.product.train.entity.ProjectApproval;

/**
 * 
 * <AUTHOR> 计划审核记录
 *
 */
@RemoteService
public interface ProjectApprovalService {

	@Transactional(readOnly = true,propagation = Propagation.SUPPORTS)
	List<ProjectApproval> find(String id);

	@Transactional
	ProjectApproval insert(String projectId, Integer status, Optional<String> suggestion, Optional<String> approvalMemberId, Optional<String> createMemberId);

	@Transactional
    List<ProjectApproval> findByProjectIds(List<String> projectIds);

	@Transactional
    void update(String projectId, Integer status, Optional<String> approvalMemberId);
}
