/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables;


import com.zxy.product.train.jooq.Keys;
import com.zxy.product.train.jooq.Train;
import com.zxy.product.train.jooq.tables.records.ClassPopMangementRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * 学员管理弹窗管理
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ClassPopMangement extends TableImpl<ClassPopMangementRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>train.t_class_pop_mangement</code>
     */
    public static final ClassPopMangement CLASS_POP_MANGEMENT = new ClassPopMangement();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<ClassPopMangementRecord> getRecordType() {
        return ClassPopMangementRecord.class;
    }

    /**
     * The column <code>train.t_class_pop_mangement.f_id</code>. 表id
     */
    public final TableField<ClassPopMangementRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "表id");

    /**
     * The column <code>train.t_class_pop_mangement.f_member_id</code>. 人员id
     */
    public final TableField<ClassPopMangementRecord, String> MEMBER_ID = createField("f_member_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "人员id");

    /**
     * The column <code>train.t_class_pop_mangement.f_type</code>. 班务人员类型：0.管理员 1.班主任
     */
    public final TableField<ClassPopMangementRecord, Integer> TYPE = createField("f_type", new org.jooq.impl.DefaultDataType((org.jooq.SQLDialect)null, Integer.class, "tinyint").defaultValue(org.jooq.impl.DSL.inline("0", new org.jooq.impl.DefaultDataType((org.jooq.SQLDialect)null, Integer.class, "tinyint"))), this, "班务人员类型：0.管理员 1.班主任");

    /**
     * The column <code>train.t_class_pop_mangement.f_flag</code>. 弹窗设置 0 弹出 1 关闭
     */
    public final TableField<ClassPopMangementRecord, Integer> FLAG = createField("f_flag", new org.jooq.impl.DefaultDataType((org.jooq.SQLDialect)null, Integer.class, "tinyint").defaultValue(org.jooq.impl.DSL.inline("0", new org.jooq.impl.DefaultDataType((org.jooq.SQLDialect)null, Integer.class, "tinyint"))), this, "弹窗设置 0 弹出 1 关闭");

    /**
     * The column <code>train.t_class_pop_mangement.f_class_id</code>. 班级id
     */
    public final TableField<ClassPopMangementRecord, String> CLASS_ID = createField("f_class_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "班级id");

    /**
     * The column <code>train.t_class_pop_mangement.f_create_time</code>. 创建时间
     */
    public final TableField<ClassPopMangementRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "创建时间");

    /**
     * Create a <code>train.t_class_pop_mangement</code> table reference
     */
    public ClassPopMangement() {
        this("t_class_pop_mangement", null);
    }

    /**
     * Create an aliased <code>train.t_class_pop_mangement</code> table reference
     */
    public ClassPopMangement(String alias) {
        this(alias, CLASS_POP_MANGEMENT);
    }

    private ClassPopMangement(String alias, Table<ClassPopMangementRecord> aliased) {
        this(alias, aliased, null);
    }

    private ClassPopMangement(String alias, Table<ClassPopMangementRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "学员管理弹窗管理");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Train.TRAIN_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<ClassPopMangementRecord> getPrimaryKey() {
        return Keys.KEY_T_CLASS_POP_MANGEMENT_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<ClassPopMangementRecord>> getKeys() {
        return Arrays.<UniqueKey<ClassPopMangementRecord>>asList(Keys.KEY_T_CLASS_POP_MANGEMENT_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ClassPopMangement as(String alias) {
        return new ClassPopMangement(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public ClassPopMangement rename(String name) {
        return new ClassPopMangement(name, null);
    }
}
