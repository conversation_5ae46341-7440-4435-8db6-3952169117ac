/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.interfaces;


import javax.annotation.Generated;
import java.io.Serializable;


/**
 * 学员管理弹窗管理
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface IClassPopMangement extends Serializable {

    /**
     * Setter for <code>train.t_class_pop_mangement.f_id</code>. 表id
     */
    public void setId(String value);

    /**
     * Getter for <code>train.t_class_pop_mangement.f_id</code>. 表id
     */
    public String getId();

    /**
     * Setter for <code>train.t_class_pop_mangement.f_member_id</code>. 人员id
     */
    public void setMemberId(String value);

    /**
     * Getter for <code>train.t_class_pop_mangement.f_member_id</code>. 人员id
     */
    public String getMemberId();

    /**
     * Setter for <code>train.t_class_pop_mangement.f_type</code>. 班务人员类型：0.管理员 1.班主任
     */
    public void setType(Integer value);

    /**
     * Getter for <code>train.t_class_pop_mangement.f_type</code>. 班务人员类型：0.管理员 1.班主任
     */
    public Integer getType();

    /**
     * Setter for <code>train.t_class_pop_mangement.f_flag</code>. 弹窗设置 0 弹出 1 关闭
     */
    public void setFlag(Integer value);

    /**
     * Getter for <code>train.t_class_pop_mangement.f_flag</code>. 弹窗设置 0 弹出 1 关闭
     */
    public Integer getFlag();

    /**
     * Setter for <code>train.t_class_pop_mangement.f_class_id</code>. 班级id
     */
    public void setClassId(String value);

    /**
     * Getter for <code>train.t_class_pop_mangement.f_class_id</code>. 班级id
     */
    public String getClassId();

    /**
     * Setter for <code>train.t_class_pop_mangement.f_create_time</code>. 创建时间
     */
    public void setCreateTime(Long value);

    /**
     * Getter for <code>train.t_class_pop_mangement.f_create_time</code>. 创建时间
     */
    public Long getCreateTime();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface IClassPopMangement
     */
    public void from(IClassPopMangement from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface IClassPopMangement
     */
    public <E extends IClassPopMangement> E into(E into);
}
