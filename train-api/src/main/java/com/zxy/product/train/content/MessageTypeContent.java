package com.zxy.product.train.content;

/**
 * @user tianjun 消息常量
 */
public class MessageTypeContent {

    /**
     * 人资模块(HR) 1 课程模块(COURSE) 2 system 3 exam 4 course 5.ask-bar 6.train ...  8 log
     *
     * 01 project 计划
     * 02 class 班级
     * 03 course 面授课程
     * 04 online 在线课程
     * 05 teacher 讲师
     * 06 questionary 调研/评估/考试
     * 08 questionnaire 班级问卷
     * insert 01 delete 02 update 03
     */


    /*
     * 计划
     */
    public static final int TRAIN_PROJECT_INSERT = 60101;
    public static final int TRAIN_PROJECT_DELETE = 60102;
    public static final int TRAIN_PROJECT_UPDATE = 60103;
//    public static final int TRAIN_PROJECR_DEMAND_INSERT = 60104;

    /*
     * 班级
     */
    public static final int TRAIN_CLASS_INSERT = 60201;
    public static final int TRAIN_CLASS_DELETE = 60202;
    public static final int TRAIN_CLASS_UPDATE = 60203;
    public static final int TRAIN_CLASS_DELETE_SMART_CAMPUS = 60204;

    /*
     * 面授课程
     */
    public static final int TRAIN_COURSE_INSERT = 60301;
    public static final int TRAIN_COURSE_DELETE = 60302;
    public static final int TRAIN_COURSE_UPDATE = 60303;

    /*
     * 在线课程
     */
    public static final int TRAIN_ONLINE_INSERT = 60401;
    public static final int TRAIN_ONLINE_DELETE = 60402;
    public static final int TRAIN_ONLINE_UPDATE = 60403;

    /*
     * 讲师
     */
    public static final int TRAIN_TEACHER_INSERT = 60501;
    public static final int TRAIN_TEACHER_DELETE = 60502;
    public static final int TRAIN_TEACHER_UPDATE = 60503;
    public static final int HR_MEMBER_IDENTY_LOG_INSERT = 22301;//用户系统身份新增
    public static final int HR_MEMBER_IDENTY_LOG_UPDATE = 22302;//用户系统身份修改

    /*
     * 调研与评估
     */
    public static final int TRAIN_QUESTIONARY_INSERT = 60601;
    public static final int TRAIN_QUESTIONARY_DELETE = 60602;
    public static final int TRAIN_QUESTIONARY_UPDATE = 60603;

    /*
     * 发布通知
     */
    public static final int TRAIN_NOTICE_SEND = 60701;

    /**
     * 班级问卷
     */
    public static final int TRAIN_QUESTIONNAIRE_INSERT = 60801;
    public static final int TRAIN_QUESTIONNAIRE_UPDATE = 60802;
    public static final int TRAIN_QUESTIONNAIRE_MINIAPP_INSERT = 60803;

    /**
     * 作业提交和审核
     */
    public static final int CLASS_TASK_SUBMIT_AUDIT = 60901;

    /**
     * 删除学员
     */
    public static final int CLASS_DELETE_TRAINEE = 61001;

    /**
     * 修改报到日
     */
    public static final int UPDATE_ARRIVEDATE = 61002;

    /**
     * 提交满意度问卷
     */
    public static final int SUBMIT_SATISFACTION_QUESTIONNAIRE = 61003;

    /**
     * 生成能力习得问卷
     */
    public static final int COMPETENCY_QUESTIONNAIRE = 61004;

    /**
     * 提交能力习得问卷
     */
    public static final int SUBMIT_SATISFACTION_QUESTIONNAIREN = 61005;

    /**
     * 提交四度评估问卷
     */
    public static final int SUBMIT_SATISFACTION_QUESTIONNAIRES = 61006;

    /**
     * 提交领导问卷
     */
    public static final int SUBMIT_SATISFACTION_QUESTIONNAIREL = 61007;

    /**
     * 生成领导问卷
     */
    public static final int MAKE_QUESTIONNAIREL = 61008;

    /**
     * 生成领导问卷代办
     */
    public static final int MAKE_QUESTIONNAIREL_L = 61009;

    /**
     * 提交新版满意度问卷
     */
    public static final int SUBMIT_NEW_SATISFACTION_QUESTIONNAIRE = 61010;

    //根据周主题生成满意度问卷
    public static final int CREATE_NEW_SATISFACTION_QUESTIONNAIRE=61011;


    /**
     * 添加学员以及学员审核
     */
    public static final int CLASS_ADD_TRAINEE_BY_CODE = 61101;//通过编码添加学员


    /**
     * 我的任务修改计划名称，封面路径
     */
    public static final int TRAIN_UPDATE_PROJECT_NAME = 61111;
    public static final int TRAIN_UPDATE_CLASS_COVER = 61112;

    /**
     * 同步班级数据到活动中
     */
    public static final int TRAIN_CLASS_NOTICE_FOR_ACTIVITY = 61121;//发布通知的时候
    public static final int TRAIN_CLASS_UPDATE_FOR_ACTIVITY = 61122;//班级更新的时候
    public static final int TRAIN_CLASS_DELETE_FOR_ACTIVITY = 61123;//更新班级的报名方式为非开放报名时。删除活动中改班级

    public static final int TRAIN_PROJECT_APPROVAL_UPDATE = 61131;//新增或修改计划占用资源
    public static final int TRAIN_PROJECT_APPROVAL_DELETE = 61132;//释放资源

    public static final int SAVE_SATISFACTION_QUESTIONARE_ANSWER = 61141;//用于保存学员提交的满意度问卷
    public static final int SAVE_SATISFACTION_QUESTIONARE_ANSWERN = 61142;//用于保存学员提交能力习得问卷
    public static final int SAVE_SATISFACTION_QUESTIONARE_ANSWERS = 61143;//用于保存学员提交四度评估问卷
    public static final int SAVE_SATISFACTION_QUESTIONARE_ANSWERL = 61144;//用于保存领导提交的领导问卷

    /**
     * 上传文件
     */
    public static final int HR_FILE_PARSE = 61161;

    /**
     * 代办更新
     */
    public static final int AGENCY_CLASSID = 61162;

    /**
     * 满意度问卷统计
     */
    public static final int SATISFACTION_QUESTIONARE_STATISTICS = 61171;//用于保存学员提交的满意度问卷


    /**
     * 讲师库讲师授课记录变更
     */
    public static final int LECTURER_LECTURES_OF_RECORDS = 61201; // 讲师授课记录变更监听

    /**
     * 提交问卷，更新用户的待办消息数量
     */
    public static final int SUBMIT_RESEARCH_QUESTIONARY_NEW=61300;

    /**
     * 团队学习--确认学时
     */
    public static final int STUDY_TEAM_CONFIRMED_CREDITS_HOURS = 61401;

    /**
     * 团队学习班--学习成果回复数
     */
    public static final int STUDY_TEAM_ACHIEVEMENT_COMMENT_COUNT = 61402;

    /**
     * 团队学习班--学习成果点赞数
     */
    public static final int STUDY_TEAM_ACHIEVEMENT_PRAISE_COUNT = 61403;

    /**
     * 团队学习班--学习成果回复点赞数
     */
    public static final int STUDY_TEAM_ACHIEVEMENT_REPLY_PRAISE_COUNT = 61404;

    //推送团队学习班学习计划 insert
    public static final int TRAIN_STUDY_PLAN_CONFIG_INSERT = 24031;
    //推送团队学习班学习计划 revoke
    public static final int TRAIN_STUDY_PLAN_CONFIG_REVOKE = 24032;
    //团队学习班学习计划更新
    public static final int TRAIN_STUDY_PLAN_UPDATE = 24033;

    //培训班新增学员信息
    public static final int TRAIN_CLASS_STUDENT_INSERT = 62001;
    //培训班更新学员信息
    public static final int TRAIN_CLASS_STUDENT_UPDATE = 62002;
    //培训班删除学员信息
    public static final int TRAIN_CLASS_STUDENT_DELETE = 62003;

    //培训项目新建同步
    public static final int TRAIN_PROJECT_SYNCHRONOUS= 63001;
    //新建班级同步策划实施
    public static final int PLA_IMPLEMENTATION_SYNCHRONOUS= 63002;

    //IM群聊成员新增
    public static final int CHAT_GROUP_MEMBER_ADD= 64001;
    //IM群聊成员更新
    public static final int CHAT_GROUP_MEMBER_UPDATE= 64002;
    //IM群聊成员删除
    public static final int CHAT_GROUP_MEMBER_DELETE= 64003;

    //自动创建群聊
    public static final int CHAT_GROUP_CREAT= 64009;

    /**系统首页配置更新*/
    public static final int HOME_SYSTEM=1000001;


}
