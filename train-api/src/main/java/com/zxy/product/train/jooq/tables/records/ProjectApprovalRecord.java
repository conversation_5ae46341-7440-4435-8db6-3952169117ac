/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.records;


import com.zxy.product.train.jooq.tables.ProjectApproval;
import com.zxy.product.train.jooq.tables.interfaces.IProjectApproval;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record9;
import org.jooq.Row9;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ProjectApprovalRecord extends UpdatableRecordImpl<ProjectApprovalRecord> implements Record9<String, String, Integer, String, String, Long, String, Integer, Long>, IProjectApproval {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>train.t_project_approval.f_id</code>. 主键
     */
    @Override
    public void setId(String value) {
        set(0, value);
    }

    /**
     * Getter for <code>train.t_project_approval.f_id</code>. 主键
     */
    @Override
    public String getId() {
        return (String) get(0);
    }

    /**
     * Setter for <code>train.t_project_approval.f_project_id</code>. 计划ID
     */
    @Override
    public void setProjectId(String value) {
        set(1, value);
    }

    /**
     * Getter for <code>train.t_project_approval.f_project_id</code>. 计划ID
     */
    @Override
    public String getProjectId() {
        return (String) get(1);
    }

    /**
     * Setter for <code>train.t_project_approval.f_status</code>. 状态（1待预订 2待审核 3同意申请 4资源已满 5不同意）
     */
    @Override
    public void setStatus(Integer value) {
        set(2, value);
    }

    /**
     * Getter for <code>train.t_project_approval.f_status</code>. 状态（1待预订 2待审核 3同意申请 4资源已满 5不同意）
     */
    @Override
    public Integer getStatus() {
        return (Integer) get(2);
    }

    /**
     * Setter for <code>train.t_project_approval.f_suggestion</code>. 审核意见
     */
    @Override
    public void setSuggestion(String value) {
        set(3, value);
    }

    /**
     * Getter for <code>train.t_project_approval.f_suggestion</code>. 审核意见
     */
    @Override
    public String getSuggestion() {
        return (String) get(3);
    }

    /**
     * Setter for <code>train.t_project_approval.f_approval_member</code>. 审核人
     */
    @Override
    public void setApprovalMember(String value) {
        set(4, value);
    }

    /**
     * Getter for <code>train.t_project_approval.f_approval_member</code>. 审核人
     */
    @Override
    public String getApprovalMember() {
        return (String) get(4);
    }

    /**
     * Setter for <code>train.t_project_approval.f_create_time</code>. 创建时间
     */
    @Override
    public void setCreateTime(Long value) {
        set(5, value);
    }

    /**
     * Getter for <code>train.t_project_approval.f_create_time</code>. 创建时间
     */
    @Override
    public Long getCreateTime() {
        return (Long) get(5);
    }

    /**
     * Setter for <code>train.t_project_approval.f_create_member</code>. 创建人ID
     */
    @Override
    public void setCreateMember(String value) {
        set(6, value);
    }

    /**
     * Getter for <code>train.t_project_approval.f_create_member</code>. 创建人ID
     */
    @Override
    public String getCreateMember() {
        return (String) get(6);
    }

    /**
     * Setter for <code>train.t_project_approval.f_delete_flag</code>. 删除标记（0未删除，1已删除）
     */
    @Override
    public void setDeleteFlag(Integer value) {
        set(7, value);
    }

    /**
     * Getter for <code>train.t_project_approval.f_delete_flag</code>. 删除标记（0未删除，1已删除）
     */
    @Override
    public Integer getDeleteFlag() {
        return (Integer) get(7);
    }

    /**
     * Setter for <code>train.t_project_approval.f_approval_time</code>. 审核时间
     */
    @Override
    public void setApprovalTime(Long value) {
        set(8, value);
    }

    /**
     * Getter for <code>train.t_project_approval.f_approval_time</code>. 审核时间
     */
    @Override
    public Long getApprovalTime() {
        return (Long) get(8);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Record1<String> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record9 type implementation
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Row9<String, String, Integer, String, String, Long, String, Integer, Long> fieldsRow() {
        return (Row9) super.fieldsRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Row9<String, String, Integer, String, String, Long, String, Integer, Long> valuesRow() {
        return (Row9) super.valuesRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field1() {
        return ProjectApproval.PROJECT_APPROVAL.ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field2() {
        return ProjectApproval.PROJECT_APPROVAL.PROJECT_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field3() {
        return ProjectApproval.PROJECT_APPROVAL.STATUS;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field4() {
        return ProjectApproval.PROJECT_APPROVAL.SUGGESTION;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field5() {
        return ProjectApproval.PROJECT_APPROVAL.APPROVAL_MEMBER;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Long> field6() {
        return ProjectApproval.PROJECT_APPROVAL.CREATE_TIME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field7() {
        return ProjectApproval.PROJECT_APPROVAL.CREATE_MEMBER;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field8() {
        return ProjectApproval.PROJECT_APPROVAL.DELETE_FLAG;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Long> field9() {
        return ProjectApproval.PROJECT_APPROVAL.APPROVAL_TIME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value1() {
        return getId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value2() {
        return getProjectId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value3() {
        return getStatus();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value4() {
        return getSuggestion();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value5() {
        return getApprovalMember();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Long value6() {
        return getCreateTime();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value7() {
        return getCreateMember();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value8() {
        return getDeleteFlag();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Long value9() {
        return getApprovalTime();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ProjectApprovalRecord value1(String value) {
        setId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ProjectApprovalRecord value2(String value) {
        setProjectId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ProjectApprovalRecord value3(Integer value) {
        setStatus(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ProjectApprovalRecord value4(String value) {
        setSuggestion(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ProjectApprovalRecord value5(String value) {
        setApprovalMember(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ProjectApprovalRecord value6(Long value) {
        setCreateTime(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ProjectApprovalRecord value7(String value) {
        setCreateMember(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ProjectApprovalRecord value8(Integer value) {
        setDeleteFlag(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ProjectApprovalRecord value9(Long value) {
        setApprovalTime(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ProjectApprovalRecord values(String value1, String value2, Integer value3, String value4, String value5, Long value6, String value7, Integer value8, Long value9) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        value7(value7);
        value8(value8);
        value9(value9);
        return this;
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(IProjectApproval from) {
        setId(from.getId());
        setProjectId(from.getProjectId());
        setStatus(from.getStatus());
        setSuggestion(from.getSuggestion());
        setApprovalMember(from.getApprovalMember());
        setCreateTime(from.getCreateTime());
        setCreateMember(from.getCreateMember());
        setDeleteFlag(from.getDeleteFlag());
        setApprovalTime(from.getApprovalTime());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends IProjectApproval> E into(E into) {
        into.from(this);
        return into;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached ProjectApprovalRecord
     */
    public ProjectApprovalRecord() {
        super(ProjectApproval.PROJECT_APPROVAL);
    }

    /**
     * Create a detached, initialised ProjectApprovalRecord
     */
    public ProjectApprovalRecord(String id, String projectId, Integer status, String suggestion, String approvalMember, Long createTime, String createMember, Integer deleteFlag, Long approvalTime) {
        super(ProjectApproval.PROJECT_APPROVAL);

        set(0, id);
        set(1, projectId);
        set(2, status);
        set(3, suggestion);
        set(4, approvalMember);
        set(5, createTime);
        set(6, createMember);
        set(7, deleteFlag);
        set(8, approvalTime);
    }

    public boolean fromPojo(Object source) {
        if (!(source instanceof com.zxy.product.train.jooq.tables.pojos.ProjectApprovalEntity)) {
            return false;
        }
        com.zxy.product.train.jooq.tables.pojos.ProjectApprovalEntity pojo = (com.zxy.product.train.jooq.tables.pojos.ProjectApprovalEntity)source;
        pojo.into(this);
        return true;
    }
}
