package com.zxy.product.train.api;

import com.zxy.common.base.annotation.RemoteService;
import com.zxy.product.train.entity.Settlement;
import com.zxy.product.train.entity.SettlementMemberQuantity;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

/**
 * Created by chun on 2017/3/7.
 */
@RemoteService
public interface SettlementService {

    @Transactional(readOnly = true,propagation = Propagation.SUPPORTS)
    Settlement get(String id);

    /**
     *
     * @param explain   //结算说明
     * @param peopleNumber//结算人数
     * @param dayNumber//结算天数
     * @param peopleDay//结算人日
     * @param classId//班级id
     * @param attendDays//上课天数
     * @return
     */
    @Transactional
    Settlement insert(Optional<String> explain, Optional<Integer> peopleNumber,
                      Optional<Double> dayNumber,Optional<Double> peopleDay,
                      Optional<String> createMouth,Optional<String> classId,
                      Optional<Double> trainDayNum,Optional<Double> attendDays,
                      Optional<Integer> settlementQuantity);

    @Transactional
    Settlement update(String id,Optional<String> explain, Optional<Integer> peopleNumber,
                      Optional<Double> dayNumber,Optional<Double> peopleDay,
                      Optional<String> createMouth,String classId,Optional<Double> trainDayNum,
                      Optional<Double> attendDays, Optional<Integer> settlementQuantity);

    /**
     * 查询制定班级是否提交结算数据
     * @param classId
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    Integer findSettlementCount(String classId);

    @Transactional
    Settlement update(String id,String createMouth);

    /**
     * 保存班务管理-结算数据-结算人数配置数据
     * @param  classId   班级id
     * @param settlementMemberQuantities  保存对象
     * @return
     */
    @Transactional
    void insertMemberQuantity(String classId, List<SettlementMemberQuantity> settlementMemberQuantities);

    /**
     * 查询班务管理-结算数据-结算人数配置数据
     * @param  classId   班级id
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    List<SettlementMemberQuantity> findMemberQuantity(String classId);

    /**
     * 查询多个班级的-结算配置
     * @param classIds
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    List<SettlementMemberQuantity> findMemberByClassIds(List<String> classIds);
}
