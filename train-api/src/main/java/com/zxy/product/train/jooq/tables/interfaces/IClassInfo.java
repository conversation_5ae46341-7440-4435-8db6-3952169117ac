/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.interfaces;


import javax.annotation.Generated;
import java.io.Serializable;
import java.sql.Timestamp;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface IClassInfo extends Serializable {

    /**
     * Setter for <code>train.t_class_info.f_id</code>. 表id
     */
    public void setId(String value);

    /**
     * Getter for <code>train.t_class_info.f_id</code>. 表id
     */
    public String getId();

    /**
     * Setter for <code>train.t_class_info.f_project_id</code>. 计划id
     */
    public void setProjectId(String value);

    /**
     * Getter for <code>train.t_class_info.f_project_id</code>. 计划id
     */
    public String getProjectId();

    /**
     * Setter for <code>train.t_class_info.f_class_teacher_phone</code>. 班主任电话
     */
    public void setClassTeacherPhone(String value);

    /**
     * Getter for <code>train.t_class_info.f_class_teacher_phone</code>. 班主任电话
     */
    public String getClassTeacherPhone();

    /**
     * Setter for <code>train.t_class_info.f_class_teacher</code>. 班主任
     */
    public void setClassTeacher(String value);

    /**
     * Getter for <code>train.t_class_info.f_class_teacher</code>. 班主任
     */
    public String getClassTeacher();

    /**
     * Setter for <code>train.t_class_info.f_arrive_date</code>. 报到日
     */
    public void setArriveDate(Long value);

    /**
     * Getter for <code>train.t_class_info.f_arrive_date</code>. 报到日
     */
    public Long getArriveDate();

    /**
     * Setter for <code>train.t_class_info.f_return_date</code>. 返程日
     */
    public void setReturnDate(Long value);

    /**
     * Getter for <code>train.t_class_info.f_return_date</code>. 返程日
     */
    public Long getReturnDate();

    /**
     * Setter for <code>train.t_class_info.f_is_outside</code>. 是否外部举办（0否，1是）
     */
    public void setIsOutside(Integer value);

    /**
     * Getter for <code>train.t_class_info.f_is_outside</code>. 是否外部举办（0否，1是）
     */
    public Integer getIsOutside();

    /**
     * Setter for <code>train.t_class_info.f_survey_type</code>. 需求调研方式
     */
    public void setSurveyType(String value);

    /**
     * Getter for <code>train.t_class_info.f_survey_type</code>. 需求调研方式
     */
    public String getSurveyType();

    /**
     * Setter for <code>train.t_class_info.f_target</code>. 培训目标
     */
    public void setTarget(String value);

    /**
     * Getter for <code>train.t_class_info.f_target</code>. 培训目标
     */
    public String getTarget();

    /**
     * Setter for <code>train.t_class_info.f_class_info_type</code>. 班级类别
     */
    public void setClassInfoType(String value);

    /**
     * Getter for <code>train.t_class_info.f_class_info_type</code>. 班级类别
     */
    public String getClassInfoType();

    /**
     * Setter for <code>train.t_class_info.f_student_type</code>. 人员类别
     */
    public void setStudentType(String value);

    /**
     * Getter for <code>train.t_class_info.f_student_type</code>. 人员类别
     */
    public String getStudentType();

    /**
     * Setter for <code>train.t_class_info.f_simple_type</code>. 补贴类型
     */
    public void setSimpleType(String value);

    /**
     * Getter for <code>train.t_class_info.f_simple_type</code>. 补贴类型
     */
    public String getSimpleType();

    /**
     * Setter for <code>train.t_class_info.f_is_plan</code>. 是否计划内
     */
    public void setIsPlan(Integer value);

    /**
     * Getter for <code>train.t_class_info.f_is_plan</code>. 是否计划内
     */
    public Integer getIsPlan();

    /**
     * Setter for <code>train.t_class_info.f_status</code>. 班级状态（1未实施、2实施中、3已实施）
     */
    public void setStatus(Integer value);

    /**
     * Getter for <code>train.t_class_info.f_status</code>. 班级状态（1未实施、2实施中、3已实施）
     */
    public Integer getStatus();

    /**
     * Setter for <code>train.t_class_info.f_confirm</code>. 是否提交（0否 1是）
     */
    public void setConfirm(Integer value);

    /**
     * Getter for <code>train.t_class_info.f_confirm</code>. 是否提交（0否 1是）
     */
    public Integer getConfirm();

    /**
     * Setter for <code>train.t_class_info.f_group_id</code>. 分组id
     */
    public void setGroupId(String value);

    /**
     * Getter for <code>train.t_class_info.f_group_id</code>. 分组id
     */
    public String getGroupId();

    /**
     * Setter for <code>train.t_class_info.f_short_name</code>. 短名称
     */
    public void setShortName(String value);

    /**
     * Getter for <code>train.t_class_info.f_short_name</code>. 短名称
     */
    public String getShortName();

    /**
     * Setter for <code>train.t_class_info.f_group_order</code>. 分组排序
     */
    public void setGroupOrder(Integer value);

    /**
     * Getter for <code>train.t_class_info.f_group_order</code>. 分组排序
     */
    public Integer getGroupOrder();

    /**
     * Setter for <code>train.t_class_info.f_create_time</code>. 创建时间
     */
    public void setCreateTime(Long value);

    /**
     * Getter for <code>train.t_class_info.f_create_time</code>. 创建时间
     */
    public Long getCreateTime();

    /**
     * Setter for <code>train.t_class_info.f_create_member</code>. 创建人
     */
    public void setCreateMember(String value);

    /**
     * Getter for <code>train.t_class_info.f_create_member</code>. 创建人
     */
    public String getCreateMember();

    /**
     * Setter for <code>train.t_class_info.f_delete_flag</code>. 删除标记（0未删除，1已删除）
     */
    public void setDeleteFlag(Integer value);

    /**
     * Getter for <code>train.t_class_info.f_delete_flag</code>. 删除标记（0未删除，1已删除）
     */
    public Integer getDeleteFlag();

    /**
     * Setter for <code>train.t_class_info.f_implementation_year</code>. 实施年
     */
    public void setImplementationYear(Integer value);

    /**
     * Getter for <code>train.t_class_info.f_implementation_year</code>. 实施年
     */
    public Integer getImplementationYear();

    /**
     * Setter for <code>train.t_class_info.f_implementation_month</code>. 实施月
     */
    public void setImplementationMonth(Integer value);

    /**
     * Getter for <code>train.t_class_info.f_implementation_month</code>. 实施月
     */
    public Integer getImplementationMonth();

    /**
     * Setter for <code>train.t_class_info.f_member_satisfaction</code>. 学员满意率
     */
    public void setMemberSatisfaction(Double value);

    /**
     * Getter for <code>train.t_class_info.f_member_satisfaction</code>. 学员满意率
     */
    public Double getMemberSatisfaction();

    /**
     * Setter for <code>train.t_class_info.f_class_satisfaction</code>. 课程满意率
     */
    public void setClassSatisfaction(Double value);

    /**
     * Getter for <code>train.t_class_info.f_class_satisfaction</code>. 课程满意率
     */
    public Double getClassSatisfaction();

    /**
     * Setter for <code>train.t_class_info.f_trainee_num</code>. 实际参训人数
     */
    public void setTraineeNum(Integer value);

    /**
     * Getter for <code>train.t_class_info.f_trainee_num</code>. 实际参训人数
     */
    public Integer getTraineeNum();

    /**
     * Setter for <code>train.t_class_info.f_submit_num</code>. 满意度问卷提交数
     */
    public void setSubmitNum(Integer value);

    /**
     * Getter for <code>train.t_class_info.f_submit_num</code>. 满意度问卷提交数
     */
    public Integer getSubmitNum();

    /**
     * Setter for <code>train.t_class_info.f_notice</code>. 是否发布 1：已发布
     */
    public void setNotice(Integer value);

    /**
     * Getter for <code>train.t_class_info.f_notice</code>. 是否发布 1：已发布
     */
    public Integer getNotice();

    /**
     * Setter for <code>train.t_class_info.f_resource_status</code>. 预定资源状态
     */
    public void setResourceStatus(Integer value);

    /**
     * Getter for <code>train.t_class_info.f_resource_status</code>. 预定资源状态
     */
    public Integer getResourceStatus();

    /**
     * Setter for <code>train.t_class_info.f_totality_satisfied</code>. 总体满意度
     */
    public void setTotalitySatisfied(Double value);

    /**
     * Getter for <code>train.t_class_info.f_totality_satisfied</code>. 总体满意度
     */
    public Double getTotalitySatisfied();

    /**
     * Setter for <code>train.t_class_info.f_course_satisfied</code>. 课程满意度
     */
    public void setCourseSatisfied(Double value);

    /**
     * Getter for <code>train.t_class_info.f_course_satisfied</code>. 课程满意度
     */
    public Double getCourseSatisfied();

    /**
     * Setter for <code>train.t_class_info.f_organization_id</code>. 需求单位
     */
    public void setOrganizationId(String value);

    /**
     * Getter for <code>train.t_class_info.f_organization_id</code>. 需求单位
     */
    public String getOrganizationId();

    /**
     * Setter for <code>train.t_class_info.f_project_source</code>. 培训来源 1：集团级 2：省级
     */
    public void setProjectSource(Integer value);

    /**
     * Getter for <code>train.t_class_info.f_project_source</code>. 培训来源 1：集团级 2：省级
     */
    public Integer getProjectSource();

    /**
     * Setter for <code>train.t_class_info.f_sort</code>. 排序
     */
    public void setSort(Integer value);

    /**
     * Getter for <code>train.t_class_info.f_sort</code>. 排序
     */
    public Integer getSort();

    /**
     * Setter for <code>train.t_class_info.f_is_overproof</code>. 课酬是否超标：0,全部超标；1，单门课酬超标；2，课酬总额超标
     */
    public void setIsOverproof(Integer value);

    /**
     * Getter for <code>train.t_class_info.f_is_overproof</code>. 课酬是否超标：0,全部超标；1，单门课酬超标；2，课酬总额超标
     */
    public Integer getIsOverproof();

    /**
     * Setter for <code>train.t_class_info.f_four_degrees_submit_num</code>. 四度问卷提交数
     */
    public void setFourDegreesSubmitNum(Integer value);

    /**
     * Getter for <code>train.t_class_info.f_four_degrees_submit_num</code>. 四度问卷提交数
     */
    public Integer getFourDegreesSubmitNum();

    /**
     * Setter for <code>train.t_class_info.f_ability_submit_num</code>. 能力习得问卷提交数
     */
    public void setAbilitySubmitNum(Integer value);

    /**
     * Getter for <code>train.t_class_info.f_ability_submit_num</code>. 能力习得问卷提交数
     */
    public Integer getAbilitySubmitNum();

    /**
     * Setter for <code>train.t_class_info.f_superior_leadership_submit_num</code>. 上级领导提交数
     */
    public void setSuperiorLeadershipSubmitNum(Integer value);

    /**
     * Getter for <code>train.t_class_info.f_superior_leadership_submit_num</code>. 上级领导提交数
     */
    public Integer getSuperiorLeadershipSubmitNum();

    /**
     * Setter for <code>train.t_class_info.f_questionnaire_status</code>. 问卷状态
     */
    public void setQuestionnaireStatus(Integer value);

    /**
     * Getter for <code>train.t_class_info.f_questionnaire_status</code>. 问卷状态
     */
    public Integer getQuestionnaireStatus();

    /**
     * Setter for <code>train.t_class_info.f_course_salary</code>. 是否确认课酬
     */
    public void setCourseSalary(Integer value);

    /**
     * Getter for <code>train.t_class_info.f_course_salary</code>. 是否确认课酬
     */
    public Integer getCourseSalary();

    /**
     * Setter for <code>train.t_class_info.f_special_class</code>. 是否为特殊班级(0为正常，1为特殊)
     */
    public void setSpecialClass(Integer value);

    /**
     * Getter for <code>train.t_class_info.f_special_class</code>. 是否为特殊班级(0为正常，1为特殊)
     */
    public Integer getSpecialClass();

    /**
     * Setter for <code>train.t_class_info.f_class_level</code>. 培训班级别【null,0：普通班级；1：高管班】
     */
    public void setClassLevel(Integer value);

    /**
     * Getter for <code>train.t_class_info.f_class_level</code>. 培训班级别【null,0：普通班级；1：高管班】
     */
    public Integer getClassLevel();

    /**
     * Setter for <code>train.t_class_info.f_view</code>. 是否展示师资评价 0不展示 1展示
     */
    public void setView(Integer value);

    /**
     * Getter for <code>train.t_class_info.f_view</code>. 是否展示师资评价 0不展示 1展示
     */
    public Integer getView();

    /**
     * Setter for <code>train.t_class_info.f_modify_date</code>. 修改时间
     */
    public void setModifyDate(Timestamp value);

    /**
     * Getter for <code>train.t_class_info.f_modify_date</code>. 修改时间
     */
    public Timestamp getModifyDate();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface IClassInfo
     */
    public void from(IClassInfo from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface IClassInfo
     */
    public <E extends IClassInfo> E into(E into);
}
