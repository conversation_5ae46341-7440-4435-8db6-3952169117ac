package com.zxy.product.train.api;

import java.util.List;

import com.zxy.product.train.entity.Trainee;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.zxy.common.base.annotation.RemoteService;
import com.zxy.product.train.entity.TraineeGroup;

/**
 * Created by 田聪 on 2017/3/6.
 */
@RemoteService
public interface TraineeGroupService {


	/**
	 * 查找班级内所有分组
	 * @param classId
	 * @return
	 */
    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
	List<TraineeGroup> find(String classId);

	/**
	 * 获取组名
	 * @param classId
	 * @param ids
	 * @return
	 */
	@Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    List<TraineeGroup> findByIds(String classId, List<String> ids);

    /**
	 * 保存
	 * @param classId
	 * @param newGroups
	 * @param delGroups
	 * @param userId
	 * @return
	 */
	int save(String classId,List<TraineeGroup> newGroups,List<TraineeGroup> delGroups,String userId);


	/**
	 * 发短信的分组及学员
	 * @param classId
	 * @return
	 */
	@Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
	List<TraineeGroup> traineesForMessage(String classId);

	@Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
	List<Trainee> getInformalTraineesForMessage(String classId);
}
