package com.zxy.product.train.api;

import com.zxy.common.base.annotation.RemoteService;
import com.zxy.common.base.helper.PagedResult;
import com.zxy.product.train.entity.ClassInfo;
import com.zxy.product.train.entity.ResearchQuestionary;
import com.zxy.product.train.entity.Settlement;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * Created by chun on 2017/2/8.
 */
@RemoteService
public interface ClassInfoService {
    String URI = "train/class-info";// 班级管理 - URL

    /**
     * 按条件分页查询班级信息
     *
     * @param page
     * @param pageSize
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    PagedResult<ClassInfo> find(int page, int pageSize, Optional<String> name, Optional<String> MIScode,
                                Optional<String> orgName, Optional<Integer> status, Optional<Integer> isOutside,Optional<Long> reportBegin,
                                Optional<Long> reportEnd, Optional<Long> returnBegin, Optional<Long> returnEnd, Optional<Integer> implementation_year,
                                Optional<Integer> implementation_month, Optional<Integer> flag, List<String> organizationIds);

    /**
     * 培训评估
     *
     * @param page
     * @param pageSize
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    PagedResult<ClassInfo> findQ(int page, int pageSize, Optional<String> name, Optional<String> MIScode,
                                Optional<String> orgName, Optional<Integer> status, Optional<Long> reportBegin,
                                Optional<Long> reportEnd, Optional<Long> returnBegin, Optional<Long> returnEnd, Optional<Integer> implementation_year,
                                Optional<Integer> implementation_month, Optional<Integer> flag, List<String> organizationIds);

    /**
     * 为班级选择器提供
     *
     * @param page
     * @param pageSize
     * @param name
     * @param organization
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    PagedResult<ClassInfo> findClass(int page, int pageSize, int config, Optional<String> name,Optional<String> classType,
                                     Optional<Integer> month,Optional<Integer> course,Optional<Integer> year,Optional<Long> start,Optional<Long> end,
                                     Optional<String> organization,List<String> organizationIds);

    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    List<ClassInfo> findClassNameAndOrganizationByClassId(String ids);

    /**
     * 前端查询班级信息分页列表
     *
     * @param page
     * @param pageSize
     * @param memberId
     * @param MIScode
     * @param className
     * @param reachYear
     * @param reachMonth
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    PagedResult<ClassInfo> frontFind(int page, int pageSize, Optional<String> memberId, Optional<String> MIScode,
                                     Optional<String> className, Optional<Integer> reachYear, Optional<Integer> reachMonth,
                                     Optional<Integer> classStatus, Optional<String> organization,Integer flag);
    
    /**
     * 响应中心
     *
     * @param page
     * @param pageSize
     * @param MIScode
     * @param className
     * @param classStatus
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    PagedResult<ClassInfo> frontResponse(int page, int pageSize, Optional<String> MIScode,Optional<String> className,Optional<Integer> classStatus);

    /**
     * 通过id进行查询班级信息
     *
     * @param id
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    ClassInfo get(String id);



    /**
     * 通过id进行修改班级信息
     *
     * @param id
     * @param MemberId
     * @param romm
     * @param classRoom
     * @param diningRoom
     * @return
     */
    @Transactional
    ClassInfo update(String id, Optional<String> classTeacher, Optional<String> MemberId, Optional<String> romm, Optional<String> classRoom,
                     Optional<String> diningRoom, Optional<String> coverId, Optional<Integer> haveProvinceLeader,
                     Optional<Integer> haveMinister, Optional<Integer> needGroupPhoto, Optional<Long> photoTime,
                     Optional<Integer> needVideo, Optional<String> videoRequirement, Optional<Integer> needMakeCourse,
                     Optional<String> courseVideoRequirement, Optional<Integer> needNet, Optional<String> tableType,
                     Optional<String> otherRequirement, Optional<String> bannerId, Optional<Integer> confirm, Optional<String> teacherPhone, Optional<String> classInfoType, Optional<String> path,Optional<String> restaurantsIds,
         			Optional<String> guestroomsIds);

    /**
     * 通过id进行修改班级信息
     *
     * @param id
     * @param MemberId
     * @param romm
     * @param classRoom
     * @param diningRoom
     * @param coverPath
     * @return
     */
    @Transactional
    ClassInfo updateManage(String id, Optional<String> classTeacher, Optional<String> MemberId, Optional<String> romm, Optional<String> classRoom,
                     Optional<String> diningRoom, Optional<String> coverId, Optional<Integer> haveProvinceLeader,
                     Optional<Integer> haveMinister, Optional<Integer> needGroupPhoto, Optional<Long> photoTime,
                     Optional<Integer> needVideo, Optional<String> videoRequirement, Optional<Integer> needMakeCourse,
                     Optional<String> courseVideoRequirement, Optional<Integer> needNet, Optional<String> tableType,
                     Optional<String> otherRequirement, Optional<String> bannerId, Optional<Integer> confirm,
                     Optional<String> teacherPhone, Optional<String> classInfoType, Optional<String> path,
                     Optional<String> restaurantsIds, Optional<String> guestroomsIds,Optional<Integer> role, Optional<String> coverPath,Optional<Integer> falg);

    /**
     * 根据计划ID查询班级基本信息
     *
     * @param projectId 计划ID
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    ClassInfo findByProjectId(String projectId);

    /**
     * 通过id进行删除班级
     *
     * @param id
     * @return
     */
    @Transactional
    int del(String id);

    @Transactional
    ClassInfo insert(String projectId, Optional<String> classTeacherPhone, Optional<String> classTeacher,
                     Optional<Long> arriveDate, Optional<Long> returnDate, Optional<Integer> isOutSide,
                     Optional<String> surveyType, Optional<String> target, Optional<String> level, Optional<String> studentType,
                     Optional<String> simpleType, Optional<Integer> isPlan, Optional<Integer> implementationYear, Optional<Integer> implementationMonth, Optional<Integer> status,
                     Optional<String> createMemberId);

    @Transactional
    ClassInfo insert(String projectId, Optional<String> classTeacherPhone, Optional<String> classTeacher,
                     Optional<Long> arriveDate, Optional<Long> returnDate, Optional<Integer> isOutSide,
                     Optional<String> surveyType, Optional<String> target, Optional<String> level, Optional<String> studentType,
                     Optional<String> simpleType, Optional<Integer> isPlan, Optional<Integer> implementationYear,Optional<Integer> implementationMonth,Optional<Integer> status,
                     Optional<String> createMemberId, Optional<Integer> resourceStatus,Double totalitySatisfied,Double courseSatisfied,Optional<Integer> projectSource,String organizationId,Integer sort,Integer qstatus);

    /**
     * 根据计划ID查询班级信息（只查单表）
     *
     * @param projectId
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    ClassInfo findSingleByProjectId(String projectId);

    /**
     * 根据班级ID查询班级与计划的信息
     *
     * @param classId 班级ID
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    ClassInfo findClassAndProjectByClassId(String classId);

    /**
     * 导出月结算报表
     *
     * @param date
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    List<Settlement> findMonth(String date, List<String> organizationIds);


    /**
     * 导出月结算成员名单
     *
     * @param date
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    List<Settlement> findMonthMember(String date,List<String> organizationIds ,Integer flag);
    /**
     * 通过组织进行查班级列表
     *
     * @param page
     * @param pageSize
     * @param orgaizationId
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    PagedResult<ClassInfo> findOrganization(int page, int pageSize, String orgaizationId);

    /**
     * 通过当前登录人进行查班级列表
     *
     * @param page
     * @param pageSize
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    PagedResult<ClassInfo> findMember(int page, int pageSize, String orgaizationId);

    /**
     * 当前用户是否有未开始（报名成功）、实施中、已结束但未超过返程日次日24时的培训班,只展示一条
     * @param memberId
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    Optional<ClassInfo> findClassByMemberId(String memberId);

    /**
     * @param memberId
     * @param status
     * @param className
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    List<ClassInfo> findMyTaskClass(String memberId, Optional<Integer> status, Optional<String> className);

    /**
     * 活动首页的班级查询
     *
     * @param page
     * @param pageSize
     * @param name
     * @param status
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    PagedResult<ClassInfo> findActivityClassInfo(Integer page, Integer pageSize, Optional<String> name, Optional<Integer> status);

    /**
     * 个人中心的我的班级
     *
     * @param memberId
     * @param status
     * @param name
     * @param arriveDateOrderBy
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    PagedResult<ClassInfo> findMyClass(Integer page, Integer pageSize,List<String> memberIds, Optional<Integer> status, Optional<String> name, Optional<Integer> arriveDateOrderBy);

    @Transactional
    ClassInfo updateTraineeNum(String id, Integer trainee_num,boolean flag,boolean flag1,boolean flag2);

    @Transactional
    ClassInfo updateSubmitNum(String id, Integer submit_num);

    /**
     * 根据考试Id查询班级信息，用于发送考试评卷通知
     *
     * @param examId
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    Optional<ClassInfo> getClassInfoByExamId(String examId);

    /**
     * 根据班级IDS查询指定班级基本信息，用于活动中查询数据
     *
     * @param ids
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    List<ClassInfo> findBasicClassInfoByIds(List<String> ids);

    /**
     * 根据班级IDS查询指定班级（含需求方信息），用于调研/评估中查询数据
     *
     * @param ids
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    List<ClassInfo> findClassInfoByIds(List<String> ids);

    /**
     * 根据班级IDS以及日期查询班级数据
     *
     * @param ids
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    List<ClassInfo> findClassInfoByDate(List<String> ids,Optional<Long> arriveDateBegin,Optional<Long> arriveDateEnd);

    /**
     * 根据id查询指定班级，用于活动同步数据
     *
     * @param id
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    Optional<ClassInfo> findClassInfoOptionalById(String id);


    /**
     * 班级问卷
     *
     * @param page
     * @param pageSize
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    PagedResult<ClassInfo> findQuestionare(int page, int pageSize, Optional<String> name, Optional<String> MIScode,
                                Optional<String> orgName, Optional<Integer> status, Optional<Long> reportBegin,
                                Optional<Long> reportEnd, Optional<Long> returnBegin, Optional<Long> returnEnd, Optional<Integer> implementation_year,
                                Optional<Integer> implementation_month, List<String> organizationIds);

    /**
     * 更新班级的组织id
     * @param projectId			计划id
     * @param organizationId	组织id
     */
    @Transactional
    void updateOrganizationIdByProjectId(String projectId,String organizationId,Integer findSource);

    /**
     * 为班级分组提供的列表查询
     * @param page
     * @param pageSize
     * @param name
     * @param orgName
     * @param organizationIds
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    PagedResult<ClassInfo> findByGroup (int page, int pageSize, Optional<String> name, Optional<String> orgName, List<String> organizationIds);

    /**
    * 根据计划id查询班级信息
     * @param projectId
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    ClassInfo findClassIdByProjectId(String projectId);

    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    List<ClassInfo> findByUserIds(List<String> ids,String memberId);

    /**
     * 根据班级id查询班级名称
     * @param id
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    ClassInfo findClassIdByProjectName(String id);


    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    ClassInfo findByProjectContacts(String id);

    /**
     * 通过班级id查询报道日和返程日
     * @param id
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    ClassInfo findByTime(String id);

    /**
     * 课酬管理确认课酬时，修改该班级课酬是否超标
     * @param classId
     * @param isOverproof
     */
    @Transactional
    void updateIsOverproof(String classId, Integer isOverproof);

    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    boolean getContactMemberId(String classId, String memberId);

    /**
     * MIS培训班接口
     * @param startTime
     * @param endTime
     * @return
     */
    @Transactional(readOnly = true)
    List<ClassInfo> findInfoToMis(Long startTime, Long endTime);


    void updateView(String id, Integer view);

    /**
	 * 通过班级idlist查询发布通知
	 * @param asList
	 * @param currentUserId
	 * @return
	 */
	@Transactional(readOnly = true,propagation = Propagation.SUPPORTS)
	List<ClassInfo> filterByIds(String[] ids, String currentUserId);

    @Transactional(propagation = Propagation.SUPPORTS)
    ClassInfo updateCourseSalary(String id);

    @Transactional(readOnly = true,propagation = Propagation.SUPPORTS)
    Map<String,String> findByClassId(String id);

    /**
     * 通过计划id返回已经审核通过的班级id
     * @param ids
     * @return
     */
    @Transactional(readOnly = true,propagation = Propagation.SUPPORTS)
    Map<String,String> findByProjectIds(List<String> ids);

    /**
     * 人工智能---培训班初始化数据
     * @param start
     * @param limit
     * @return
     */
    @Transactional(readOnly = true,propagation = Propagation.SUPPORTS)
    List<String> getAllClassIds(int start,int limit);

    /**
     * 获取班级详情
     * @param classId
     * @return
     */
    @Transactional(readOnly = true)
    Optional<ClassInfo> getDetails(String classId);

    /**
     * 查询指定人员培训经历
     */
    @Transactional(readOnly = true)
    List<ClassInfo> classExperience(String memberId);

    /**
     * 获取指定人员,指定班级,问卷的分数
     */
    @Transactional(readOnly = true)
    ResearchQuestionary getQuestionnaireScores(String memberId, String classId);

    /**
     * 获取指定人员,指定班级的详细信息
     */
    @Transactional(readOnly = true)
    ClassInfo getClassAndMemberDetail(String memberId, String classId);

    /**
     * 查询需要处理的用户与培训班信息
     * @param startTime
     * @param endTime
     * @return
     */
    @Transactional
    Boolean dealClassInfo(Long startTime, Long endTime);
}

