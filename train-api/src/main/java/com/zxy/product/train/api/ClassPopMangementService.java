package com.zxy.product.train.api;

import com.zxy.common.base.annotation.RemoteService;
import com.zxy.product.train.entity.ClassPopMangement;

import java.util.Optional;

/**
 * @Auther: xxh
 * @Date: 2025/8/28 - 08 - 28 - 18:17
 * @Description: com.zxy.product.train.api
 * @version: 1.0
 */
@RemoteService
public interface ClassPopMangementService {
    ClassPopMangement add(ClassPopMangement mangement);

    Optional<ClassPopMangement> getFlag(String classId, String memberId);
}
