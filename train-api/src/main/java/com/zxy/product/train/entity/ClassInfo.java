package com.zxy.product.train.entity;

import com.zxy.product.train.jooq.tables.pojos.ClassInfoEntity;
import java.util.List;

public class ClassInfo extends ClassInfoEntity {

	private static final long serialVersionUID = 8164026462422600460L;

	public static final int IS_OUTSIDE_FALSE = 0; // 培训方式：0院外培训
	public static final int IS_OUTSIDE_TRUE = 1; // 培训方式：1院外培训
	public static final int IS_OUTSIDE_ONLINE = 2; // 培训方式：2在线学习
	public static final int STATUS_FALSE = 1; // 实施状态： 未实施
	public static final int STATUS_ING = 2; // 实施状态： 实施中
	public static final int STATUS_TRUE = 3; // 实施状态： 已实施
	public static final int DELETE_FLASE=0;	//删除状态：未删除
	public static final int DELETE_TRUE=1;	//删除状态，已删除
	public static final int CLASS_ROOM = 6; //配置管理中的班级教室
	public static final String SHORT_NAME = "短名称"; //分组初始化话短名称
	public static final String GROUP_ID = "100200312"; //分组管理默认给的分组ID
	public static final int STATUS_NOT_FINISH = 1;//未完成
	public static final int STATUS_NOT_START = 2;//未开始
	public static final int EXAM_TYPE = 1;//考试
	public static final int STUDENT_SATISFACTION_TYPE = 4;//学员满意度
	public static final int CLASS_IS_NOTICE = 1;//已发布通知
	public static final int STATUS_WAIT_AUDIT = 4;//学员在该班级的状态是：待审核
	public static final Integer BUSSINESS_TYPE = 5; //业务类型，用于个人中心我的任务列表
	public static final String CLASS_DETAIL_PAGE_INFO_KEY = "class-detail-page-info-key";
	public static final int SORT_FALSE = 1;//排序的字段
	public static final int SORT_ING = 2;//排序的字段
	public static final int SORT_TRUE = 3;//排序的字段
	public static final int COURSE_SALARY_ONE = 1; //是否提交课酬

	/**
	 * 展示评价
	 */
	public static final int VIEW_YES = 1;

	/**
	 * 2019年5月12日后启用新版满意度问卷
	 */
	public static final long SATISFACTION_TIME = 1557590400000L;

	private String name;
	private String className; //计划中班级名称
	private String code; // MIS编码
	private String organization; //需求方单位
	private String contactPhone; //需求电话
	private String teacherPhone; //班主任电话
	private String teacherEmail; //班主任邮箱
	private String contachEmail; //需求方邮箱
	private String teacher; // 班主任
	private String contactPeople; //需求方联系人
	private String contactName; //需求方联系人编号
	private String trainObject; //培训对象
	private String restRoom; //客房
	private String classRoom;//教室ID
	private String classRoomName;//教室名称
	private String diningRoom;//餐厅
	private ClassDetail classDetail; // 为首页可配置存入班级详情的对象
	private String address;//培训地点
	private Integer registNumber;//已报名人数
	private Integer amount; //计划人数
	private Integer days; //培训天数
	private String yearMonth; //年月
	private Double feedbackRate; //问卷回答率
	private String bannerId; // 封面id
	private String path;
	private List<ConfigurationValue> configurationValue;
	private List<ClassroomConfiguration> classroomConfig;
	private Member member;
	private List<Bus> busList;
	private List<ClassEvaluate> classEvaluate;
	private Integer traineeType;//学员类型
	private Project project;//计划
	private String memberId;
	private List<ConfigurationValue> restaurantsConfig;//餐厅
	private List<ConfigurationValue> guestroomsConfig;//客房
	private String cost;//培训级别
	private String type;//培训类别
	private String questionaryId;//满意问卷ID
	private Integer questionaryType;//问卷类型
	private String coverPath;//推荐活动的班级封面
	private Integer qStatus;//能力习得问卷发布状态
	private Integer fStatus;//四度问卷提交状态（0代表没人提交1代表有人提交）
	private Integer fQuestionnaire;//是否有四度评估问卷（0代表没有1代表有）
	private double duration;
	private List<Trainee> traineeList;
	private Integer isOpen;
	private String projectName;//培训名称
	private String trainingTypeName;//培训地点


	private long courseDate;//课程上课时间

	private Integer isPartyCadre;//是否是党干部培训班  0：否 1：是

	private Integer isManualFinish;//是否手动结束：0-否，1-是

	private long courseDateMax;

	private Boolean chatGroup;

	private String chatGroupId;

	private String conversionId;

	private String oldPositionName;
	private String newPositionName;
	private Object extendData;
	private Integer projectStatus;//项目状态

	public String getOldPositionName() {
		return oldPositionName;
	}

	public void setOldPositionName(String oldPositionName) {
		this.oldPositionName = oldPositionName;
	}

	public String getNewPositionName() {
		return newPositionName;
	}

	public void setNewPositionName(String newPositionName) {
		this.newPositionName = newPositionName;
	}


	public Integer getIsOpen() {
		return isOpen;
	}

	public void setIsOpen(Integer isOpen) {
		this.isOpen = isOpen;
	}

	public Integer getfQuestionnaire() {
		return fQuestionnaire;
	}

	public void setfQuestionnaire(Integer fQuestionnaire) {
		this.fQuestionnaire = fQuestionnaire;
	}

	public Integer getfStatus() {
		return fStatus;
	}

	public void setfStatus(Integer fStatus) {
		this.fStatus = fStatus;
	}

	public Integer getqStatus() {
		return qStatus;
	}

	public void setqStatus(Integer qStatus) {
		this.qStatus = qStatus;
	}

	public String getQuestionaryId() {
		return questionaryId;
	}

	public void setQuestionaryId(String questionaryId) {
		this.questionaryId = questionaryId;
	}

	public String getCost() {
		return cost;
	}

	public void setCost(String cost) {
		this.cost = cost;
	}

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	public List<ConfigurationValue> getRestaurantsConfig() {
		return restaurantsConfig;
	}

	public void setRestaurantsConfig(List<ConfigurationValue> restaurantsConfig) {
		this.restaurantsConfig = restaurantsConfig;
	}

	public List<ConfigurationValue> getGuestroomsConfig() {
		return guestroomsConfig;
	}

	public void setGuestroomsConfig(List<ConfigurationValue> guestroomsConfig) {
		this.guestroomsConfig = guestroomsConfig;
	}

	public List<ClassroomConfiguration> getClassroomConfig() {
		return classroomConfig;
	}

	public void setClassroomConfig(List<ClassroomConfiguration> classroomConfig) {
		this.classroomConfig = classroomConfig;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getPath() {
		return path;
	}

	public void setPath(String path) {
		this.path = path;
	}

	public String getMemberId() {
		return memberId;
	}

	public void setMemberId(String memberId) {
		this.memberId = memberId;
	}

	public Project getProject() {
		return project;
	}

	public void setProject(Project project) {
		this.project = project;
	}

	public List<ClassEvaluate> getClassEvaluate() {
		return classEvaluate;
	}

	public void setClassEvaluate(List<ClassEvaluate> classEvaluate) {
		this.classEvaluate = classEvaluate;
	}

	public List<Bus> getBusList() {
		return busList;
	}


	public void setBusList(List<Bus> busList) {
		this.busList = busList;
	}

	public String getAddress() {
		return address;
	}

	public void setAddress(String address) {
		this.address = address;
	}

	public String getRestRoom() {
		return restRoom;
	}

	public void setRestRoom(String restRoom) {
		this.restRoom = restRoom;
	}

	public Member getMember() {
		return member;
	}

	public void setMember(Member member) {
		this.member = member;
	}

	public Integer getDays() {
		return days;
	}

	public void setDays(Integer days) {
		this.days = days;
	}

	public Integer getAmount() {
		return amount;
	}

	public void setAmount(Integer amount) {
		this.amount = amount;
	}

	public Integer getRegistNumber() {
		return registNumber;
	}

	public void setRegistNumber(Integer registNumber) {
		this.registNumber = registNumber;
	}

	public String getClassRoomName() {
		return classRoomName;
	}

	public void setClassRoomName(String classRoomName) {
		this.classRoomName = classRoomName;
	}

	public ClassDetail getClassDetail() {
		return classDetail;
	}

	public void setClassDetail(ClassDetail classDetail) {
		this.classDetail = classDetail;
	}

	public static long getSerialVersionUID() {
		return serialVersionUID;
	}

	public static int getIsOutsideFalse() {
		return IS_OUTSIDE_FALSE;
	}

	public static int getIsOutsideTrue() {
		return IS_OUTSIDE_TRUE;
	}

	public static int getStatusFalse() {
		return STATUS_FALSE;
	}

	public static int getStatusIng() {
		return STATUS_ING;
	}

	public static int getStatusTrue() {
		return STATUS_TRUE;
	}

	public static int getDeleteFlase() {
		return DELETE_FLASE;
	}

	public static int getDeleteTrue() {
		return DELETE_TRUE;
	}

	public String getClassName() {
		return className;
	}

	public void setClassName(String className) {
		this.className = className;
	}

	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public String getOrganization() {
		return organization;
	}

	public void setOrganization(String organization) {
		this.organization = organization;
	}

	public List<ConfigurationValue> getConfigurationValue() {
		return configurationValue;
	}

	public void setConfigurationValue(List<ConfigurationValue> configurationValue) {
		this.configurationValue = configurationValue;
	}

	public Double getFeedbackRate() {
		return feedbackRate;
	}

	public void setFeedbackRate(Double feedbackRate) {
		this.feedbackRate = feedbackRate;
	}

	public String getContactPhone() {
		return contactPhone;
	}

	public void setContactPhone(String contactPhone) {
		this.contactPhone = contactPhone;
	}

	public String getTeacherPhone() {
		return teacherPhone;
	}

	public void setTeacherPhone(String teacherPhone) {
		this.teacherPhone = teacherPhone;
	}

	public String getTeacherEmail() {
		return teacherEmail;
	}

	public String getYearMonth() {
		return yearMonth;
	}
	public void setYearMonth(String yearMonth) {
		this.yearMonth = yearMonth;
	}

	public void setTeacherEmail(String teacherEmail) {
		this.teacherEmail = teacherEmail;
	}

	public String getContachEmail() {
		return contachEmail;
	}

	public void setContachEmail(String contachEmail) {
		this.contachEmail = contachEmail;
	}

	public String getTeacher() {
		return teacher;
	}

	public void setTeacher(String teacher) {
		this.teacher = teacher;
	}

	public String getContactPeople() {
		return contactPeople;
	}

	public void setContactPeople(String contactPeople) {
		this.contactPeople = contactPeople;
	}

	public String getTrainObject() {
		return trainObject;
	}

	public void setTrainObject(String trainObject) {
		this.trainObject = trainObject;
	}

	public String getClassRoom() {
		return classRoom;
	}

	public void setClassRoom(String classRoom) {
		this.classRoom = classRoom;
	}

	public String getDiningRoom() {
		return diningRoom;
	}

	public void setDiningRoom(String diningRoom) {
		this.diningRoom = diningRoom;
	}

	public String getBannerId() {
		return bannerId;
	}

	public void setBannerId(String bannerId) {
		this.bannerId = bannerId;
	}

	public Integer getTraineeType() {
		return traineeType;
	}

	public void setTraineeType(Integer traineeType) {
		this.traineeType = traineeType;
	}

	public String getCoverPath() {
		return coverPath;
	}

	public void setCoverPath(String coverPath) {
		this.coverPath = coverPath;
	}

	public List<Trainee> getTraineeList() {
		return traineeList;
	}

	public void setTraineeList(List<Trainee> traineeList) {
		this.traineeList = traineeList;
	}

	public double getDuration() {
		return duration;
	}

	public void setDuration(double duration) {
		this.duration = duration;
	}


	public String getContactName() {
		return contactName;
	}

	public void setContactName(String contactName) {
		this.contactName = contactName;
	}

	public long getCourseDate() {
		return courseDate;
	}

	public void setCourseDate(long courseDate) {
		this.courseDate = courseDate;
	}

	public Integer getQuestionaryType() {
		return questionaryType;
	}

	public void setQuestionaryType(Integer questionaryType) {
		this.questionaryType = questionaryType;
	}

	public Integer getIsPartyCadre() {
		return isPartyCadre;
	}

	public void setIsPartyCadre(Integer isPartyCadre) {
		this.isPartyCadre = isPartyCadre;
	}

	public Integer getIsManualFinish() {
		return isManualFinish;
	}

	public void setIsManualFinish(Integer isManualFinish) {
		this.isManualFinish = isManualFinish;
	}

	public long getCourseDateMax() {
		return courseDateMax;
	}

	public void setCourseDateMax(long courseDateMax) {
		this.courseDateMax = courseDateMax;
	}

	public Boolean getChatGroup() {
		return chatGroup;
	}

	public void setChatGroup(Boolean chatGroup) {
		this.chatGroup = chatGroup;
	}

	public String getChatGroupId() {
		return chatGroupId;
	}

	public void setChatGroupId(String chatGroupId) {
		this.chatGroupId = chatGroupId;
	}

	public String getConversionId() {
		return conversionId;
	}

	public void setConversionId(String conversionId) {
		this.conversionId = conversionId;
	}
	public Object getExtendData() {
		return extendData;
	}

	public void setExtendData(Object extendData) {
		this.extendData = extendData;
	}

	public String getProjectName() {
		return projectName;
	}

	public void setProjectName(String projectName) {
		this.projectName = projectName;
	}

	public String getTrainingTypeName() {
		return trainingTypeName;
	}

	public void setTrainingTypeName(String trainingTypeName) {
		this.trainingTypeName = trainingTypeName;
	}

	public Integer getProjectStatus() {
		return projectStatus;
	}

	public void setProjectStatus(Integer projectStatus) {
		this.projectStatus = projectStatus;
	}
}
