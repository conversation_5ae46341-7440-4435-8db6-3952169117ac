/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq;


import com.zxy.product.train.jooq.tables.*;

import javax.annotation.Generated;


/**
 * Convenience access to all tables in train
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class Tables {

    /**
     * The table <code>train.t_album</code>.
     */
    public static final Album ALBUM = Album.ALBUM;

    /**
     * 受众项
     */
    public static final AudienceItem AUDIENCE_ITEM = AudienceItem.AUDIENCE_ITEM;

    /**
     * 受众人表
     */
    public static final AudienceMember AUDIENCE_MEMBER = AudienceMember.AUDIENCE_MEMBER;

    /**
     * 受众对象表
     */
    public static final AudienceObject AUDIENCE_OBJECT = AudienceObject.AUDIENCE_OBJECT;

    /**
     * 班车表
     */
    public static final Bus BUS = Bus.BUS;

    /**
     * 班车详情表
     */
    public static final BusDetail BUS_DETAIL = BusDetail.BUS_DETAIL;

    /**
     * 班车选项主题表
     */
    public static final BusOption BUS_OPTION = BusOption.BUS_OPTION;

    /**
     * The table <code>train.t_center_configuration</code>.
     */
    public static final CenterConfiguration CENTER_CONFIGURATION = CenterConfiguration.CENTER_CONFIGURATION;

    /**
     * The table <code>train.t_classroom_configuration</code>.
     */
    public static final ClassroomConfiguration CLASSROOM_CONFIGURATION = ClassroomConfiguration.CLASSROOM_CONFIGURATION;

    /**
     * 培训班级和班务人员关联表
     */
    public static final ClassstaffClass CLASSSTAFF_CLASS = ClassstaffClass.CLASSSTAFF_CLASS;

    /**
     * The table <code>train.t_classstaff_configuration</code>.
     */
    public static final ClassstaffConfiguration CLASSSTAFF_CONFIGURATION = ClassstaffConfiguration.CLASSSTAFF_CONFIGURATION;

    /**
     * The table <code>train.t_class_business_progress</code>.
     */
    public static final ClassBusinessProgress CLASS_BUSINESS_PROGRESS = ClassBusinessProgress.CLASS_BUSINESS_PROGRESS;

    /**
     * 历史班级课程表
     */
    public static final ClassCourseHistory CLASS_COURSE_HISTORY = ClassCourseHistory.CLASS_COURSE_HISTORY;

    /**
     * The table <code>train.t_class_detail</code>.
     */
    public static final ClassDetail CLASS_DETAIL = ClassDetail.CLASS_DETAIL;

    /**
     * The table <code>train.t_class_evaluate</code>.
     */
    public static final ClassEvaluate CLASS_EVALUATE = ClassEvaluate.CLASS_EVALUATE;

    /**
     * The table <code>train.t_class_group</code>.
     */
    public static final ClassGroup CLASS_GROUP = ClassGroup.CLASS_GROUP;

    /**
     * 历史班级表
     */
    public static final ClassHistory CLASS_HISTORY = ClassHistory.CLASS_HISTORY;

    /**
     * The table <code>train.t_class_info</code>.
     */
    public static final ClassInfo CLASS_INFO = ClassInfo.CLASS_INFO;

    /**
     * The table <code>train.t_class_offline_course</code>.
     */
    public static final ClassOfflineCourse CLASS_OFFLINE_COURSE = ClassOfflineCourse.CLASS_OFFLINE_COURSE;

    /**
     * The table <code>train.t_class_online_course</code>.
     */
    public static final ClassOnlineCourse CLASS_ONLINE_COURSE = ClassOnlineCourse.CLASS_ONLINE_COURSE;

    /**
     * 新版满意度问卷数据表
     */
    public static final ClassQuestionnaireTotal CLASS_QUESTIONNAIRE_TOTAL = ClassQuestionnaireTotal.CLASS_QUESTIONNAIRE_TOTAL;

    /**
     * The table <code>train.t_class_quota</code>.
     */
    public static final ClassQuota CLASS_QUOTA = ClassQuota.CLASS_QUOTA;

    /**
     * The table <code>train.t_class_quota_detail</code>.
     */
    public static final ClassQuotaDetail CLASS_QUOTA_DETAIL = ClassQuotaDetail.CLASS_QUOTA_DETAIL;

    /**
     * The table <code>train.t_class_required</code>.
     */
    public static final ClassRequired CLASS_REQUIRED = ClassRequired.CLASS_REQUIRED;

    /**
     * The table <code>train.t_class_required_theme</code>.
     */
    public static final ClassRequiredTheme CLASS_REQUIRED_THEME = ClassRequiredTheme.CLASS_REQUIRED_THEME;

    /**
     * The table <code>train.t_class_resource</code>.
     */
    public static final ClassResource CLASS_RESOURCE = ClassResource.CLASS_RESOURCE;

    /**
     * The table <code>train.t_class_signup_info</code>.
     */
    public static final ClassSignupInfo CLASS_SIGNUP_INFO = ClassSignupInfo.CLASS_SIGNUP_INFO;

    /**
     * The table <code>train.t_class_statistics</code>.
     */
    public static final ClassStatistics CLASS_STATISTICS = ClassStatistics.CLASS_STATISTICS;

    /**
     * The table <code>train.t_class_theme</code>.
     */
    public static final ClassTheme CLASS_THEME = ClassTheme.CLASS_THEME;

    /**
     * The table <code>train.t_class_two_brings</code>.
     */
    public static final ClassTwoBrings CLASS_TWO_BRINGS = ClassTwoBrings.CLASS_TWO_BRINGS;

    /**
     * The table <code>train.t_collecting_course</code>.
     */
    public static final CollectingCourse COLLECTING_COURSE = CollectingCourse.COLLECTING_COURSE;

    /**
     * 集采方案配置
     */
    public static final CollectionProgrammeConfig COLLECTION_PROGRAMME_CONFIG = CollectionProgrammeConfig.COLLECTION_PROGRAMME_CONFIG;

    /**
     * 集采方案详解关联课程表
     */
    public static final CollectionProgrammeCourse COLLECTION_PROGRAMME_COURSE = CollectionProgrammeCourse.COLLECTION_PROGRAMME_COURSE;

    /**
     * 学院授课记录表
     */
    public static final CollegeTeaching COLLEGE_TEACHING = CollegeTeaching.COLLEGE_TEACHING;

    /**
     * 历史配置表
     */
    public static final ConfigruationHistory CONFIGRUATION_HISTORY = ConfigruationHistory.CONFIGRUATION_HISTORY;

    /**
     * The table <code>train.t_configuration</code>.
     */
    public static final Configuration CONFIGURATION = Configuration.CONFIGURATION;

    /**
     * The table <code>train.t_configuration_value</code>.
     */
    public static final ConfigurationValue CONFIGURATION_VALUE = ConfigurationValue.CONFIGURATION_VALUE;

    /**
     * 公司段对应关系
     */
    public static final CorporateSegmentRelation CORPORATE_SEGMENT_RELATION = CorporateSegmentRelation.CORPORATE_SEGMENT_RELATION;

    /**
     * The table <code>train.t_course_attach</code>.
     */
    public static final CourseAttach COURSE_ATTACH = CourseAttach.COURSE_ATTACH;

    /**
     * 课程属性表
     */
    public static final CourseAttribute COURSE_ATTRIBUTE = CourseAttribute.COURSE_ATTRIBUTE;

    /**
     * 课程目录表
     */
    public static final CourseCategory COURSE_CATEGORY = CourseCategory.COURSE_CATEGORY;

    /**
     * 在线课程表
     */
    public static final CourseInfo COURSE_INFO = CourseInfo.COURSE_INFO;

    /**
     * The table <code>train.t_course_salary</code>.
     */
    public static final CourseSalary COURSE_SALARY = CourseSalary.COURSE_SALARY;

    /**
     * 教研教学活动配置
     */
    public static final CourseTeachingActivities COURSE_TEACHING_ACTIVITIES = CourseTeachingActivities.COURSE_TEACHING_ACTIVITIES;

    /**
     * 需求方枚举表
     */
    public static final DemandSideOrganization DEMAND_SIDE_ORGANIZATION = DemandSideOrganization.DEMAND_SIDE_ORGANIZATION;

    /**
     * The table <code>train.t_dimension</code>.
     */
    public static final Dimension DIMENSION = Dimension.DIMENSION;

    /**
     * The table <code>train.t_dimension_question</code>.
     */
    public static final DimensionQuestion DIMENSION_QUESTION = DimensionQuestion.DIMENSION_QUESTION;

    /**
     * The table <code>train.t_evaluate</code>.
     */
    public static final Evaluate EVALUATE = Evaluate.EVALUATE;

    /**
     * 面授课程库表
     */
    public static final F2fCourse F2F_COURSE = F2fCourse.F2F_COURSE;

    /**
     * 面授课程库表
     */
    public static final F2fCourseLibrary F2F_COURSE_LIBRARY = F2fCourseLibrary.F2F_COURSE_LIBRARY;

    /**
     * The table <code>train.t_grant_detail</code>.
     */
    public static final GrantDetail GRANT_DETAIL = GrantDetail.GRANT_DETAIL;

    /**
     * The table <code>train.t_group_configuration</code>.
     */
    public static final GroupConfiguration GROUP_CONFIGURATION = GroupConfiguration.GROUP_CONFIGURATION;

    /**
     * The table <code>train.t_group_configuration_value</code>.
     */
    public static final GroupConfigurationValue GROUP_CONFIGURATION_VALUE = GroupConfigurationValue.GROUP_CONFIGURATION_VALUE;

    /**
     * The table <code>train.t_home_lecturer</code>.
     */
    public static final HomeLecturer HOME_LECTURER = HomeLecturer.HOME_LECTURER;

    /**
     * The table <code>train.t_job</code>.
     */
    public static final Job JOB = Job.JOB;

    /**
     * The table <code>train.t_label</code>.
     */
    public static final Label LABEL = Label.LABEL;

    /**
     * The table <code>train.t_lecturer</code>.
     */
    public static final Lecturer LECTURER = Lecturer.LECTURER;

    /**
     * 讲师擅长课程列表
     */
    public static final LecturerAdeptCourse LECTURER_ADEPT_COURSE = LecturerAdeptCourse.LECTURER_ADEPT_COURSE;

    /**
     * 讲师属性配置表
     */
    public static final LecturerAttribute LECTURER_ATTRIBUTE = LecturerAttribute.LECTURER_ATTRIBUTE;

    /**
     * 讲师专业序列/讲师课程库课程分类配置
     */
    public static final LecturerCourseConfig LECTURER_COURSE_CONFIG = LecturerCourseConfig.LECTURER_COURSE_CONFIG;

    /**
     * The table <code>train.t_lecturer_label</code>.
     */
    public static final LecturerLabel LECTURER_LABEL = LecturerLabel.LECTURER_LABEL;

    /**
     * 讲师发布权限人员表
     */
    public static final LecturerReleaseAuthority LECTURER_RELEASE_AUTHORITY = LecturerReleaseAuthority.LECTURER_RELEASE_AUTHORITY;

    /**
     * 讲师点赞记录
     */
    public static final LecturerThumbsUp LECTURER_THUMBS_UP = LecturerThumbsUp.LECTURER_THUMBS_UP;

    /**
     * The table <code>train.t_level</code>.
     */
    public static final Level LEVEL = Level.LEVEL;

    /**
     * The table <code>train.t_limit_configuration</code>.
     */
    public static final LimitConfiguration LIMIT_CONFIGURATION = LimitConfiguration.LIMIT_CONFIGURATION;

    /**
     * The table <code>train.t_limit_default_configuration</code>.
     */
    public static final LimitDefaultConfiguration LIMIT_DEFAULT_CONFIGURATION = LimitDefaultConfiguration.LIMIT_DEFAULT_CONFIGURATION;

    /**
     * The table <code>train.t_member</code>.
     */
    public static final Member MEMBER = Member.MEMBER;

    /**
     * The table <code>train.t_member_config</code>.
     */
    public static final MemberConfig MEMBER_CONFIG = MemberConfig.MEMBER_CONFIG;

    /**
     * The table <code>train.t_message_detail</code>.
     */
    public static final MessageDetail MESSAGE_DETAIL = MessageDetail.MESSAGE_DETAIL;

    /**
     * The table <code>train.t_message_record</code>.
     */
    public static final MessageRecord MESSAGE_RECORD = MessageRecord.MESSAGE_RECORD;

    /**
     * The table <code>train.t_organization</code>.
     */
    public static final Organization ORGANIZATION = Organization.ORGANIZATION;

    /**
     * The table <code>train.t_organization_detail</code>.
     */
    public static final OrganizationDetail ORGANIZATION_DETAIL = OrganizationDetail.ORGANIZATION_DETAIL;

    /**
     * 各单位授课记录表
     */
    public static final OrganizationTeaching ORGANIZATION_TEACHING = OrganizationTeaching.ORGANIZATION_TEACHING;

    /**
     * 其他教学教研记录表
     */
    public static final OtherTeaching OTHER_TEACHING = OtherTeaching.OTHER_TEACHING;

    /**
     * PCCW HR接口调用结果
     */
    public static final PccwResult PCCW_RESULT = PccwResult.PCCW_RESULT;

    /**
     * The table <code>train.t_position</code>.
     */
    public static final Position POSITION = Position.POSITION;

    /**
     * The table <code>train.t_project</code>.
     */
    public static final Project PROJECT = Project.PROJECT;

    /**
     * The table <code>train.t_project_approval</code>.
     */
    public static final ProjectApproval PROJECT_APPROVAL = ProjectApproval.PROJECT_APPROVAL;

    /**
     * 历史培训计划表
     */
    public static final ProjectHistory PROJECT_HISTORY = ProjectHistory.PROJECT_HISTORY;

    /**
     * The table <code>train.t_project_occupy</code>.
     */
    public static final ProjectOccupy PROJECT_OCCUPY = ProjectOccupy.PROJECT_OCCUPY;

    /**
     * The table <code>train.t_question</code>.
     */
    public static final Question QUESTION = Question.QUESTION;

    /**
     * 培训班满意度问卷类型问题表
     */
    public static final QuestionnaireQuestionType QUESTIONNAIRE_QUESTION_TYPE = QuestionnaireQuestionType.QUESTIONNAIRE_QUESTION_TYPE;

    /**
     * The table <code>train.t_question_attr</code>.
     */
    public static final QuestionAttr QUESTION_ATTR = QuestionAttr.QUESTION_ATTR;

    /**
     * 红色展馆信息
     */
    public static final RedPavilion RED_PAVILION = RedPavilion.RED_PAVILION;

    /**
     * The table <code>train.t_research_answer_record</code>.
     */
    public static final ResearchAnswerRecord RESEARCH_ANSWER_RECORD = ResearchAnswerRecord.RESEARCH_ANSWER_RECORD;

    /**
     * The table <code>train.t_research_questionary</code>.
     */
    public static final ResearchQuestionary RESEARCH_QUESTIONARY = ResearchQuestionary.RESEARCH_QUESTIONARY;

    /**
     * The table <code>train.t_research_record</code>.
     */
    public static final ResearchRecord RESEARCH_RECORD = ResearchRecord.RESEARCH_RECORD;

    /**
     * 历史课酬表
     */
    public static final SalaryHistory SALARY_HISTORY = SalaryHistory.SALARY_HISTORY;

    /**
     * The table <code>train.t_settlement</code>.
     */
    public static final Settlement SETTLEMENT = Settlement.SETTLEMENT;

    /**
     * The table <code>train.t_settlement_configuration</code>.
     */
    public static final SettlementConfiguration SETTLEMENT_CONFIGURATION = SettlementConfiguration.SETTLEMENT_CONFIGURATION;

    /**
     * The table <code>train.t_settlement_configuration_value</code>.
     */
    public static final SettlementConfigurationValue SETTLEMENT_CONFIGURATION_VALUE = SettlementConfigurationValue.SETTLEMENT_CONFIGURATION_VALUE;

    /**
     * 签到表
     */
    public static final Sign SIGN = Sign.SIGN;

    /**
     * 签到详情表
     */
    public static final SignDetail SIGN_DETAIL = SignDetail.SIGN_DETAIL;

    /**
     * 请假表
     */
    public static final SignLeave SIGN_LEAVE = SignLeave.SIGN_LEAVE;

    /**
     * The table <code>train.t_student_detail</code>.
     */
    public static final StudentDetail STUDENT_DETAIL = StudentDetail.STUDENT_DETAIL;

    /**
     * The table <code>train.t_student_detail_total</code>.
     */
    public static final StudentDetailTotal STUDENT_DETAIL_TOTAL = StudentDetailTotal.STUDENT_DETAIL_TOTAL;

    /**
     * 学员明细规则排序表
     */
    public static final StudentDetailTotalSort STUDENT_DETAIL_TOTAL_SORT = StudentDetailTotalSort.STUDENT_DETAIL_TOTAL_SORT;

    /**
     * 历史学员表
     */
    public static final StudentHistory STUDENT_HISTORY = StudentHistory.STUDENT_HISTORY;

    /**
     * 作业表
     */
    public static final Task TASK = Task.TASK;

    /**
     * 作业审核表
     */
    public static final TaskApproval TASK_APPROVAL = TaskApproval.TASK_APPROVAL;

    /**
     * 作业附件关联表
     */
    public static final TaskAttach TASK_ATTACH = TaskAttach.TASK_ATTACH;

    /**
     * 用户提交作业详情表
     */
    public static final TaskMember TASK_MEMBER = TaskMember.TASK_MEMBER;

    /**
     * 作业审核人关联表
     */
    public static final TaskReviewer TASK_REVIEWER = TaskReviewer.TASK_REVIEWER;

    /**
     * 培训学员表
     */
    public static final Trainee TRAINEE = Trainee.TRAINEE;

    /**
     * 培训学员分组表
     */
    public static final TraineeGroup TRAINEE_GROUP = TraineeGroup.TRAINEE_GROUP;

    /**
     * 学习团队表
     */
    public static final StudyTeam STUDY_TEAM = com.zxy.product.train.jooq.tables.StudyTeam.STUDY_TEAM;

    /**
     * 学习活动表
     */
    public static final StudyTeamActivity STUDY_TEAM_ACTIVITY = com.zxy.product.train.jooq.tables.StudyTeamActivity.STUDY_TEAM_ACTIVITY;

    /**
     * 学习活动-任务关联表
     */
    public static final StudyTeamActivityTask STUDY_TEAM_ACTIVITY_TASK = com.zxy.product.train.jooq.tables.StudyTeamActivityTask.STUDY_TEAM_ACTIVITY_TASK;

    /**
     * 学习团队成员表
     */
    public static final StudyTeamMember STUDY_TEAM_MEMBER = com.zxy.product.train.jooq.tables.StudyTeamMember.STUDY_TEAM_MEMBER;

    /**
     * 学习团队成员签到表
     */
    public static final StudyTeamMemberSign STUDY_TEAM_MEMBER_SIGN = com.zxy.product.train.jooq.tables.StudyTeamMemberSign.STUDY_TEAM_MEMBER_SIGN;

    /**
     * 学习团队成员签到表流水表
     */
    public static final StudyTeamMemberSignLog STUDY_TEAM_MEMBER_SIGN_LOG = com.zxy.product.train.jooq.tables.StudyTeamMemberSignLog.STUDY_TEAM_MEMBER_SIGN_LOG;

    /**
     * 团队学习班-学习成果表
     */
    public static final StudyTeamAchievement STUDY_TEAM_ACHIEVEMENT = com.zxy.product.train.jooq.tables.StudyTeamAchievement.STUDY_TEAM_ACHIEVEMENT;

    /**
     * 团队学习班-点赞明细表
     */
    public static final StudyTeamAchievementPraise STUDY_TEAM_ACHIEVEMENT_PRAISE = com.zxy.product.train.jooq.tables.StudyTeamAchievementPraise.STUDY_TEAM_ACHIEVEMENT_PRAISE;

    /**
     * 团队学习班-学习成果回复表
     */
    public static final StudyTeamAchievementReply STUDY_TEAM_ACHIEVEMENT_REPLY = com.zxy.product.train.jooq.tables.StudyTeamAchievementReply.STUDY_TEAM_ACHIEVEMENT_REPLY;

    /**
     * 团队学习班-活动资料表
     */
    public static final StudyTeamActivityAttachment STUDY_TEAM_ACTIVITY_ATTACHMENT = com.zxy.product.train.jooq.tables.StudyTeamActivityAttachment.STUDY_TEAM_ACTIVITY_ATTACHMENT;

    /**
     * 团队学习班-活动相册资料表
     */
    public static final StudyTeamActivityPhotos STUDY_TEAM_ACTIVITY_PHOTOS = com.zxy.product.train.jooq.tables.StudyTeamActivityPhotos.STUDY_TEAM_ACTIVITY_PHOTOS;

    /**
     * 培训班结算人数配置表
     */
    public static final SettlementMemberQuantity SETTLEMENT_MEMBER_QUANTITY = com.zxy.product.train.jooq.tables.SettlementMemberQuantity.SETTLEMENT_MEMBER_QUANTITY;

    /**
     * 学习活动-课程领学人学习时间
     */
    public static final StudyTeamLeaderConfirmDetail STUDY_TEAM_LEADER_CONFIRM_DETAIL = com.zxy.product.train.jooq.tables.StudyTeamLeaderConfirmDetail.STUDY_TEAM_LEADER_CONFIRM_DETAIL;

    /**
     * 删除记录表
     */
    public static final DeleteDataTrain DELETE_DATA_TRAIN = com.zxy.product.train.jooq.tables.DeleteDataTrain.DELETE_DATA_TRAIN;
    /**
     * The table <code>train.t_class_grades_project</code>.
     */
    public static final ClassGradesProject CLASS_GRADES_PROJECT = com.zxy.product.train.jooq.tables.ClassGradesProject.CLASS_GRADES_PROJECT;

    /**
     * The table <code>train.t_class_grades_project_member</code>.
     */
    public static final ClassGradesProjectMember CLASS_GRADES_PROJECT_MEMBER = com.zxy.product.train.jooq.tables.ClassGradesProjectMember.CLASS_GRADES_PROJECT_MEMBER;

    /**
     * 培训类别表
     */
    public static final TrainingType TRAINING_TYPE = com.zxy.product.train.jooq.tables.TrainingType.TRAINING_TYPE;

    /**
     * 智慧教务-配置情况
     */
    public static final SolutionConfiguration SOLUTION_CONFIGURATION = com.zxy.product.train.jooq.tables.SolutionConfiguration.SOLUTION_CONFIGURATION;

    /**
     * 培训项目表
     */
    public static final TrainProject TRAIN_PROJECT = com.zxy.product.train.jooq.tables.TrainProject.TRAIN_PROJECT;

    /**
     * 策划实施表
     */
    public static final PlanningImplementation PLANNING_IMPLEMENTATION = com.zxy.product.train.jooq.tables.PlanningImplementation.PLANNING_IMPLEMENTATION;

    /**
     * 策划实施关联表
     */
    public static final PlanningImplementationRelated PLANNING_IMPLEMENTATION_RELATED = com.zxy.product.train.jooq.tables.PlanningImplementationRelated.PLANNING_IMPLEMENTATION_RELATED;

    /**
     * 培训班群聊成员表
     */
    public static final TrainChatGroup TRAIN_CHAT_GROUP = com.zxy.product.train.jooq.tables.TrainChatGroup.TRAIN_CHAT_GROUP;

    /**
     * 培训班群聊表
     */
    public static final TrainChatGroupInfo TRAIN_CHAT_GROUP_INFO = com.zxy.product.train.jooq.tables.TrainChatGroupInfo.TRAIN_CHAT_GROUP_INFO;

    /**
     * 培训班课程问卷满意度结果表
     */
    public static final ClassResearchSatisfaction CLASS_RESEARCH_SATISFACTION = com.zxy.product.train.jooq.tables.ClassResearchSatisfaction.CLASS_RESEARCH_SATISFACTION;
    public static final AcademicStatisticsInfo ACADEMIC_STATISTICS_INFO = com.zxy.product.train.jooq.tables.AcademicStatisticsInfo.ACADEMIC_STATISTICS_INFO;
    /**
     * 学习报告-培训班学习表(学情报告月度统计使用)
     */
    public static final StudyReportTrain_2025 STUDY_REPORT_TRAIN_2025 = com.zxy.product.train.jooq.tables.StudyReportTrain_2025.STUDY_REPORT_TRAIN_2025;

    /**
     * 学习报告-培训班学习表(学情报告月度统计使用)
     */
    public static final StudyReportTrain_2026 STUDY_REPORT_TRAIN_2026 = com.zxy.product.train.jooq.tables.StudyReportTrain_2026.STUDY_REPORT_TRAIN_2026;

    /**
     * 学习报告-培训班学习表(学情报告月度统计使用)
     */
    public static final StudyReportTrain_2027 STUDY_REPORT_TRAIN_2027 = com.zxy.product.train.jooq.tables.StudyReportTrain_2027.STUDY_REPORT_TRAIN_2027;

    /**
     * 学习报告-培训班学习表(学情报告月度统计使用)
     */
    public static final StudyReportTrain_2028 STUDY_REPORT_TRAIN_2028 = com.zxy.product.train.jooq.tables.StudyReportTrain_2028.STUDY_REPORT_TRAIN_2028;

    /**
     * 学习报告-培训班学习表(学情报告月度统计使用)
     */
    public static final StudyReportTrain_2029 STUDY_REPORT_TRAIN_2029 = com.zxy.product.train.jooq.tables.StudyReportTrain_2029.STUDY_REPORT_TRAIN_2029;
    /**
     * 学员管理弹窗管理
     */
    public static final ClassPopMangement CLASS_POP_MANGEMENT = com.zxy.product.train.jooq.tables.ClassPopMangement.CLASS_POP_MANGEMENT;
}
