ALTER TABLE `train`.`t_class_info`
    ADD COLUMN `f_view` TINYINT(4) NULL COMMENT '是否展示师资评价 0不展示 1展示' AFTER `f_class_level`;

CREATE TABLE `train`.`t_class_pop_mangement` (
                                         `f_id` VARCHAR(40) NOT NULL COMMENT '表id',
                                         `f_member_id` VARCHAR(40) DEFAULT NULL COMMENT '人员id',
                                         `f_type` TINYINT(4) DEFAULT 0 COMMENT '班务人员类型：0.管理员 1.班主任',
                                         `f_flag` TINYINT(4) DEFAULT 0 COMMENT '弹窗设置 0 弹出 1 关闭',
                                         `f_class_id` VARCHAR(40) DEFAULT NULL COMMENT '班级id',
                                         `f_create_time` BIGINT(20) DEFAULT NULL COMMENT '创建时间',
                                         PRIMARY KEY (`f_id`),
                                         KEY `classs_pop_member_id` (`f_member_id`)
) ENGINE=INNODB DEFAULT CHARSET=utf8 COMMENT='学员管理弹窗管理';


INSERT INTO system.t_MODULE_GROUP(f_id, f_organization_id,f_name,f_code,f_max_select,f_min_select)
VALUES ('28','1', '培训班', 'training-class', 1,0);

INSERT INTO system.t_HOME_MODULE(f_id, f_organization_id,f_module_code,f_name,f_client_type,f_module_group_id)
VALUES ('025112','1', 'training-class', '培训班', 2,'28');