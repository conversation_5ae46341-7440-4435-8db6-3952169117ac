package com.zxy.product.train.async.task;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.asiainfo.openplatform.AIESBClient;
import com.asiainfo.openplatform.utils.AIESBConstants;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.zxy.common.cache.Cache;
import com.zxy.common.cache.CacheService;
import com.zxy.common.dao.support.CommonDao;
import com.zxy.product.system.api.permission.OrganizationHrMappingService;
import com.zxy.product.system.entity.OrganizationHrMapping;
import com.zxy.product.train.async.util.DateUtil;
import com.zxy.product.train.dto.OfflineClassInfo;
import com.zxy.product.train.dto.OfflineClassInfoDTO;
import com.zxy.product.train.dto.Trainee;
import com.zxy.product.train.dto.TraineeDTO;
import com.zxy.product.train.entity.*;
import org.apache.commons.lang.StringUtils;
import org.jooq.Condition;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

import static com.zxy.product.train.jooq.Tables.*;

/**
 * 电讯盈科(PCCW)人力集中化系统(iHR)接口.
 *
 * <AUTHOR>
 */
@Component
public class PccwTask {

  private static final Logger log = LoggerFactory.getLogger(PccwTask.class);

  private static final String PUSH_KEY_OFFLINE_CLASS_DATA = "OfflineClassInfoData";//M_HR_CUST_140000002-导入员工面授课程信息
  private static final String PUSH_KEY_TRAINEE_INFO = "traineeInfoData";//M_HR_CUST_140000003-工参训信息


  @Autowired
  private CommonDao<Project> projectDao;

  @Autowired
  private CommonDao<PccwResult> pccwResultDao;

  @Autowired
  private CommonDao<Organization> organizationDao;

  @Autowired
  private OrganizationHrMappingService organizationHrMappingService;

  private Cache pccwCache;

  @Autowired
  public void setCacheService(CacheService cacheService) {
    this.pccwCache = cacheService.create("train-web-server","pccw-hand-push");
  }

  /**
   * M_HR_CUST_140000002-导入员工面授课程信息(增量). 凌晨四点执行.
   */
  @Scheduled(cron = "0 0 4 * * ? ")
  @Transactional(rollbackFor = Exception.class)
  public void offlineClassInfo() {
    String method = "M_HR_CUST_140000002";
    Map<String, String> sysParam = getSysParamMap(method);
    // 处理失败历史数据
    List<PccwResult> failResultList = pccwResultDao
            .fetch(PCCW_RESULT.STATUS.eq(0).and(PCCW_RESULT.METHOD.eq(method)));
    if (Objects.nonNull(failResultList) && failResultList.size() > 0) {
      failResultList.stream().forEach(fr -> {
        Long createTime = fr.getCreateTime();
        String linkId = fr.getId();
        // 返回前一天 00:00:00 - 23:59:59增量数据
        long start = DateUtil.getYesterdayBeginTime(createTime);
        long end = DateUtil.getYesterdayEndTime(createTime);
        boolean dealResult = dealOfflineClassInfo(sysParam, method, start, end, linkId, new ArrayList<>());
        log.error("offlineClassInfo. time = {}, result = {}, linkId={}",
                System.currentTimeMillis(), dealResult, linkId);
        //处理成功
        int state = 1;
        if (!dealResult) {
          //处理失败,下次继续处理
          state = 0;
        }
        //更新历史处理记录
        fr.setStatus(state);
        fr.setUpdateTime(System.currentTimeMillis());
        pccwResultDao.update(fr);
      });
    }
    //处理当天记录
    long start = DateUtil.getYesterdayBeginTime(System.currentTimeMillis());
    long end = DateUtil.getYesterdayEndTime(System.currentTimeMillis());
    log.error("dealOnlineStudyTotalTime. time = {}, start = {}", System.currentTimeMillis(), start);
    boolean deal = dealOfflineClassInfo(sysParam, method, start, end, null, new ArrayList<>());
    log.error("dealOnlineStudyTotalTime. time = {}, result = {}", System.currentTimeMillis(), deal);
  }

  /**
   * M_HR_CUST_140000003-导入员工参训信息(增量).
   * <p>
   * 凌晨四点执行.
   */
  @Scheduled(cron = "0 5 4 * * ? ")
  @Transactional(rollbackFor = Exception.class)
  public void traineeInfo() {
    String method = "M_HR_CUST_140000003";
    Map<String, String> sysParam = getSysParamMap(method);
    // 处理失败历史数据
    List<PccwResult> failResultList = pccwResultDao.fetch(PCCW_RESULT.STATUS.eq(0)
            .and(PCCW_RESULT.METHOD.eq(method)));
    if (Objects.nonNull(failResultList) && failResultList.size() > 0) {
      failResultList.stream().forEach(fr -> {
        Long createTime = fr.getCreateTime();
        String linkId = fr.getId();
        // 返回前一天 00:00:00 - 23:59:59增量数据
        long start = DateUtil.getYesterdayBeginTime(createTime);
        long end = DateUtil.getYesterdayEndTime(createTime);
        boolean dealResult = dealTraineeInfo(sysParam, method, start, end, linkId, new ArrayList<>());
        log.error("dealTraineeInfo. time = {}, result = {}, linkId={}", System.currentTimeMillis(),
                dealResult, linkId);
        //处理成功
        int state = 1;
        if (!dealResult) {
          //处理失败,下次继续处理
          state = 0;
        }
        //更新历史处理记录
        fr.setStatus(state);
        fr.setUpdateTime(System.currentTimeMillis());
        pccwResultDao.update(fr);
      });
    }
    //处理当天记录
    long start = DateUtil.getYesterdayBeginTime(System.currentTimeMillis());
    long end = DateUtil.getYesterdayEndTime(System.currentTimeMillis());
    log.error("dealTraineeInfo. time = {}, start = {}", System.currentTimeMillis(), start);
    boolean deal = dealTraineeInfo(sysParam, method, start, end, null, new ArrayList<>());
    log.error("dealTraineeInfo. time = {}, result = {}", System.currentTimeMillis(), deal);
  }

  /**
   * 公共参数Map.
   */
  private Map<String, String> getSysParamMap(String method) {
    Map<String, String> sysParam = Maps.newHashMap();
    sysParam.put("appId", "10036");
    sysParam.put("method", method);
    sysParam.put("format", "json");
    sysParam.put("version", "V1.0");
    sysParam.put("timestamp", DateUtil.format(new Date(), DateUtil.DATE_TIME_PATTERN));
    return sysParam;
  }

  /**
   * 手动执行 M_HR_CUST_140000002-导入员工面授课程信息(增量). 2小时执行一次.
   */
//  @Scheduled(fixedDelay = 120 * 60 * 1000)
  @Scheduled(fixedDelay = 60 * 1000)
  public void handPushOfflineClassInfo() {
    String method = "M_HR_CUST_140000002";
    Map<String, String> sysParam = getSysParamMap(method);
    try{
      Map<String,Object> map = pccwCache.get(PUSH_KEY_OFFLINE_CLASS_DATA, HashMap.class);
      if(map!=null&&!map.isEmpty()){
        Integer type=(Integer)map.get("type");
        switch (type){
          case 1:
            Long startTime=(Long)map.get("startTime");
            Long endTime=(Long)map.get("endTime");

            String projectIdsStr = (String)map.get("projectIdsStr");
            List<String> projectIds = new ArrayList<>();

            if (projectIdsStr !=null && projectIdsStr.length() > 0){

              projectIds = Arrays.asList(projectIdsStr.split(","));
            }
            dealOfflineClassInfo(sysParam, method, startTime, endTime,null, projectIds);
            log.error("导入员工面授课程信息手动推送成功，time = {}",System.currentTimeMillis());
            break;
          case 3:
            log.error("测试导入员工面授课程信息手动推送正常，date={}",System.currentTimeMillis());
            break;
        }
      }
    }catch(Exception e){
      log.error("导入员工面授课程信息手动推送失败，error={}",e);
    }finally{
      //无论是否成功调用，必须清除redis
      pccwCache.clear(PUSH_KEY_OFFLINE_CLASS_DATA);
    }

  }

  /**
   * 手动执行 M_HR_CUST_140000003-导入员工参训信息. 2小时执行一次.
   */
//  @Scheduled(fixedDelay = 120 * 60 * 1000)
  @Scheduled(fixedDelay =  60 * 1000)
  public void handPushTraineeInfo() {
    String method = "M_HR_CUST_140000003";
    Map<String, String> sysParam = getSysParamMap(method);
    try{
      Map<String,Object> map = pccwCache.get(PUSH_KEY_TRAINEE_INFO, HashMap.class);
      if(map!=null&&!map.isEmpty()){
        Integer type=(Integer)map.get("type");
        switch (type){
          case 4:
            Long startTime=(Long)map.get("startTime");
            Long endTime=(Long)map.get("endTime");

            String projectIdsStr = (String)map.get("projectIdsStr");
            List<String> projectIds = new ArrayList<>();

            if (projectIdsStr !=null && projectIdsStr.length() > 0){

              projectIds = Arrays.asList(projectIdsStr.split(","));
            }
            dealTraineeInfo(sysParam, method, startTime, endTime,null, projectIds);
            log.error("导入员工参训信息手动推送成功，time = {}",System.currentTimeMillis());
            break;
          case 6:
            log.error("测试导入员工参训信息手动推送正常，date={}",System.currentTimeMillis());
            break;
        }
      }
    }catch(Exception e){
      log.error("导入员工参训信息手动推送失败，error={}",e);
    }finally{
      //无论是否成功调用，必须清除redis
      pccwCache.clear(PUSH_KEY_TRAINEE_INFO);
    }

  }


  /**
   * 导入员工面授课程信息.
   *  @param sysParam 公共入参
   * @param method   请求接口类型
   * @param start    开始时间
   * @param end      结束时间
   * @param linkId   上次处理失败的记录ID(默认和当前主键相同)
   * @param projectIds
   */
  private boolean dealOfflineClassInfo(Map<String, String> sysParam, String method, long start,
                                       long end, String linkId, List<String> projectIds) {
    //修改为1周的数据
    long week = 7L * 24L * 3600L * 1000L;
    //接口请求路径
    String path = "/hrd/sync/train/faceToFace";

    Integer totalCount = 0;

    Condition firstCondition = CLASS_OFFLINE_COURSE.TYPE.eq(1);
    firstCondition = firstCondition.and(CLASS_INFO.RETURN_DATE.add(week).between(start, end));

    if (projectIds != null && projectIds.size() > 0 ){
      firstCondition = firstCondition.and(PROJECT.ID.in(projectIds));
    }

    Condition finallCondition = firstCondition;
    // 获取所有有新增课程的省公司组织ID
    List<String> orgIds = projectDao
            .execute(cf -> cf.selectDistinct(PROJECT.ORGANIZATION_ID).from(PROJECT)
                    .leftJoin(CLASS_INFO)
                    .on(CLASS_INFO.PROJECT_ID.eq(PROJECT.ID))
                    .leftJoin(CLASS_OFFLINE_COURSE)
                    .on(CLASS_OFFLINE_COURSE.CLASS_ID.eq(CLASS_INFO.ID))
                    .where(finallCondition)
                    .fetch(PROJECT.ORGANIZATION_ID));
    Set<String> rootOrgIds = Sets.newHashSet();
    // 省公司信息
    Map<String, Organization> rootOrgMap = Maps.newHashMap();
    if (orgIds != null && orgIds.size() > 0) {

      //key:companyId 获取到path的第二个和第三个逗号之间的path参数
      Set<String> companyIdsSet = new HashSet<>();

      organizationDao.execute(x ->
              x.selectDistinct(ORGANIZATION.PATH)
                      .from(ORGANIZATION)
                      .where(ORGANIZATION.ID.in(orgIds)))
              .fetch(r -> {

                //获取到path的第二个和第三个逗号之间的path参数 和 ihr_code
                String companyId = this.extractBetweenSecondAndThirdComma(r.get(ORGANIZATION.PATH));

                if (companyId != null){
                  companyIdsSet.add(companyId);
                }
                return null;
              });

      companyIdsSet.add("1");
      companyIdsSet.add("10000001");

      //path,ihr_code
      List<Organization> rootOrgs = organizationDao.execute(on ->
              on.selectDistinct(ORGANIZATION.ID, ORGANIZATION.IHR_CODE, ORGANIZATION.CODE)
                      .from(ORGANIZATION)
                      .where(ORGANIZATION.ID.in(companyIdsSet))
                      .fetchInto(Organization.class));
      rootOrgs.forEach(x -> {
        String rootId = x.getId();
        rootOrgIds.add(rootId);
        rootOrgMap.put(rootId, x);
      });
    }
    int rootOrgSize = rootOrgIds.size();
    log.error("待处理导入员工面授课程信息,组织数量size={}", rootOrgSize);
    if (rootOrgSize > 0) {
      for (String rootOrg : rootOrgIds) {

        Integer oniceCount = 0;
        int page = 1;
        int pageSize = 50;
        //当k<1时退出循环
        int k = 1;
        //省公司编码
        Organization organization = rootOrgMap.get(rootOrg);
        String provinceCode = null;
        if (Objects.nonNull(organization)) {

          //获取人力发展系统组织映射编码
          // 如果当前省份编码不存在或者当前省份处于不可推送状态，跳过此次循环
          try {
            provinceCode = getHrMappingCode(organization.getId(),organization.getIhrCode());
          }catch (Exception e){
            log.error("面授课程数据推送状态：org:{},ihrCode:{},msg:{}",organization.getId(),organization.getIhrCode(),e.getMessage());
            continue;
          }
        }
        log.error("面授课程数据推送状态：org:{},ihrCode:{}",organization.getId(),organization.getIhrCode());
        Condition condition = ORGANIZATION.PATH.like("1,10000001," + rootOrg + "%");
        if (Objects.equals(rootOrg, "1")) {
          condition = ORGANIZATION.ID.eq("1");
        }else if (Objects.equals(rootOrg, "10000001")) {
        condition = ORGANIZATION.ID.eq("10000001");
      }

        if (projectIds != null && projectIds.size() > 0 ){
          condition.and(PROJECT.ID.in(projectIds));
        }

        while (k >= 1) {
          final int currentPage = (page - 1) * pageSize;
          //记录总数
          int size = 0;
          Condition finalCondition = condition;
          List<OfflineClassInfo> offlineClassInfos = projectDao
                  .execute(cf -> cf.selectDistinct(
                          PROJECT.ID,
                          PROJECT.NAME,
                          PROJECT.CODE,
                          PROJECT.TARGET,
                          PROJECT.DAYS,
                          PROJECT.OBJECT,
                          PROJECT.CONTACT_PHONE,
                          CLASS_OFFLINE_COURSE.ID,
                          CLASS_OFFLINE_COURSE.NAME,
                          CLASS_OFFLINE_COURSE.TEACHER_NAME,
                          CLASS_OFFLINE_COURSE.TYPE,
                          CLASS_OFFLINE_COURSE.COURSE_DATE,
                          CLASS_OFFLINE_COURSE.START_TIME,
                          CLASS_OFFLINE_COURSE.END_TIME,
                          CONFIGURATION_VALUE.NAME,
                          CLASS_INFO.ARRIVE_DATE,
                          CLASS_INFO.RETURN_DATE,
                          CLASS_INFO.IS_OUTSIDE,
                          ORGANIZATION.NAME,
                          ORGANIZATION.CODE,
                          ORGANIZATION.IHR_CODE,
                          MEMBER.FULL_NAME,
                          MEMBER.NAME
                  )
                          .from(PROJECT).leftJoin(CLASS_INFO)
                          .on(CLASS_INFO.PROJECT_ID.eq(PROJECT.ID))
                          .leftJoin(CLASS_OFFLINE_COURSE)
                          .on(CLASS_OFFLINE_COURSE.CLASS_ID.eq(CLASS_INFO.ID))
                          .leftJoin(CONFIGURATION_VALUE)
                          .on(CONFIGURATION_VALUE.ID.eq(PROJECT.TYPE_ID))
                          .leftJoin(ORGANIZATION).on(ORGANIZATION.ID.eq(PROJECT.ORGANIZATION_ID))
                          .leftJoin(MEMBER)
                          .on(MEMBER.ID.eq(PROJECT.CONTACT_MEMBER_ID))
                          .where(CLASS_OFFLINE_COURSE.TYPE.eq(1)
                                  .and(CLASS_INFO.RETURN_DATE.add(week).between(start, end)))
                          .and(CLASS_OFFLINE_COURSE.DELETE_FLAG.eq(ClassOfflineCourse.DELETE_FALSE))
                          .and(finalCondition)
                          .limit(currentPage, pageSize + 1).fetch().map(x -> {
                            OfflineClassInfo offlineClassInfo = new OfflineClassInfo();
                            offlineClassInfo.setProjectId(x.get(PROJECT.ID));
                            offlineClassInfo.setProjectName(x.get(PROJECT.NAME));
                            //项目mis编码
                            offlineClassInfo.setProjectCode(x.get(PROJECT.CODE));
                            //主办单位
                            offlineClassInfo.setSponsorOrgName(x.get(ORGANIZATION.NAME));
                            //主办单位编码
                            offlineClassInfo.setSponsorOrgCode(x.get(ORGANIZATION.IHR_CODE));

                            // 培训目标,培训目的
                            offlineClassInfo.setTrainTarget(x.get(PROJECT.TARGET));
                            //培训对象
                            offlineClassInfo.setTrainOrientation(x.get(PROJECT.OBJECT));
                            // 开始时间
                            offlineClassInfo.setStartDate(DateUtil
                                    .format(new Date(x.get(CLASS_INFO.ARRIVE_DATE)),
                                            DateUtil.DATE_TIME_PATTERN));
                            // 结束时间
                            offlineClassInfo.setEndDate(DateUtil
                                    .format(new Date(x.get(CLASS_INFO.RETURN_DATE)),
                                            DateUtil.DATE_TIME_PATTERN));
                            // 培训天数,培训时长
                            offlineClassInfo.setTrainDays(x.get(PROJECT.DAYS));
                            // 培训类型
                            offlineClassInfo.setTrainType(x.get(CONFIGURATION_VALUE.NAME));
/*                            //培训形式  CLASS_INFO.isOutSide  0-校内培训 1-校外培训 3-在线学习
                            offlineClassInfo.setTrainType(this.getTrainType(x.get(CLASS_INFO.IS_OUTSIDE)));*/
                            //培训联系人  t_member.f_full_name
                            offlineClassInfo.setContact(x.get(MEMBER.NAME));
                            //联系人电话 t_project.f_contact_phone
                            offlineClassInfo.setContactPhone(x.get(PROJECT.CONTACT_PHONE));
                            //是否有考试 默认否
                            offlineClassInfo.setExamFlag("N");
                            //是否有考试 默认否
                            offlineClassInfo.setExamID("");
                            //是否有考试 默认否
                            offlineClassInfo.setExamName("");
                            // 课程id
                            offlineClassInfo.setCourseId(x.get(CLASS_OFFLINE_COURSE.ID));
                            // 课程名称
                            offlineClassInfo.setCourseName(x.get(CLASS_OFFLINE_COURSE.NAME));
                            //课程类型
                            offlineClassInfo.setCourseType(getCourseType(x.get(CLASS_OFFLINE_COURSE.TYPE)));
                            //讲师姓名
                            offlineClassInfo.setTrainerName(x.get(CLASS_OFFLINE_COURSE.TEACHER_NAME));
                            // 课程开始结束时间
                            Long timestamp = x.get(CLASS_OFFLINE_COURSE.COURSE_DATE);
                            String startTimeStr = x.get(CLASS_OFFLINE_COURSE.START_TIME);
                            String endTimeStr = x.get(CLASS_OFFLINE_COURSE.END_TIME);
                            if (timestamp != null && startTimeStr != null  &&  endTimeStr != null) {
                              offlineClassInfo.setCoStartDate(getDateStr(timestamp,startTimeStr,endTimeStr)[0]);
                              offlineClassInfo.setCoEndDate(getDateStr(timestamp,startTimeStr,endTimeStr)[1]);
                              //培训时长 小时
                              offlineClassInfo.setTrainHours(calculateHoursDifference(startTimeStr,endTimeStr));
                            }
                            //操作状态新增
                            offlineClassInfo.setOperateSatus("A");
                            return offlineClassInfo;
                          }));
          if (Objects.nonNull(offlineClassInfos) && offlineClassInfos.size() > 0) {
            size = offlineClassInfos.size();
            // 是否还有下一页记录
            if (size > pageSize) {
              offlineClassInfos.remove(size - 1);
            }
            OfflineClassInfoDTO offlineClassInfoDTO = new OfflineClassInfoDTO();
            offlineClassInfoDTO.setCurrentPage(page);
            offlineClassInfoDTO.setData(offlineClassInfos);
            offlineClassInfoDTO.setPageSize(pageSize);
            //省公司code
            offlineClassInfoDTO.setProvinceCode(provinceCode);
            //表示是数据来源为网大
            JSONObject jsonObject=new JSONObject();
            jsonObject.put("source","OU");
            offlineClassInfoDTO.setInputExt(JSON.toJSONString(jsonObject));
            // 封装业务数据
            String busiParam = JSONObject.toJSONString(offlineClassInfoDTO);
            PccwResult pccwResult = new PccwResult();
            pccwResult.forInsert();
            //默认失败处理linkId与主键相同
            pccwResult.setLinkId(pccwResult.getId());
            boolean linkIdIsBlank = StringUtils.isNotBlank(linkId);
            if (linkIdIsBlank) {
              pccwResult.setLinkId(linkId);
            }
            pccwResult.setUpdateTime(System.currentTimeMillis());
            pccwResult.setMethod(method);
            //处理状态码 0:失败 1:成功 2:失败记录本次处理失败,下次待处理
            int status = linkIdIsBlank ? 2 : 0;
            try {
              // 调用外部服务接口
              log.error("dealOfflineClassInfo request param ={}", busiParam);
              String responseStr = AIESBClient
                      .rest(sysParam, busiParam, path, AIESBConstants.METHOD.POST);
              log.error("dealOfflineClassInfo response = {}", responseStr);
              pccwResultDao.insert(dealResponse(pccwResult, responseStr, status));
            } catch (Exception e) {
              log.error("导入员工面授课程信息ERROR", e);
              pccwResult.setStatus(status);
              pccwResult.setInstanceId("-1");
              pccwResult.setRespCode("-1");
              pccwResult.setRespDesc("导入员工面授课程信息ERROR");
              pccwResultDao.insert(pccwResult);
              return false;
            }
          }

          if (size > pageSize) {
            totalCount += size - 1;
            oniceCount += size - 1;
          }else {
            totalCount += size;
            oniceCount += size;
          }

          // 多页记录
          if (size > pageSize) {
            page++;
          } else {
            k = 0;
            page = 1;
          }
        }
        log.error("dealOfflineClassInfo oniceCount  = {}", oniceCount);
      }
    }
    log.error("dealOfflineClassInfo totalCount  = {}", totalCount);
    return true;
  }

  /**
   * 获取培训形式  0-校内培训 1-校外培训 3-在线学习
   * @return
   */
  private String getTrainType(Integer outSideType) {

    String trainType = "";

    if ("0".equals(outSideType)){

      trainType = OfflineClassInfo.TRAIN_TYPE_IN_SCHOOL;
    }else if ("1".equals(outSideType)){

      trainType = OfflineClassInfo.TRAIN_TYPE_OUTSIDE_SCHOOL;
    }else if ("2".equals(outSideType)){

      trainType = OfflineClassInfo.TRAIN_TYPE_ONLINE;
    }

    return trainType;
  }

  /**
   * 导入员工参训信息.
   *  @param sysParam 公共入参
   * @param method   请求接口类型
   * @param start    开始时间
   * @param end      结束时间
   * @param linkId   上次处理失败的记录ID(默认和当前主键相同)
   * @param projectIds
   */
  private boolean dealTraineeInfo(Map<String, String> sysParam, String method, long start,
                                  long end, String linkId, List<String> projectIds) {
    //一周後同步數據
    long month = 7L * 24L * 3600L * 1000L;
    //接口请求路径
    String path = "/hrd/sync/train/traineeInfo";

    Integer totalCount = 0;

    Condition firstCondition = CLASS_INFO.RETURN_DATE.add(month).between(start, end);

    if (projectIds != null && projectIds.size() > 0 ){
      firstCondition = firstCondition.and(PROJECT.ID.in(projectIds));
    }

    Condition finallCondition = firstCondition;

    // 获取所有有新增课程的省公司组织ID
    List<String> orgIds = projectDao
            .execute(cf -> cf.selectDistinct(PROJECT.ORGANIZATION_ID).from(PROJECT)
                    .leftJoin(CLASS_INFO).on(CLASS_INFO.PROJECT_ID.eq(PROJECT.ID))
                    .where(finallCondition)
                    .fetch(PROJECT.ORGANIZATION_ID));
    Set<String> rootOrgIds = Sets.newHashSet();
    // 省公司信息
    Map<String, Organization> rootOrgMap = Maps.newHashMap();
    if (orgIds != null && orgIds.size() > 0) {

      //key:companyId 获取到path的第二个和第三个逗号之间的path参数
      Set<String> companyIdsSet = new HashSet<>();

      organizationDao.execute(x ->
              x.selectDistinct(ORGANIZATION.PATH)
                      .from(ORGANIZATION)
                      .where(ORGANIZATION.ID.in(orgIds)))
              .fetch(r -> {

                  //获取到path的第二个和第三个逗号之间的path参数 和 ihr_code
                  String companyId = this.extractBetweenSecondAndThirdComma(r.get(ORGANIZATION.PATH));

                  if (companyId != null){
                    companyIdsSet.add(companyId);
                  }
                  return null;
              });

      companyIdsSet.add("1");
      companyIdsSet.add("10000001");

      //path,ihr_code
      List<Organization> rootOrgs = organizationDao.execute(on ->
              on.selectDistinct(ORGANIZATION.ID, ORGANIZATION.IHR_CODE, ORGANIZATION.CODE)
                      .from(ORGANIZATION)
                      .where(ORGANIZATION.ID.in(companyIdsSet))
                      .fetchInto(Organization.class));
      rootOrgs.forEach(x -> {
        String rootId = x.getId();
        rootOrgIds.add(rootId);
        rootOrgMap.put(rootId, x);
      });
    }
    int rootOrgSize = rootOrgIds.size();
    log.error("待处理导入员工参训信息,组织数量size={}", rootOrgSize);
    if (rootOrgSize > 0) {
      for (String rootOrg : rootOrgIds) {
        Integer oniceCount = 0;
        int page = 1;
        int pageSize = 50;
        //当k<1时退出循环
        int k = 1;
        //省公司编码
        Organization organization = rootOrgMap.get(rootOrg);
        String provinceCode = null;
        if (Objects.nonNull(organization)) {

          //根据机构查询人力发展系统省份编码
          // 如果当前省份编码不存在或者当前省份处于不可推送状态，跳过此次循环
          try {
            provinceCode = getHrMappingCode(organization.getId(), organization.getIhrCode());
          }catch (Exception e){
            log.error("导入员工参训信息推送状态：org:{},ihrCode:{},msg:{}",organization.getId(),organization.getIhrCode(),e.getMessage());
            continue;
          }
        }
        log.error("导入员工参训信息推送状态：org:{},ihrCode:{}",organization.getId(),organization.getIhrCode());
        // 组织信息判断,为移动节点下或省公司下
        Condition condition = ORGANIZATION.PATH.like("1,10000001," + rootOrg + "%");
        if (Objects.equals(rootOrg, "1")) {
          condition = ORGANIZATION.ID.eq("1");
        }else if (Objects.equals(rootOrg, "10000001")) {
          condition = ORGANIZATION.ID.eq("10000001");
        }

        if (projectIds != null && projectIds.size() > 0 ){
          condition.and(PROJECT.ID.in(projectIds));
        }

        while (k >= 1) {
          final int currentPage = (page - 1) * pageSize;
          //记录总数
          int size = 0;
          Condition finalCondition = condition;
          List<Trainee> trainees = projectDao
                  .execute(cf -> cf.selectDistinct(
                          PROJECT.ID,
                          PROJECT.NAME,
                          MEMBER.NAME,
                          MEMBER.IHR_CODE,
                          MEMBER.FULL_NAME,
                          CLASS_INFO.ARRIVE_DATE,
                          CLASS_INFO.RETURN_DATE
                  )
                          .from(PROJECT)
                          .leftJoin(CLASS_INFO)
                          .on(CLASS_INFO.PROJECT_ID.eq(PROJECT.ID))
                          .innerJoin(TRAINEE)
                          .on(TRAINEE.CLASS_ID.eq(CLASS_INFO.ID))
                          .leftJoin(MEMBER)
                          .on(MEMBER.ID.eq(TRAINEE.MEMBER_ID))
                          .leftJoin(ORGANIZATION).on(ORGANIZATION.ID.eq(PROJECT.ORGANIZATION_ID))
                          .where(CLASS_INFO.RETURN_DATE.add(month).between(start, end))
                          .and(finalCondition)
                          .limit(currentPage, pageSize + 1).fetch().map(x -> {
                            Trainee trainee = new Trainee();
                            // 项目ID
                            trainee.setProjectId(x.get(PROJECT.ID));
                            // 项目名称
                            trainee.setProjectName(x.get(PROJECT.NAME));
                            // 员工编号
                            String ihrCode = x.get(MEMBER.IHR_CODE, String.class);
                            if (StringUtils.isBlank(ihrCode)) {
                              ihrCode = x.get(MEMBER.NAME, String.class);
                            }
                            trainee.setEmpNumber(ihrCode);
                            // 员工姓名
                            trainee.setEmpName(x.get(MEMBER.FULL_NAME));
                            // 是否考试
                            trainee.setExamFlag("N");
                            // 考试id
                            trainee.setExamId("");
                            // 考试分数
                            trainee.setExamScore("");

                            return trainee;
                          }));
          if (Objects.nonNull(trainees) && trainees.size() > 0) {
            size = trainees.size();
            // 是否还有下一页记录
            if (size > pageSize) {
              trainees.remove(size - 1);
            }
            TraineeDTO traineeDTO = new TraineeDTO();
            traineeDTO.setCurrentPage(page);
            traineeDTO.setData(trainees);
            traineeDTO.setPageSize(pageSize);
            traineeDTO.setProvinceCode(provinceCode);
            //表示是数据来源为网大
            JSONObject jsonObject=new JSONObject();
            jsonObject.put("source","OU");
            traineeDTO.setInputExt(JSON.toJSONString(jsonObject));
            // 封装业务数据
            String busiParam = JSONObject.toJSONString(traineeDTO);
            PccwResult pccwResult = new PccwResult();
            pccwResult.forInsert();
            //默认失败处理linkId与主键相同
            pccwResult.setLinkId(pccwResult.getId());
            boolean linkIdIsBlank = StringUtils.isNotBlank(linkId);
            if (linkIdIsBlank) {
              pccwResult.setLinkId(linkId);
            }
            pccwResult.setUpdateTime(System.currentTimeMillis());
            pccwResult.setMethod(method);
            //处理状态码 0:失败 1:成功 2:失败记录本次处理失败,下次待处理
            int status = linkIdIsBlank ? 2 : 0;
            try {
              // 调用外部服务接口
              log.error("dealTraineeInfo request param = {}", busiParam);
              String responseStr = AIESBClient
                      .rest(sysParam, busiParam, path, AIESBConstants.METHOD.POST);
              log.error("dealTraineeInfo response = {}", responseStr);
              pccwResultDao.insert(dealResponse(pccwResult, responseStr, status));
            } catch (Exception e) {
              log.error("导入员工参训信息ERROR", e);
              pccwResult.setStatus(status);
              pccwResult.setInstanceId("-1");
              pccwResult.setRespCode("-1");
              pccwResult.setRespDesc("导入员工参训信息ERROR");
              pccwResultDao.insert(pccwResult);
              return false;
            }
          }

          if (size > pageSize) {
            totalCount += size - 1;
            oniceCount += size - 1;
          }else {
            totalCount += size;
            oniceCount += size;
          }

          // 多页记录
          if (size > pageSize) {
            page++;
          } else {
            k = 0;
            page = 1;
          }
        }
        log.error("dealOfflineClassInfo oniceCount  = {}", oniceCount);
      }
    }
    log.error("dealOfflineClassInfo totalCount  = {}", totalCount);
    return true;
  }

  private Integer calculateHoursDifference(String startTimeStr, String endTimeStr) {
    if (startTimeStr == null || startTimeStr == null) {
      return 0;
    }
    double hours=0;
    SimpleDateFormat sdf = new SimpleDateFormat("HH:mm");
    try {
      Date startDate = sdf.parse(startTimeStr);
      Date endDate = sdf.parse(endTimeStr);
      long diffInMillis = endDate.getTime() - startDate.getTime();
      hours = (double) diffInMillis / (1000 * 60 * 60);
    } catch (ParseException e) {
      e.printStackTrace();
    }
    return (int) Math.round(hours);
  }

  /**
   * 课程类型（1面授  2观看录像 3直播 4其他）
   * @return
   */
  private String getCourseType(Integer type) {

    String trainType = OfflineClassInfo.TYPE_OTHER;

    if ("1".equals(type)){

      trainType = OfflineClassInfo.TYPE_FACE;
    }else if ("2".equals(type)){

      trainType = OfflineClassInfo.TYPE_VIDEO;
    }else if ("3".equals(type)){

      trainType = OfflineClassInfo.TYPE_LIVE;
    }

    return trainType;
  }

  private String[] getDateStr(Long timestamp, String startTimeStr, String endTimeStr) {
    String[] result = new String[2];
    try {
      Date date = new Date(timestamp);
      // 创建 Calendar 实例
      Calendar calendar = Calendar.getInstance();
      calendar.setTime(date);
      // 解析开始和结束时间字符串
      SimpleDateFormat timeFormat = new SimpleDateFormat("HH:mm");
      Date startTime = timeFormat.parse(startTimeStr);
      Date endTime = timeFormat.parse(endTimeStr);

      // 设置开始和结束时间的年月日
      Calendar startCal = Calendar.getInstance();
      startCal.setTime(startTime);
      startCal.set(Calendar.YEAR, calendar.get(Calendar.YEAR));
      startCal.set(Calendar.MONTH, calendar.get(Calendar.MONTH));
      startCal.set(Calendar.DAY_OF_MONTH, calendar.get(Calendar.DAY_OF_MONTH));

      Calendar endCal = Calendar.getInstance();
      endCal.setTime(endTime);
      endCal.set(Calendar.YEAR, calendar.get(Calendar.YEAR));
      endCal.set(Calendar.MONTH, calendar.get(Calendar.MONTH));
      endCal.set(Calendar.DAY_OF_MONTH, calendar.get(Calendar.DAY_OF_MONTH));

      // 格式化输出开始和结束时间
      SimpleDateFormat outputFormat = new SimpleDateFormat("yyyyMMddHHmmss");
      String formattedStartTime = outputFormat.format(startCal.getTime());
      String formattedEndTime = outputFormat.format(endCal.getTime());
      long diffInMillis = endCal.getTime().getTime() - startCal.getTime().getTime();
      result[0] = formattedStartTime;
      result[1] = formattedEndTime;
    } catch (ParseException e) {
      e.printStackTrace();
    }
    return result;
  }

  /**
   * 处理接口响应.
   */
  private PccwResult dealResponse(PccwResult pccwResult, String responseStr, int status) {
    String success = "00000";
    if (StringUtils.isNotBlank(responseStr)) {
      JSONObject jsonObject = JSONObject.parseObject(responseStr);
      String instanceId = jsonObject.getString("instanceId");
      String respCode = jsonObject.getString("respCode");
      String respDesc = jsonObject.getString("respDesc");
      pccwResult.setInstanceId(instanceId);
      pccwResult.setRespCode(respCode);
//      pccwResult.setRespDesc(respDesc);
      //成功
      if (Objects.equals(success, respCode)) {
        status = 1;
      }
      JSONObject result = jsonObject.getJSONObject("result");
      if (Objects.nonNull(result)) {
        String statusCode = result.getString("statusCode");
        String errReason = result.getString("errReason");
        int totalRecord = result.getIntValue("totalRecord");
        pccwResult.setStatusCode(statusCode);
//        pccwResult.setErrReason(errReason);
        pccwResult.setTotalRecord(totalRecord);
      }
      pccwResult.setStatus(status);
      return pccwResult;
    }
    return pccwResult;
  }

  /**
   * 获取人力发展系统组织映射编码,如果查不到对应hr_province_code的话，provinceCode字段默认给他们传组织表的ihrcode
   * @param orgId
   * @param provinceCode
   * @return
   */
  private String getHrMappingCode(String orgId, String provinceCode){

    Optional<OrganizationHrMapping> hrMappingOptional = organizationHrMappingService.getOptional(orgId);

    if(hrMappingOptional.isPresent()){
      provinceCode = hrMappingOptional.get().getHrProvinceCode();
    }

    return provinceCode;
  }

  /**
   * 专门用于提取第二个和第三个逗号之间的字符串
   */
  private  String extractBetweenSecondAndThirdComma(String input) {
    return extractBetweenCommas(input, 2, 3);
  }


  /**
   * 提取指定位置逗号之间的字符串（从0开始计数）
   * @param input 输入字符串
   * @param startCommaIndex 起始逗号索引
   * @param endCommaIndex 结束逗号索引
   * @return 提取的子字符串
   */
  private  String extractBetweenCommas(String input, int startCommaIndex, int endCommaIndex) {

    if (input == null || startCommaIndex < 0 || endCommaIndex <= startCommaIndex) {
      return null;
    }

    int[] commaPositions = findCommaPositions(input);

    // 校验是否有足够的逗号
    if (commaPositions.length < endCommaIndex + 1) {
      return null;
    }

    int start = (startCommaIndex == 0) ? 0 : commaPositions[startCommaIndex - 1] + 1;
    int end = commaPositions[endCommaIndex];

    return input.substring(start, end);
  }

  private static int[] findCommaPositions(String input) {
    if (input == null || input.isEmpty()) {
      return new int[0];
    }

    java.util.List<Integer> positions = new java.util.ArrayList<>();
    for (int i = 0; i < input.length(); i++) {
      if (input.charAt(i) == ',') {
        positions.add(i);
      }
    }

    return positions.stream().mapToInt(Integer::intValue).toArray();
  }


}